import { react } from 'eslint-config-ali';

export default [
  ...react,
  {
    rules: {
      /** js */
      'no-nested-ternary': 'off',
      'max-len': 'off',
      'no-useless-catch': 'off',
      'no-mixed-operators': 'off',
      'react/display-name': 'off',
      "no-confusing-arrow": "off",
      "@stylistic/max-len": ["warn", {
        "code": 200
      }],
      /** ts */
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/consistent-type-assertions': 'off',
      '@typescript-eslint/no-shadow': 'warn',
      /** react */
      'react/jsx-closing-tag-location': 'off',
      'react/no-unused-prop-types': 'off',
    },
  },
];
