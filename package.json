{"private": true, "author": "liulingbo <<EMAIL>>", "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev", "prepare": "husky"}, "dependencies": {"@ali/aes-tracker": "^3.3.13", "@ali/aes-tracker-plugin-api": "^3.1.10", "@ali/aes-tracker-plugin-blank": "^3.0.2", "@ali/aes-tracker-plugin-event": "^3.0.0", "@ali/aes-tracker-plugin-eventTiming": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-longtask": "^3.0.1", "@ali/aes-tracker-plugin-perf": "^3.1.3", "@ali/aes-tracker-plugin-pv": "^3.0.6", "@ali/aes-tracker-plugin-resourceError": "^3.0.4", "@ant-design/x": "^1.5.0", "@types/uuid": "^10.0.0", "ahooks": "^3.9.0", "antd": "^5.26.6", "antd-style": "^3.7.1", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-emoji": "^5.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "umi": "^4.4.11", "uuid": "^11.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/mockjs": "^1.0.10", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/react-syntax-highlighter": "^15.5.13", "@umijs/lint": "^4.4.11", "@umijs/plugins": "^4.4.11", "classnames": "^2.5.1", "commitlint-config-ali": "^1.1.0", "eslint": "^9.0.0", "eslint-config-ali": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "mockjs": "^1.1.0", "prettier": "^3.6.2", "stylelint": "^14", "tailwindcss": "^3", "typescript": "^5.0.3"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": ["eslint --fix --quiet"], "src/**/*.{scss,less,css}": ["prettier --write"]}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1"}, "tnpm": {"mode": "yarn", "lockfile": "enable"}, "repository": "**************************:InnoArchClub/ai-pc-fe.git"}