---
globs: "*.css,*.less,*.styles.ts"
---

# Styling Guidelines

## Styling Approaches
This project uses two main styling approaches:

### 1. antd-style (CSS-in-JS) - Preferred for new components
```typescript
import { createStyles } from 'antd-style';

export const useComponentStyles = createStyles(({ token, css }) => ({
  wrapper: css`
    padding: ${token.padding}px;
    background: ${token.colorBgContainer};
    border-radius: ${token.borderRadius}px;
  `,
  content: css`
    color: ${token.colorText};
    font-size: ${token.fontSize}px;
  `,
}));
```

### 2. LESS Modules - For existing components
```less
.container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  
  .header {
    margin-bottom: 12px;
    font-weight: 600;
  }
  
  .content {
    flex: 1;
    overflow: auto;
  }
}
```

## Style File Organization
- Component styles: `ComponentName/index.styles.ts` or `ComponentName/index.less`
- Global styles: [src/global.css](mdc:src/global.css)
- Layout styles: Use antd-style for responsive layouts

## Design Token Usage
When using antd-style, always leverage Ant Design tokens:
- `token.colorPrimary` - Primary brand color
- `token.colorBgContainer` - Container background
- `token.padding` - Standard padding values
- `token.borderRadius` - Consistent border radius
- `token.fontSize` - Typography scale

## CSS Class Naming
- Use camelCase for CSS-in-JS style objects
- Use kebab-case for LESS class names
- Be descriptive: `.navigationWrapper`, `.chatMessageList`
- Avoid generic names: `.container`, `.content` (unless truly generic)

## Responsive Design
```typescript
const useResponsiveStyles = createStyles(({ token, css }) => ({
  container: css`
    padding: ${token.padding}px;
    
    @media (max-width: 768px) {
      padding: ${token.paddingSM}px;
    }
  `,
}));
```

## Component-Specific Styling
- Each component should have its own style file
- Avoid global style pollution
- Use CSS modules or CSS-in-JS for style isolation
- Leverage Ant Design's built-in styling for standard components

## Icon and Asset Handling
- Use the centralized icon system from [src/constant/icon.ts](mdc:src/constant/icon.ts)
- Store static assets in [src/assets/](mdc:src/assets/)
- Prefer SVG icons for scalability
- Use CDN URLs for external resources when appropriate
