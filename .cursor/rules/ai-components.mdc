---
description: "Guidelines for working with AI-related components and Ant Design X"
---

# AI Component Development Guidelines

## Ant Design X Integration
This project heavily uses Ant Design X for AI interfaces. Key components include:

### Chat Interface Components
- `Bubble` - For message display with role-based styling
- `Sender` - Input component with file upload and speech capabilities  
- `useXChat` - Hook for chat state management
- `useXAgent` - Hook for AI agent integration
- `Prompts` - Pre-defined prompt suggestions
- `Welcome` - Welcome screen component
- `Attachments` - File upload handling

### Usage Example (from [src/pages/chat/index.tsx](mdc:src/pages/chat/index.tsx))
```typescript
import { Bubble, Sender, useXChat, useXAgent } from '@ant-design/x';

const [agent] = useXAgent<BubbleDataType>({
  baseURL: "your-api-endpoint",
  dangerouslyApiKey: "your-api-key",
});

const { onRequest, messages } = useXChat({
  agent,
  requestFallback: (_, { error }) => ({
    content: error.name === "AbortError" ? "Request is aborted" : "Request failed",
    role: "assistant",
  }),
  transformMessage: (info) => {
    // Handle streaming responses
    return { content: processedContent, role: "assistant" };
  },
});
```

## AI Component Patterns

### Message Structure
```typescript
type BubbleDataType = {
  role: 'user' | 'assistant';
  content: string;
  status?: 'loading' | 'complete';
};
```

### Streaming Response Handling
- Always implement `transformMessage` for processing streaming data
- Handle loading states with proper UI feedback
- Implement abort functionality for canceling requests
- Use typing effects for enhanced user experience

### Error Handling for AI Components
- Provide fallback messages for failed requests
- Handle network timeouts gracefully  
- Show appropriate loading states
- Implement retry mechanisms where appropriate

## XProvider Configuration
Wrap your app with XProvider in the main layout:
```typescript
import { XProvider } from '@ant-design/x';

export default function Layout() {
  return (
    <XProvider>
      {/* Your app components */}
    </XProvider>
  );
}
```

## AI-Specific UI Patterns
- Use `Bubble.List` for message threads with role-based styling
- Implement `Prompts` for suggested actions/questions
- Add file attachment support using `Attachments` component
- Include speech-to-text functionality in `Sender` components
- Provide action buttons (copy, like, retry) for assistant messages

## Performance Considerations
- Implement proper message pagination for long conversations
- Use virtualization for large message lists
- Optimize re-renders with proper React.memo usage
- Handle large file uploads with progress indicators

## Accessibility
- Ensure proper ARIA labels for AI components
- Support keyboard navigation in chat interfaces
- Provide screen reader friendly message announcements
- Include alternative text for AI-generated content
