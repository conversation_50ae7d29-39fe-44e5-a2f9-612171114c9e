---
globs: '*.tsx,*.ts'
description: 'Guidelines for general component development prioritizing Ant Design components'
---

# Component Development Guidelines

## Core Principle: Ant Design First

**Always prioritize using Ant Design components over custom implementations**. Ant Design provides a comprehensive, well-tested component library that ensures consistency, accessibility, and maintainability.

## When to Use Ant Design Components

### ✅ Preferred Approach

- Use `Table` instead of custom list implementations
- Use `Form` and form fields instead of custom form handling
- Use `Modal`, `Drawer` for overlays instead of custom implementations
- Use `Button`, `Input`, `Select` for basic UI elements
- Use `Layout`, `Grid` system for page structure
- Use `Menu`, `Breadcrumb` for navigation
- Use `Card`, `List` for content display
- Use `DatePicker`, `TimePicker` for date/time selection

### Example: Table Implementation

```typescript
// ✅ GOOD: Using Ant Design Table
import { Table, Tag, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table/interface';

const FileList: React.FC = () => {
  const columns: ColumnsType<FileData> = [
    {
      title: '文件名',
      dataIndex: 'name',
      render: (text, record) => (
        <Space>
          <Image src={record.icon} preview={false} />
          <span>{text}</span>
          {record.isAiGenerated && <Tag>AI生成</Tag>}
        </Space>
      ),
    },
    // more columns...
  ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowSelection={rowSelection}
      pagination={paginationConfig}
    />
  );
};

// ❌ AVOID: Custom list implementation
const CustomFileList: React.FC = () => {
  return (
    <div>
      {items.map(item => (
        <div key={item.id} className="custom-row">
          {/* Custom implementation */}
        </div>
      ))}
    </div>
  );
};
```

## Essential Ant Design Components

### Data Display

- **Table**: For data grids with sorting, filtering, selection
- **List**: For simple item lists
- **Card**: For content containers
- **Descriptions**: For key-value pairs
- **Image**: For image display with preview
- **Avatar**: For user/entity representation
- **Tag**: For labels and status indicators

### Data Entry

- **Form**: Form container with validation
- **Input**: Text input with various types
- **Select**: Dropdown selection
- **DatePicker**: Date and time selection
- **Upload**: File upload with drag-and-drop
- **Checkbox**, **Radio**: Selection controls
- **Switch**: Toggle controls

### Navigation

- **Menu**: Navigation menus
- **Breadcrumb**: Navigation path
- **Pagination**: Page navigation
- **Tabs**: Tab navigation
- **Steps**: Step-by-step navigation

### Feedback

- **Modal**: Dialog boxes
- **Drawer**: Slide-out panels
- **Notification**: Toast notifications
- **Message**: Global messages
- **Spin**: Loading indicators
- **Progress**: Progress indicators

## Component Development Standards

### 1. TypeScript Interfaces

Always define proper TypeScript interfaces for props:

```typescript
interface ComponentProps {
  data: DataType[];
  loading?: boolean;
  onSelect?: (item: DataType) => void;
  className?: string;
}

const Component: React.FC<ComponentProps> = ({ data, loading, onSelect, className }) => {
  // Implementation
};
```

### 2. Styling with antd-style

Use `antd-style` for component styling to maintain theme consistency:

```typescript
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  container: {
    padding: token.padding,
    background: token.colorBgContainer,
    borderRadius: token.borderRadius,
  },
  customElement: {
    color: token.colorPrimary,
    fontSize: token.fontSize,
  },
}));
```

### 3. Component Structure

Follow this standard component structure:

```typescript
import React from 'react';
import { ComponentName } from 'antd';
import { createStyles } from 'antd-style';

// Interfaces
interface Props {
  // prop definitions
}

// Styles
const useStyles = createStyles(({ token }) => ({
  // styles
}));

// Component
const MyComponent: React.FC<Props> = ({ prop1, prop2 }) => {
  const { styles } = useStyles();

  // Component logic

  return (
    <div className={styles.container}>
      <ComponentName>
        {/* Implementation */}
      </ComponentName>
    </div>
  );
};

export default MyComponent;
```

## Best Practices

### ✅ Do's

- **Extend Ant Design components** instead of creating from scratch
- **Use Ant Design's built-in props** for common functionality
- **Leverage Ant Design's theme tokens** in custom styles
- **Use Form.Item** for all form fields to get validation and layout
- **Implement proper loading and error states** using Ant Design feedback components
- **Use Space component** for consistent spacing between elements

### ❌ Don'ts

- **Don't reinvent basic UI components** that Ant Design already provides
- **Don't ignore Ant Design's design tokens** when creating custom styles
- **Don't mix multiple UI libraries** unless absolutely necessary
- **Don't bypass Form validation** by handling validation manually
- **Don't create custom modals/drawers** when Ant Design's are sufficient

## Custom Component Guidelines

### When Custom Components Are Needed

Only create custom components when:

- Ant Design doesn't provide the specific functionality
- You need to combine multiple Ant Design components into a reusable pattern
- You're creating domain-specific business components

### Custom Component Pattern

```typescript
// ✅ GOOD: Custom component built on Ant Design
const FileUploadCard: React.FC<Props> = ({ onUpload, accept }) => {
  return (
    <Card className={styles.uploadCard}>
      <Upload.Dragger
        accept={accept}
        onChange={onUpload}
        showUploadList={false}
      >
        <InboxOutlined className={styles.uploadIcon} />
        <Typography.Text>点击或拖拽文件到此区域上传</Typography.Text>
      </Upload.Dragger>
    </Card>
  );
};
```

## Performance Considerations

- Use `Table` pagination for large datasets
- Implement `List` virtualization for very long lists
- Use `Image` component's lazy loading for image-heavy interfaces
- Leverage `Form` optimization with proper field names and dependencies

## Accessibility

- Ant Design components come with built-in accessibility features
- Always provide proper `aria-label` for custom elements
- Use semantic HTML when extending Ant Design components
- Test with screen readers when creating complex custom components

## Integration with Project Architecture

- Place reusable components in `/src/components/`
- Use the established styling patterns with `antd-style`
- Follow the project's TypeScript configuration
- Integrate with the existing theme and design tokens
