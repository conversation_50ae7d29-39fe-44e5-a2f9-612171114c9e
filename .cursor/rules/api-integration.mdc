---
globs: "src/server/**/*"
---

# API Integration Standards

## Request Architecture
All API calls should use the centralized request utility: [src/server/request.tsx](mdc:src/server/request.tsx)

## POP API Integration Pattern
```typescript
import request from '@/server/request';
import { POP_PRODUCT } from '@/server/popConfig';

interface RequestParams {
  // Define request parameters
}

interface ResponseData {
  // Define expected response structure
}

export const apiFunction = async (params: RequestParams) => {
  return request<RequestParams, ResponseData>({
    product: POP_PRODUCT.KNOWLEDGE_BASE, // Use appropriate product
    action: 'ActionName',
    params,
  });
};
```

## Error Handling
- All API errors are automatically handled by the request utility
- Error modals are displayed automatically using `showErrorModal`
- Always handle both success and error cases in components
- Use try-catch blocks for additional error handling when needed

## API File Organization
- Group related API functions in single files (e.g., knowledge-base operations)
- Use descriptive action names that match the backend API
- Export individual functions rather than default exports for better tree-shaking

## Request/Response Types
- Always define TypeScript interfaces for request parameters
- Define response data structures based on actual API responses
- Use generic types in the request function for type safety
- Include error response types using `ErrorResponse` interface

## Component Integration
```typescript
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

const handleApiCall = async () => {
  setLoading(true);
  setError(null);
  
  try {
    const response = await apiFunction(params);
    // Handle success
  } catch (err) {
    setError('Failed to process request');
    // Error modal already shown by request utility
  } finally {
    setLoading(false);
  }
};
```

## Authentication
- API authentication is handled automatically by the request utility
- Include necessary headers and signature generation
- Use the `getCurrentTimer()` utility for timestamp generation
- SignatureNonce is automatically generated for each request
