---
globs: "*.ts,*.tsx"
---

# TypeScript & React Component Standards

## TypeScript Requirements
- Always define explicit interfaces for component props
- Use proper typing for all function parameters and return values
- Export types when they might be reused across components
- Prefer interfaces over types for object shapes
- Use `React.FC` for functional component typing

## Component Structure Pattern
```typescript
import React from 'react';
import { useComponentStyles } from './index.styles';

interface ComponentNameProps {
  // Define all props with proper types
  id?: string;
  children?: React.ReactNode;
}

const ComponentName: React.FC<ComponentNameProps> = ({ 
  id = "default-id", 
  children 
}) => {
  const { styles } = useComponentStyles();
  
  return (
    <div className={styles.wrapper} id={id}>
      {children}
    </div>
  );
};

export default ComponentName;
```

## Import Organization
1. React and React-related imports first
2. Third-party library imports (antd, umi, etc.)
3. Internal imports grouped by:
   - Components (`@/components/`)
   - Utils (`@/utils/`)
   - Constants (`@/constant/`)
   - Types (`@/types/`)

## Type Definitions
- Define component prop interfaces immediately before the component
- Use descriptive names for interfaces: `ComponentNameProps`, `ApiResponseType`
- Always specify return types for complex functions
- Use generic types for reusable API functions

## Best Practices
- Use optional chaining (`?.`) for potentially undefined properties
- Implement proper error boundaries for component error handling
- Use React hooks properly with dependency arrays
- Prefer functional components over class components
- Always handle loading and error states in components that fetch data

## Ant Design Integration
- Import specific components from antd: `import { Button, Space } from 'antd'`
- Use Ant Design X components for AI interfaces: `import { Bubble, Sender, useXChat } from '@ant-design/x'`
- Always provide proper typing for Ant Design component props using `GetProp` utility type when needed
