---
alwaysApply: true
---

# AI-PC-FE Project Structure & Architecture Guide

## Project Overview

This is a React-based AI-PC frontend application built with:

- **Framework**: Umi v4 with TypeScript
- **UI Library**: Ant Design v5 + Ant Design X (AI components)
- **Styling**: antd-style + CSS modules
- **State Management**: React hooks and Context API
- **API Integration**: Custom POP API client with Axios

## Key Directory Structure

### Core Application Files

- [src/layouts/index.tsx](mdc:src/layouts/index.tsx) - Main layout wrapper with XProvider
- [src/components/menu/index.tsx](mdc:src/components/menu/index.tsx) - Navigation menu component
- [package.json](mdc:package.json) - Dependencies and scripts
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration

### Component Organization

- `/src/components/` - Reusable UI components
- `/src/pages/` - Route-based page components (chat, design, ppt, etc.)
- `/src/layouts/` - Layout components
- `/src/server/` - API integration layer
- `/src/utils/` - Utility functions
- `/src/constant/` - Application constants and types

### Key Components

- **Chat Interface**: [src/pages/chat/index.tsx](mdc:src/pages/chat/index.tsx) - Main AI chat interface using Ant Design X
- **Menu System**: Uses icon-based navigation with agent and general menu items
- **Knowledge Base**: [src/components/KnowledgeBaseManager/index.tsx](mdc:src/components/KnowledgeBaseManager/index.tsx) - Knowledge base management UI

## Architecture Principles

1. **Component-First**: Every UI element should be a reusable component
2. **Type Safety**: All components must have proper TypeScript interfaces
3. **Separation of Concerns**: Keep API logic in `/server/`, utilities in `/utils/`, components in `/components/`
4. **Consistent Styling**: Use antd-style for CSS-in-JS or LESS modules for component-specific styles
5. **Icon System**: Use the centralized icon mapping from [src/constant/icon.ts](mdc:src/constant/icon.ts)

## Development Guidelines

- Always import from `umi` for routing functionality
- Use Ant Design X components for AI-related interfaces
- Follow the established pattern for API requests using [src/server/request.tsx](mdc:src/server/request.tsx)
- Maintain consistent file naming: kebab-case for directories, camelCase for files
- Each component should have its own styles file (index.styles.ts or index.less)
