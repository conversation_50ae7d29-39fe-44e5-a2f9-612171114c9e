import AES from '@ali/aes-tracker';
import AESPluginPV from '@ali/aes-tracker-plugin-pv';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import AESPluginJSError from '@ali/aes-tracker-plugin-jserror';
import AESPluginAPI from '@ali/aes-tracker-plugin-api';
import AESPluginResourceError from '@ali/aes-tracker-plugin-resourceError';
import AESPluginPerf from '@ali/aes-tracker-plugin-perf';
import AESPluginEventTiming from '@ali/aes-tracker-plugin-eventTiming';
import AESPluginLongTask from '@ali/aes-tracker-plugin-longtask';
import AESPluginBlank from '@ali/aes-tracker-plugin-blank';
import { getLoginInfo } from '@/utils/user';
import { isPre } from '@/utils/env';

const initAES = async () => {
  // 开发环境不上报数据
  if (process.env.NODE_ENV === 'development') {
    return;
  }
  const loginInfo = await getLoginInfo();
  const aes = new AES({
    env: isPre() ? 'pre' : 'prod',
    pid: 'HPPWK5', // 项目 ID
    user_type: '6', // 当前登录用户所属的账号体系
    uid: loginInfo.username, // 当前登录用户的账号 ID
    username: loginInfo.username, // 当前登录用户的账号名称
  });
  // 挂载插件
  const plugins = aes.use([
    [AESPluginPV, {
      enableHash: true,
      getPageId: () => {
        return window.location.hash.split('#')[1] || '';
      },
    }],
    AESPluginEvent,
    AESPluginJSError,
    AESPluginAPI,
    AESPluginResourceError,
    AESPluginPerf,
    AESPluginEventTiming,
    AESPluginLongTask,
    AESPluginBlank,
  ]);

  return {
    aes,
    plugins,
  };
};

initAES();
