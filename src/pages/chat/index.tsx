import { Spin, notification } from 'antd';
import React, { useEffect, useState } from 'react';
import cx from 'classnames';
import ChatSender from '@/components/chat-sender';
import Welcome from '@/components/welcome';
import ChatHeader from '@/components/chat-header';
import { Iconfont } from '@/components/icon';
import ChatList from '@/components/chat-list';
import LiveComponent from '@/components/live-component';
import useChat from '@/hooks/useChat';
import { useHomePageStyle } from './index.styles';
import { AgentIdEnum } from '@/constant/enum';
import { ResourceItem } from '@/server/WuyingAI/SendMessage';
import { getLoginInfo, safeParseJSON } from '@/utils';
import { useTabContext } from '@/contexts/TabContext';
import { useRequest } from 'ahooks';

const times = [
  { label: '早上好！', start: 7, end: 11 },
  { label: '中午好！', start: 11, end: 13 },
  { label: '下午好！', start: 13, end: 18 },
  { label: '晚上好！', start: 18, end: 31 },
];

const getCurrentGreeting = () => {
  const hours = new Date().getHours();
  let currentTime = times[0];
  for (let i = 0; i < times.length; i++) {
    if (hours >= times[i].start % 24 && hours < times[i].end) {
      currentTime = times[i];
      break;
    }
  }
  return currentTime.label;
};

const ToolCallTab = ({ msgs, selected }: {
  msgs: Array<{
    toolName: string;
    toolCallContent: string;
    toolCallId: string;
    isFinished: boolean;
  }>;
  selected: {
    Name: string;
    DesktopId: string;
  } | null;
}) => {
  const { styles } = useHomePageStyle();

  return (<div className={styles.toolCallWrapper}>
    <div className={styles.toolCallHeader}>
      <div className={styles.toolCallHeaderTitle}>
        <div className={styles.toolCallHeaderName}>
          <Iconfont className={styles.toolCallHeaderIcon} type="window-logo" />
          {selected?.Name ?? ''}
        </div>
      </div>
      <div className={styles.toolCallHeaderTip}>搜索与内容相关的信息</div>
    </div>
    <div
      className={styles.toolCallHeaderTitle}
      style={{
        margin: '12px 0',
      }}
    >任务进度</div>
    {
            msgs.map((msg) => {
              // pc agent调用不显示
              if (msg.toolName === 'pc_agent') return null;
              const messageContent = safeParseJSON(msg.toolCallContent)?.msg ?? '';
              return (<div key={msg.toolCallId} className={styles.toolCallItem}>
                <Iconfont className={styles.toolCallItemStatusIcon} type={msg.isFinished ? 'checkmark--outline' : 'play--outline'} fill={msg.isFinished ? '#00BFA5' : '#0075FF'} />
                {
                  msg.isFinished ? <div className={styles.toolCallItemStatus} style={{ color: '#00BFA5' }}>已完成</div> : <div className={styles.toolCallItemStatus} style={{ color: '#0075FF' }}>进行中</div>
                }
                <div style={{
                  color: '#474A52',
                }}
                >{msg.toolName}: {messageContent}</div>
              </div>
              );
            })
          }
  </div>);
};

const Chat: React.FC = () => {
  const { styles } = useHomePageStyle();
  const { tabs, addTab, updateTab, tabVisible, hideTabs, showTabs, closeAllTabs } = useTabContext();

  const { sendMessage, loading, messages, setMessages, resetChat, stopChat, setSessionInfo, sessionInfo, toolCallMsgs, connectInfo } =
    useChat({
      agentId: AgentIdEnum.alpha,
    });

  const { data: userInfo } = useRequest(getLoginInfo);
  const username = userInfo?.username;

  // 当前执行环境
  const [selectedEnvironment, setSelectedEnvironment] = useState<{
    Name: string;
    DesktopId: string;
  } | null>(null);

  useEffect(() => {
    if (!toolCallMsgs.length) {
      return;
    }
    if (tabs.find((it) => it.key === 'toolCall')) {
      updateTab('toolCall', {
        props: {
          msgs: toolCallMsgs,
          selected: selectedEnvironment,
        },
      });
    } else {
      addTab({
        key: 'toolCall',
        label: '当前会话',
        children: ToolCallTab,
        props: {
          msgs: toolCallMsgs,
          selected: selectedEnvironment,
        },
      });
    }
  }, [toolCallMsgs]);

  useEffect(() => {
    if (connectInfo?.ticket) {
      if (tabs.find((it) => it.key === 'live')) {
        updateTab('live', {
          props: {
            connectInfo,
          },
        });
      } else {
        addTab({
          key: 'live',
          label: 'LIVE',
          children: LiveComponent,
          props: {
            connectInfo,
          },
        });
      }
    }
  }, [connectInfo]);

  useEffect(() => {
    return () => {
      // 页面卸载时关闭所有tab
      closeAllTabs();
    };
  }, []);

  // ==================== Event ====================
  const onSubmit = (val: string, resources: ResourceItem[]) => {
    if (!val) return;

    if (loading) {
      notification.error({
        message: '请求正在处理中，请等待请求完成。',
      });
      return;
    }

    sendMessage(val, resources, selectedEnvironment?.DesktopId);
  };

  // ==================== Nodes ====================
  const chatList = messages?.length ? (
    <ChatList messages={messages} loading={loading} sessionId={sessionInfo.SessionId} updateMessages={setMessages} />
  ) : (
    <Welcome text={`${getCurrentGreeting()}${username}`} tip="连云端万物,有求必应的云上AI新伙伴" />
  );

  const handleOpenTab = () => {
    if (tabVisible) {
      hideTabs();
    } else {
      showTabs();
    }
  };

  // ==================== Render =================
  return (
    <div className={styles.layout}>
      <ChatHeader
        agentId={AgentIdEnum.alpha}
        selectedEnvironment={selectedEnvironment}
        onSelectEnvironment={setSelectedEnvironment}
        onNewChat={resetChat}
        handleOpenTab={handleOpenTab}
      />
      <div
        className={cx(styles.chat, {
          [styles.layoutChatting]: messages.length > 0,
        })}
      >
        {chatList}
        <ChatSender
          onSubmit={onSubmit}
          loading={loading}
          agentId={AgentIdEnum.alpha}
          sessionInfo={sessionInfo}
          setSessionInfo={setSessionInfo}
          stopChat={stopChat}
        />
      </div>
    </div>
  );
};

export default Chat;
