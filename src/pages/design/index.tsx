import { useRequest } from 'ahooks';
import { getLoginInfo } from '@/utils';
import { useLocation } from 'umi';

export default function Design() {
  // TODO: 设计页跳转
  // window.location.href = 'http://47.98.254.247:3000/';

  const location = useLocation();
  console.log('🚀 ~ Design ~ location:', location);
  // 获取登陆态
  const { data: userInfo } = useRequest(getLoginInfo);
  console.log('🚀 ~ Design ~ userInfo:', userInfo);

  // return null;
  return (userInfo?.loginToken && location?.pathname?.includes('design') && <iframe
    src={`https://ai-design.pre-wy.aliyun.com?loginToken=${encodeURIComponent(userInfo?.loginToken || '')}&loginSessionId=${encodeURIComponent(userInfo?.loginSessionId || '')}&username=${encodeURIComponent(userInfo?.username || '')}&loginRegionId=${encodeURIComponent(userInfo?.loginRegionId || '')}`}
    width="100%"
    height="100%"
    style={{
      border: 'none',
    }}
    allow="cross-origin-isolated"
  />);
}
