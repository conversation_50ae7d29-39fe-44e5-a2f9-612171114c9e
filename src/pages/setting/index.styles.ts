import { createStyles } from 'antd-style';

export const useSettingStyles = createStyles(({ css }) => ({
  wrapper: css`
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    background: #f2f5fa;
    color: #474a52;
    flex: 1;
    height: 100%;
  `,
  title: css`
    display: flex;
    padding: 16px 0;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #1f2024;
  `,
  titleIcon: css`
    width: 24px;
    height: 24px;
  `,
  content: css`
    border-radius: 16px;
    background: #ffffff;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  `,
  itemTitle: css`
    color: #474a52;
    font-weight: bold;
  `,
  itemSelector: css`
    width: 100%;
    max-width: 500px;
    height: 42px;
    margin: 12px 0 20px;
  `,
  itemBorder: css`
    border: 1px solid #e6e8eb;
    border-radius: 8px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  `,
  quotaTip: css`
    color: #8c909c;
    display: flex;
    align-items: center;
    margin: 12px 0;
    & svg {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  `,
  quota: css`
    justify-content: space-between;
  `,
  feedbackTitle: css`
    display: flex;
align-items: center;
color: #1F2024;
font-weight: bold;
margin-bottom: 32px;
  `,
  feedbackIssue: css`
  margin: 20px 0;
  `,
  feedbackContact: css`
  margin: 20px 0;
  height: 52px;
  `,
}));
