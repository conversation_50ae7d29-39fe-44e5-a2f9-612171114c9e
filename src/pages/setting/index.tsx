import { Select, Input, Button, notification } from 'antd';
import { Iconfont } from '@/components/icon';
import { DescribeUserSetting } from '@/server/WuyingAI/DescribeUserSetting';
import { UpdateUserSetting } from '@/server/WuyingAI/UpdateUserSetting';
import { CreateUserFeedback } from '@/server/WuyingAI/CreateUserFeedback';
import { useSettingStyles } from './index.styles';
import { useRequest } from 'ahooks';
import { useState } from 'react';
export default function Setting() {
  const { styles, cx } = useSettingStyles();
  const { data: settingsInfo, run: getSettingsInfo } = useRequest(() => DescribeUserSetting({}));
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackInfo, setFeedbackInfo] = useState({
    IssueDetail: '',
    ContactInfo: '',
  });
  // TODO: 更新设置
  const updateSettings = ({ DesktopId, Model }: { DesktopId?: string; Model?: string }) => {
    UpdateUserSetting({
      DesktopId, // 运行环境偏好，传递desktop_id
      Model, // 模型选择，传递model名称
    }).then(getSettingsInfo);
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.title}>
        <Iconfont type="settings--alt--outline" className={styles.titleIcon} />
        <span>设置</span>
      </div>
      <div className={styles.content}>
        {showFeedback ? <>
          <div className={styles.feedbackTitle}><Iconfont type="arrow--left" className={styles.titleIcon} style={{ marginRight: 12, cursor: 'pointer' }} onClick={() => setShowFeedback(false)} />问题反馈详情</div>
          <div className={styles.itemTitle}>问题详情</div>
          <Input.TextArea
            className={styles.feedbackIssue}
            style={{
              resize: 'none',
              height: 360,
            }}
            value={feedbackInfo.IssueDetail}
            placeholder="请输入问题"
            onChange={(e) => setFeedbackInfo((prev) => ({
              ...prev,
              IssueDetail: e.target.value,
            }))}
          />
          <div className={styles.itemTitle}>联系方式</div>
          <Input
            className={styles.feedbackContact}
            value={feedbackInfo.ContactInfo}
            placeholder="请输入手机号"
            onChange={(e) => setFeedbackInfo((prev) => ({
              ...prev,
              ContactInfo: e.target.value,
            }))}
          />
          <Button
            type="primary"
            style={{
              marginBottom: 16,
            }}
            onClick={
            async () => {
              if (!feedbackInfo.ContactInfo || !feedbackInfo.IssueDetail) return;
              try {
                await CreateUserFeedback(feedbackInfo);
                notification.success({
                  message: '提交成功',
                });
                setShowFeedback(false);
                setFeedbackInfo({
                  IssueDetail: '',
                  ContactInfo: '',
                });
              } catch (e) {
                notification.error({
                  message: '提交失败',
                });
              }
            }
          }
          >提交</Button>
        </> : <>        <div className={styles.itemTitle}>运行环境偏好</div>
          <Select
            className={styles.itemSelector}
            options={settingsInfo?.Data?.AvailableEnvironments?.map?.((it) => ({
              label: it.Name,
              value: it.DesktopId,
            }))}
            value={settingsInfo?.Data?.CurrentSettings?.DesktopId}
            onChange={(value) => updateSettings({ DesktopId: value })}
            placeholder="请选择运行环境偏好"
          />
          <div className={styles.itemTitle}>软件版本</div>
          <div
            className={cx(styles.itemSelector, styles.itemBorder, styles.quota)}
            style={{ marginBottom: 0, borderRadius: '8px 8px 0 0', borderBottom: 0 }}
          >
            <span>版本号</span>
            <span>V1.0.0</span>
          </div>
          <div
            className={cx(styles.itemSelector, styles.itemBorder, styles.quota)}
            style={{ marginTop: 0, borderRadius: '0 0 8px 8px' }}
          >
            <span>版本时间</span>
            <span>2025.07.30 </span>
          </div>
          {/* <div className={styles.itemTitle}>模型切换</div>
          <Select
            className={styles.itemSelector}
            options={
            settingsInfo?.Data?.AvailableModels?.map?.((it) => ({ label: it, value: it })) ?? []
          }
            value={settingsInfo?.Data?.CurrentSettings?.Model}
            onChange={(value) => updateSettings({ Model: value })}
            placeholder="请选择模型"
          /> */}
          <div className={styles.itemTitle}>更多</div>
          <div
            className={cx(styles.itemSelector, styles.itemBorder)}
            style={{
              cursor: 'pointer',
            }}
            onClick={() => setShowFeedback(true)}
          >问题反馈</div></>}
        {/* <div className={styles.itemTitle}>额度说明</div>
        <div className={styles.quotaTip}>
          <Iconfont type="information--filled" fill="#0075FF" />
          <span>余额每日24点重置，不同的交互所使用的余额大小不同，计数以AI回答为准</span>
        </div>
        <div className={cx(styles.itemSelector, styles.itemBorder, styles.quota)}>
          <span>试用期额度</span>
          <span>489/1000</span>
        </div> */}
      </div>
    </div>
  );
}
