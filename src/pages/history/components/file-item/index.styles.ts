import { createStyles } from 'antd-style';

export const useFileItemStyles = createStyles(({ token }) => ({
  fileItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '20px',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    cursor: 'pointer',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: token.colorFillAlter,
    },
    '@media (max-width: 768px)': {
      padding: '16px',
      flexDirection: 'column',
      alignItems: 'stretch',
      gap: '12px',
    },
    '@media (max-width: 480px)': {
      padding: '12px',
      gap: '8px',
    },
  },
  fileContent: {
    display: 'flex',
    alignItems: 'center',
    flex: 1,
    cursor: 'pointer',
    minWidth: 0,
    '@media (max-width: 768px)': {
      flex: 'none',
      width: '100%',
    },
  },
  fileIcon: {
    width: '28px',
    height: '28px',
    flexShrink: 0,
    '@media (max-width: 480px)': {
      width: '24px',
      height: '24px',
    },
  },
  fileTitle: {
    marginLeft: '12px',
    fontSize: '14px',
    color: token.colorTextSecondary,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    // whiteSpace: 'nowrap',
    '@media (max-width: 768px)': {
      whiteSpace: 'normal',
      display: '-webkit-box',
      WebkitLineClamp: 2,
      WebkitBoxOrient: 'vertical',
      lineHeight: '1.4',
    },
    '@media (max-width: 480px)': {
      fontSize: '13px',
      marginLeft: '8px',
    },
  },
  fileActions: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px',
    flexShrink: 0,
    '@media (max-width: 768px)': {
      justifyContent: 'space-between',
      gap: '16px',
    },
    '@media (max-width: 480px)': {
      gap: '12px',
    },
  },
  fileDate: {
    fontSize: '14px',
    color: token.colorTextTertiary,
    '@media (max-width: 480px)': {
      fontSize: '12px',
    },
  },
  actionIcon: {
    fontSize: '16px',
    color: token.colorTextTertiary,
    cursor: 'pointer',
    padding: '4px',
    borderRadius: '4px',
    transition: 'all 0.2s',
    '&:hover': {
      color: token.colorPrimary,
      backgroundColor: token.colorPrimaryBg,
    },
    '@media (max-width: 480px)': {
      fontSize: '14px',
      padding: '6px',
    },
  },
}));
