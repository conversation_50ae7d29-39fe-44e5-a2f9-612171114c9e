import React, { useState } from 'react';
import { Dropdown, message, Modal, Input } from 'antd';
import {
  EyeOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { history } from 'umi';
import { useFileItemStyles } from './index.styles';
import { Iconfont } from '@/components/icon';
import { historyIconMap, HistoryIconType } from '@/constant/icon';
import { DeleteSession } from '@/server/WuyingAI/DeleteSession';
import { UpdateSession } from '@/server/WuyingAI/UpdateSession';
import dayjs from 'dayjs';
import { AgentIdEnum, agentIdRouterMap } from '@/constant/enum';

interface FileData {
  title: string;
  date: string;
  sessionId: string;
  agentId?: AgentIdEnum;
}

interface FileItemProps {
  file: FileData;
  refresh: () => void;
}

const FileItem: React.FC<FileItemProps> = ({ file, refresh }) => {
  const { styles } = useFileItemStyles();
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState(file.title);

  // 更新会话标题的请求
  const { loading: updateLoading, run: updateSession } = useRequest(
    (params) => UpdateSession(params),
    {
      manual: true,
      onSuccess: (data) => {
        message.success('重命名成功');
        setIsRenameModalVisible(false);
        // 这里可以触发父组件刷新数据
        refresh();
      },
      onError: (error) => {
        message.error('重命名失败，请重试');
      },
    },
  );

  // 删除会话的请求
  const { loading: deleteLoading, run: deleteSession } = useRequest(
    (params) => DeleteSession(params),
    {
      manual: true,
      onSuccess: (data) => {
        message.success('删除成功');
        // 这里可以触发父组件刷新数据
        refresh();
      },
      onError: (error) => {
        message.error('删除失败，请重试');
      },
    },
  );

  const handleView = () => {
    message.info(`sessionId and agentId: ${file.sessionId} ${file.agentId}`);
  };

  const handleRename = () => {
    setNewTitle(file.title);
    setIsRenameModalVisible(true);
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>确定要删除这个文件吗？</p>
          <p style={{ color: '#666', fontSize: '14px', marginTop: '8px' }}>文件名：{file.title}</p>
        </div>
      ),
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: {
        loading: deleteLoading,
      },
      onOk: () => {
        deleteSession({
          SessionId: file.sessionId,
        });
      },
      onCancel: () => {
        // 用户取消删除
      },
    });
  };

  const handleRenameConfirm = () => {
    if (!newTitle.trim()) {
      message.error('标题不能为空');
      return;
    }

    if (newTitle.trim() === file.title) {
      setIsRenameModalVisible(false);
      return;
    }

    updateSession({
      SessionId: file.sessionId,
      NewTitle: newTitle.trim(),
    });
  };

  const handleRenameCancel = () => {
    setIsRenameModalVisible(false);
    setNewTitle(file.title);
  };

  const menuItems = [
    {
      key: 'rename',
      icon: <EditOutlined />,
      label: '重命名',
      onClick: handleRename,
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      onClick: handleDelete,
    },
  ];

  const handleContinue = () => {
    // 跳转对应的会话页面
    history.push({
      pathname: agentIdRouterMap[file.agentId as AgentIdEnum],
      search: `?SessionId=${file.sessionId}`,
    });
  };

  return (
    <>
      <div className={styles.fileItem}>
        <div className={styles.fileContent} onClick={handleContinue}>
          <Iconfont
            type={historyIconMap[file.agentId as HistoryIconType] ?? 'alpha'}
            className={styles.fileIcon}
          />
          <span className={styles.fileTitle}>{file.title}</span>
        </div>

        <div className={styles.fileActions}>
          <span className={styles.fileDate}>{dayjs(file.date).format('YYYY-MM-DD HH:mm:ss')}</span>
          <EyeOutlined className={styles.actionIcon} onClick={handleContinue} />
          <Dropdown menu={{ items: menuItems }} trigger={['click']} placement="bottomRight">
            <MoreOutlined className={styles.actionIcon} onClick={(e) => e.stopPropagation()} />
          </Dropdown>
        </div>
      </div>

      <Modal
        title="重命名文件"
        open={isRenameModalVisible}
        onOk={handleRenameConfirm}
        onCancel={handleRenameCancel}
        confirmLoading={updateLoading}
        okText="确认"
        cancelText="取消"
        width={500}
      >
        <Input
          value={newTitle}
          onChange={(e) => setNewTitle(e.target.value)}
          placeholder="请输入新的文件名"
          maxLength={100}
          showCount
          onPressEnter={handleRenameConfirm}
          autoFocus
        />
      </Modal>
    </>
  );
};

export default FileItem;
