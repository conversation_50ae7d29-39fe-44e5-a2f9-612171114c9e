import { createStyles } from 'antd-style';

export const useHeaderStyles = createStyles(({ token }) => ({
  header: {
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    padding: '0 20px',
    '@media (max-width: 768px)': {
      padding: '0 16px',
    },
    '@media (max-width: 480px)': {
      padding: '0 12px',
    },
  },
  headerContent: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'end',
    height: '64px',
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      alignItems: 'stretch',
      height: 'auto',
      gap: '16px',
      paddingTop: '16px',
    },
  },
  tabsContainer: {
    display: 'flex',
    gap: '24px',
    alignItems: 'end',
    overflowX: 'auto',
    scrollbarWidth: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    '@media (max-width: 768px)': {
      gap: '16px',
      alignItems: 'center',
    },
    '@media (max-width: 480px)': {
      gap: '12px',
    },
  },
  tab: {
    padding: '16px 0',
    cursor: 'pointer',
    fontSize: '14px',
    color: token.colorText,
    borderBottom: '3px solid transparent',
    transition: 'all 0.2s',
    whiteSpace: 'nowrap',
    flexShrink: 0,
    '&:hover': {
      color: token.colorPrimary,
    },
    '@media (max-width: 768px)': {
      padding: '12px 0',
      fontSize: '13px',
    },
    '@media (max-width: 480px)': {
      padding: '8px 0',
      fontSize: '12px',
    },
  },
  activeTab: {
    color: token.colorPrimary,
    borderBottomColor: token.colorPrimary,
    fontWeight: 500,
  },
  searchContainer: {
    marginBottom: '12px',
    '@media (max-width: 768px)': {
      marginBottom: '16px',
    },
  },
  searchInput: {
    width: '320px',
    borderRadius: '12px',
    '& .ant-input': {
      fontSize: '16px',
    },
    '@media (max-width: 768px)': {
      width: '100%',
      maxWidth: '400px',
    },
    '@media (max-width: 480px)': {
      '& .ant-input': {
        fontSize: '14px',
      },
    },
  },
}));
