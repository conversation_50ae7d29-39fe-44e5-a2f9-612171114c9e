import React, { useState } from 'react';
import { Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useHeaderStyles } from './index.styles';
import { AgentIdEnum } from '@/constant/enum';

const tabs = [
  {
    name: '全部',
    value: 'all',
  },
  {
    name: '通用',
    value: AgentIdEnum.alpha,
  },
  {
    name: 'AI PPT',
    value: AgentIdEnum.ppt,
  },
  {
    name: 'Deep Research',
    value: AgentIdEnum.deep,
  },
];
interface HeaderProps {
  activeTab: string;
  setActiveTab: (tab: AgentIdEnum | 'all') => void;
  handleSearch: (value: string) => void;
}

const Header: React.FC<HeaderProps> = ({
  activeTab,
  setActiveTab,
  handleSearch,
}) => {
  const { styles, cx } = useHeaderStyles();

  const [value, setValue] = useState('');

  return (
    <div className={styles.header}>
      <div className={styles.headerContent}>
        <div className={styles.tabsContainer}>
          {tabs.map((tab) => (
            <div
              key={tab.value}
              className={cx(styles.tab, activeTab === tab.value && styles.activeTab)}
              onClick={() => setActiveTab(tab.value as AgentIdEnum | 'all')}
            >
              {tab.name}
            </div>
          ))}
        </div>

        <div className={styles.searchContainer}>
          <Input
            className={styles.searchInput}
            placeholder="搜索对话"
            prefix={<SearchOutlined />}
            value={value}
            onPressEnter={() => handleSearch(value)}
            onChange={(e) => setValue(e.target.value)}
            size="large"
          />
        </div>
      </div>
    </div>
  );
};

export default Header;
