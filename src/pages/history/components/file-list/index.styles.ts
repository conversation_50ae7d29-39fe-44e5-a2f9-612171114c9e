import { createStyles } from 'antd-style';

export const useFileListStyles = createStyles(({ token }) => ({
  container: {
    paddingBottom: '16px',
  },
  fileList: {
    minHeight: '400px',
  },
  pagination: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '16px',
    padding: '24px 20px',
    borderTop: `1px solid ${token.colorBorderSecondary}`,
    backgroundColor: token.colorFillAlter,
    '@media (max-width: 480px)': {
      padding: '16px 12px',
      gap: '12px',
    },
  },
  paginationBtn: {
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '6px',
    backgroundColor: token.colorBgContainer,
    border: `1px solid ${token.colorBorderSecondary}`,
    cursor: 'pointer',
    transition: 'all 0.2s',
    color: token.colorText,
    '&:hover:not(.disabled)': {
      borderColor: token.colorPrimary,
      color: token.colorPrimary,
    },
    '@media (max-width: 480px)': {
      width: '28px',
      height: '28px',
    },
  },
  disabled: {
    cursor: 'not-allowed',
    opacity: 0.4,
    '&:hover': {
      borderColor: token.colorBorderSecondary,
      color: token.colorText,
    },
  },
  paginationText: {
    fontSize: '14px',
    color: token.colorTextSecondary,
    fontWeight: 500,
    minWidth: '40px',
    textAlign: 'center',
    '@media (max-width: 480px)': {
      fontSize: '13px',
      minWidth: '35px',
    },
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '400px',
  },
  errorContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '400px',
  },
  emptyContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '400px',
  },
}));
