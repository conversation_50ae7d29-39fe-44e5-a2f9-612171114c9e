import React, { useState, useEffect } from 'react';
import { Spin, Empty } from 'antd';
import { useRequest } from 'ahooks';
import { useFileListStyles } from './index.styles';
import FileItem from '../file-item';
import { ListUserSessions, Session } from '@/server/WuyingAI/ListUserSessions';
import { RightOutlined } from '@ant-design/icons';
import { AgentIdEnum } from '@/constant/enum';

interface FileData {
  title: string;
  date: string;
  sessionId: string;
  agentId?: AgentIdEnum;
}

interface FileListProps {
  activeTab: AgentIdEnum | 'all';
  searchQuery: string;
}

const FileList: React.FC<FileListProps> = ({ activeTab, searchQuery }) => {
  const { styles } = useFileListStyles();
  const [nextToken, setNextToken] = useState<string | undefined>(undefined);
  const [allSessions, setAllSessions] = useState<Session[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10;
  // 根据 tab 获取对应的 AgentId
  const getAgentIdByTab = (tab: AgentIdEnum | 'all'): AgentIdEnum | undefined => {
    return tab === 'all' ? undefined : tab;
  };

  // 使用 useRequest 请求会话列表
  const { loading, error, run } = useRequest(
    (token?: string) =>
      ListUserSessions({
        MaxResults: pageSize,
        NextToken: token,
        AgentId: getAgentIdByTab(activeTab),
        Keyword: searchQuery,
      }),
    {
      manual: true,
      onSuccess: (data) => {
        if (nextToken) {
          // 加载更多数据
          setAllSessions((prev) => [...prev, ...(data?.Data?.Sessions || [])]);
        } else {
          // 首次加载或重新加载
          setAllSessions(data?.Data?.Sessions || []);
        }
        setHasMore(!!data?.NextToken);
        setNextToken(data?.NextToken);
      },
    },
  );

  // 当 tab 或搜索条件改变时重新加载数据
  useEffect(() => {
    setAllSessions([]);
    setNextToken(undefined);
    setHasMore(true);
    run();
  }, [activeTab, run, searchQuery]);

  const refresh = () => {
    setAllSessions([]);
    setNextToken(undefined);
    setHasMore(true);
    run();
  };

  // 将会话数据转换为文件数据格式
  const convertSessionsToFiles = (sessions: Session[]): FileData[] => {
    return sessions.map((session) => {
      return {
        title: session.Title,
        date: session.GmtModified,
        sessionId: session.SessionId,
        agentId: session.AgentId,
      };
    });
  };

  const fileData: FileData[] = convertSessionsToFiles(allSessions);

  const handleLoadMore = () => {
    if (hasMore && !loading && nextToken) {
      run(nextToken);
    }
  };

  // 首次加载状态
  if (loading && allSessions.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  // 错误状态
  if (error && allSessions.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.errorContainer}>
          <Empty description="加载失败，请重试" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      </div>
    );
  }

  // 空数据状态
  if (fileData.length === 0 && !loading) {
    return (
      <div className={styles.container}>
        <div className={styles.emptyContainer}>
          <Empty
            description={searchQuery ? '未找到相关文件' : '暂无文件'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Spin spinning={loading}>
        <div className={styles.fileList}>
          {fileData.map((file) => (
            <FileItem key={file.sessionId} file={file} refresh={refresh} />
          ))}
        </div>
        {hasMore && (
          <>
            <div className={styles.pagination} onClick={handleLoadMore}>
              加载更多
              <RightOutlined />
            </div>
          </>
        )}
      </Spin>
    </div>
  );
};

export default FileList;
