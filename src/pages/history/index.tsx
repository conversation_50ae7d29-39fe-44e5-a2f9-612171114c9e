import React, { useState } from 'react';
import { useAppStyles } from './index.styles';
import Header from './components/header';
import FileList from './components/file-list';
import { Iconfont } from '@/components/icon';
import { AgentIdEnum } from '@/constant/enum';
export default function History() {
  const { styles } = useAppStyles();
  const [activeTab, setActiveTab] = useState<AgentIdEnum | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };
  return (
    <div className={styles.wrapper}>
      <div className={styles.title}>
        <Iconfont type="lishi" className={styles.titleIcon} />
        <span>历史对话</span>
      </div>
      <div className={styles.content}>
        <Header
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          handleSearch={handleSearch}
        />
        <FileList activeTab={activeTab} searchQuery={searchQuery} />
      </div>
    </div>
  );
}
