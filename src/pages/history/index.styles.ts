import { createStyles } from 'antd-style';

export const useAppStyles = createStyles(({ token }) => ({
  // container: {
  //   // minHeight: '100vh',
  //   backgroundColor: '#f8f9fa',
  //   padding: '20px',
  //   flex: 1,
  //   '@media (max-width: 768px)': {
  //     padding: '12px',
  //   },
  //   '@media (max-width: 480px)': {
  //     padding: '8px',
  //   },
  // },
  content: {
    // maxWidth: '1200px',
    margin: '0 auto',
    backgroundColor: token.colorBgContainer,
    borderRadius: '16px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
    overflow: 'auto',
    width: '100%',
    '@media (max-width: 768px)': {
      borderRadius: '12px',
    },
    '@media (max-width: 480px)': {
      borderRadius: '8px',
    },
  },
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    padding: '20px',
    background: '#f2f5fa',
    color: '#474a52',
    flex: 1,
    height: '100%',
  },
  title: {
    fontSize: '18px',
    fontWeight: 'bold',
    display: 'flex',
    padding: '16px 0',
    alignItems: 'center',
    color: '#1f2024',
  },
  titleIcon: {
    height: '24px',
    width: '24px',
  },
}));
