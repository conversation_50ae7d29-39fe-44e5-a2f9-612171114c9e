import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  listManager: css`
    width: 100%;
    box-shadow: unset;
    border-radius: 8px;
    border: none;

    .ant-card-body {
      padding: 0;
    }
  `,
  header: css`
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `,
  title: css`
    font-size: 14px;
    color: #474a52;
  `,
  search: css`
    width: 280px;

    .ant-input-affix-wrapper {
      border-radius: 12px;
      background-color: #f2f5fa;
      border: none;

      &:focus,
      &.ant-input-affix-wrapper-focused {
        border-color: #0075ff;
        box-shadow: 0 0 0 2px rgba(0, 117, 255, 0.1);
      }
    }

    .ant-input {
      background-color: transparent;
      color: #8c909c;

      &::placeholder {
        color: #8c909c;
      }
    }
  `,
  searchIcon: css`
    color: #8c909c;
  `,
  tabs: css`
    margin: 0;

    .ant-tabs-nav {
      margin: 0;
      padding: 0 20px;
    }

    .ant-tabs-tab {
      font-size: 14px;
      font-weight: 400;
      color: #1f2024;

      &.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #0075ff;
        font-weight: 500;
      }
    }

    .ant-tabs-ink-bar {
      background: #0075ff;
      height: 3px;
    }
  `,
}));
