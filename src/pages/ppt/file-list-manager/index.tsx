import { useState, useEffect } from 'react';
import { Card, Input, Tabs, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import FileList from './file-list';
import { useStyles } from './index.styles';
import { ListUserSessionArtifacts, ArtifactItem } from '@/server/WuyingAI/ListUserSessionArtifacts';
import dayjs from 'dayjs';

const { Search } = Input;

type ArtifactType = 'resultArtifact' | 'processArtifact' | 'sessionFile';

// 定义不同tab对应的ArtifactTypes
const TAB_ARTIFACT_TYPES: Record<string, ArtifactType[]> = {
  all: ['resultArtifact', 'processArtifact', 'sessionFile'],
  result: ['resultArtifact'],
  process: ['processArtifact'],
};

interface TabFiles {
  all: ArtifactItem[];
  result: ArtifactItem[];
  process: ArtifactItem[];
}

const FileListManager = () => {
  const { styles } = useStyles();
  const [searchValue, setSearchValue] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [loading, setLoading] = useState(false);
  const [tabFiles, setTabFiles] = useState<TabFiles>({
    all: [],
    result: [],
    process: [],
  });

  // 获取文件列表
  const fetchFileList = async (tab: string = activeTab) => {
    setLoading(true);
    try {
      const response = await ListUserSessionArtifacts({
        LoginToken: '123',
        RegionId: 'cn-hangzhou',
        SessionId: 'sess_ddb4e9a45ece4c1f8f32c9237f109d56',
        ArtifactTypes: ['artifactFile', 'sessionFile'], // TODO: 需要修改
        MaxResults: 100,
      });

      if (response.Code === '200') {
        console.log('API Response:', response);
        // 更新对应tab的文件列表
        setTabFiles((prev) => ({
          ...prev,
          [tab]: response.Data || [],
        }));
      } else {
        message.error(response.Message || '获取文件列表失败');
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 初始加载时获取全部文件
    fetchFileList('all');
  }, []);

  // 当切换tab时，如果该tab还没有数据则获取数据
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    if (tabFiles[newTab as keyof TabFiles].length === 0) {
      fetchFileList(newTab);
    }
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const getFilteredFiles = () => {
    const currentFiles = tabFiles[activeTab as keyof TabFiles];

    // 根据搜索关键词筛选
    let filteredFiles = currentFiles;
    if (searchValue) {
      filteredFiles = filteredFiles.filter((file) =>
        file.FileName.toLowerCase().includes(searchValue.toLowerCase()));
    }

    // 转换为FileList组件需要的格式
    return filteredFiles.map((file) => ({
      id: file.ArtifactId,
      name: file.FileName,
      type: file.FileType.split('/')[1] || file.FileType,
      date: dayjs(file.GmtModified).format('YYYY-MM-DD'),
      size: `${(file.FileSize / (1024 * 1024)).toFixed(1)}MB`,
      isAIGenerated: file.ArtifactType === 'resultArtifact',
    }));
  };

  const getTabCounts = () => {
    return {
      all: tabFiles.all.length,
      result: tabFiles.result.length,
      process: tabFiles.process.length,
    };
  };

  const counts = getTabCounts();
  const tabItems = [
    {
      key: 'all',
      label: `全部文件 (${counts.all})`,
    },
    {
      key: 'result',
      label: `结果文件 (${counts.result})`,
    },
    {
      key: 'process',
      label: `过程文件 (${counts.process})`,
    },
  ];

  return (
    <Card className={styles.listManager}>
      {/* Header */}
      <div className={styles.header}>
        <span className={styles.title}>文件列表</span>
        <Search
          placeholder="搜索文件"
          allowClear
          className={styles.search}
          prefix={<SearchOutlined className={styles.searchIcon} />}
          onSearch={handleSearch}
          onChange={(e) => setSearchValue(e.target.value)}
        />
      </div>

      {/* Tabs */}
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        className={styles.tabs}
      />

      {/* File List */}
      <FileList files={getFilteredFiles()} onFileDeleted={() => fetchFileList(activeTab)} />
    </Card>
  );
};

export default FileListManager;
