import { List, Tag, Button, Dropdown, Space, message, Modal } from 'antd';
import { DownloadOutlined, EyeOutlined, MoreOutlined } from '@ant-design/icons';
import { getFileIconByFileName } from '@/utils/file';
import { useStyles } from './index.styles';
import { ListUserArtifactDownloadUrls } from '@/server/WuyingAI/ListUserArtifactDownloadUrls';
import { DeleteUserArtifacts } from '@/server/WuyingAI/DeleteUserArtifacts';
import { Iconfont } from '@/components/icon';

interface FileItem {
  id: string;
  name: string;
  type: string;
  date: string;
  size: string;
  isAIGenerated: boolean;
}

interface FileListProps {
  files: FileItem[];
  onFileDeleted?: () => void;
}

const FileList = ({ files, onFileDeleted }: FileListProps) => {
  const { styles } = useStyles();

  const handleDownload = async (file: FileItem) => {
    try {
      const response = await ListUserArtifactDownloadUrls({
        LoginToken: '123',
        LoginSessionId: 'sess_ddb4e9a45ece4c1f8f32c9237f109d56',
        ArtifactIds: [file.id],
      });

      if (response.Code === '200' && response.Data.DownloadLinks.length > 0) {
        const downloadLink = response.Data.DownloadLinks[0];

        const link = document.createElement('a');
        link.href = downloadLink.DownloadUrl;
        link.download = downloadLink.FileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        message.success('开始下载文件');
      } else if (response.Data.FailedFiles.length > 0) {
        const failedFile = response.Data.FailedFiles[0];
        message.error(`下载失败: ${failedFile.Error}`);
      } else {
        message.error(response.Message || '下载失败');
      }
    } catch (error) {
      console.error('下载文件失败:', error);
      message.error('下载文件失败');
    }
  };

  const handleDelete = async (file: FileItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${file.name}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await DeleteUserArtifacts({
            LoginToken: '123',
            RegionId: 'cn-hangzhou',
            ArtifactIds: [file.id],
          });

          if (response.Code === '200') {
            if (response.Data.FailedFiles?.length > 0) {
              const failedFile = response.Data.FailedFiles[0];
              message.error(`删除失败: ${failedFile.Error}`);
            } else {
              message.success(response.Message || '文件删除成功');
              onFileDeleted?.();
            }
          } else {
            message.error(response.Message || '删除失败');
          }
        } catch (error) {
          console.error('删除文件失败:', error);
          message.error('删除文件失败');
        }
      },
    });
  };

  const handlePreview = (file: FileItem) => {
    console.log('预览文件:', file.name);
  };

  const getMoreMenuItems = (file: FileItem) => [
    {
      key: 'download',
      label: '下载',
      icon: <DownloadOutlined />,
      onClick: () => handleDownload(file),
    },
    {
      key: 'preview',
      label: '预览',
      icon: <EyeOutlined />,
      onClick: () => handlePreview(file),
    },
    {
      key: 'share',
      label: '分享',
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
      onClick: () => handleDelete(file),
    },
  ];

  return (
    <List
      className={styles.list}
      dataSource={files}
      renderItem={(file: FileItem) => (
        <List.Item
          className={styles.listItem}
          actions={[
            <Button
              type="text"
              icon={<DownloadOutlined />}
              className={styles.actionButton}
              onClick={() => handleDownload(file)}
            />,
            <Button
              type="text"
              icon={<EyeOutlined />}
              className={styles.actionButton}
              onClick={() => handlePreview(file)}
            />,
            <Dropdown menu={{ items: getMoreMenuItems(file) }} trigger={['click']}>
              <Button type="text" icon={<MoreOutlined />} className={styles.actionButton} />
            </Dropdown>,
          ]}
        >
          <List.Item.Meta
            avatar={
              <Iconfont type={getFileIconByFileName(file.name)} className={styles.fileIcon} />
            }
            title={
              <Space>
                <span className={styles.fileName}>{file.name}</span>
                {file.isAIGenerated && <Tag className={styles.aiTag}>AI生成</Tag>}
              </Space>
            }
          />
          <div className={styles.fileInfo}>
            <span className={styles.fileDate}>{file.date}</span>
            <span className={styles.fileSize}>{file.size}</span>
          </div>
        </List.Item>
      )}
    />
  );
};

export default FileList;
