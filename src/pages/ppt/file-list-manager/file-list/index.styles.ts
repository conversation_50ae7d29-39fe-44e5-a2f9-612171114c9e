import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  list: css`
    height: calc(100vh - 200px);
    overflow: auto;
  `,
  listItem: css`
    border: none;
    padding-left: 20px !important;
    border-block-end: none !important;

    &:hover {
      background-color: #fafafa;
    }

    &:last-child {
      border-bottom: none;
    }

    .ant-list-item-action {
      margin-left: auto;

      > li {
        padding: 0 4px;
      }
    }
  `,
  actionButton: css`
    &.ant-btn-text:hover {
      background-color: #f5f5f5;
    }
  `,
  avatar: css`
    font-size: 12px;
  `,
  fileName: css`
    font-size: 14px;
    color: #474a52;
  `,
  aiTag: css`
    background: linear-gradient(115deg, #52b7ff, #5e7eff 56%);
    color: #ffffff;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  `,
  fileInfo: css`
    display: flex;
    gap: 120px;
    align-items: center;
  `,
  fileDate: css`
    font-size: 14px;
    color: #8c909c;
    min-width: 80px;
  `,
  fileSize: css`
    font-size: 14px;
    color: #8c909c;
    min-width: 60px;
  `,
  fileIcon: {
    fontSize: '28px',
  },
}));
