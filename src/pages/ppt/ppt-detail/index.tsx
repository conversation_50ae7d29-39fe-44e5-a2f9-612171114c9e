import { useRef, useEffect } from 'react';
import { useSnapshot } from 'umi';
import { Modal } from 'antd';
import { isEqual } from 'lodash-es';
import useAiPPT from '@/hooks/useAiPPT';
import pptState from '@/model/pptModel';
import { useStyles } from './index.styles';

const PPTDetail = () => {
  const { styles } = useStyles();
  const containerRef = useRef<HTMLDivElement>(null);
  const { currInfo, nextInfo } = useSnapshot(pptState);
  const { createPPT, editPPT } = useAiPPT();

  useEffect(() => {
    console.log('lxy pptDetail effect --- ', { nextInfo, currInfo });
    if (isEqual(nextInfo, currInfo)) return;

    const hasNextInfo = nextInfo.pptId || nextInfo.content;
    if (!hasNextInfo) return;

    const hasCurrInfo = currInfo.pptId || currInfo.content;

    const reloadPPT = () => {
      const { type: nextType, pptId: nextPptId, content: nextContent, sessionId: nextSessionId } = nextInfo;
      if (nextType === 'create' && nextContent && nextSessionId) {
        pptState.actions.setCurrPptInfo(nextInfo);
        createPPT({ container: containerRef.current!, content: nextContent });
      }
      if (nextType === 'edit' && nextPptId) {
        pptState.actions.setCurrPptInfo(nextInfo);
        editPPT({ container: containerRef.current!, pptId: nextPptId });
      }
    };

    if (hasCurrInfo) {
      Modal.confirm({
        title: '提示',
        content: '是否要离开当前的ppt页面？',
        onOk: () => {
          reloadPPT();
        },
        onCancel: () => {
          pptState.actions.resetNextPptInfo();
        },
      });
      return;
    }
    reloadPPT();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currInfo, nextInfo]);

  return (
    <div className={styles.pptDetail}>
      {/* <div className={styles.back}>返回会话</div> */}
      <div className={styles.ppt} ref={containerRef} />
    </div>
  );
};

export default PPTDetail;
