import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  back: css`
    width: 100%;
    height: 40px;
    display: flex;
    padding-left: 24px;
    align-items: center;
    box-sizing: border-box;
    border-bottom: 1px solid #e6e8eb;
  `,
  pptDetail: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 12px;
    background: #fff;
  `,
  ppt: css`
    width: 100%;
    height: calc(100vh - 140px);

    .editor-body {
      background: #fff;
    }
  `,
}));
