import { useState, useEffect, useMemo, forwardRef, useImperativeHandle } from 'react';
import { useRequest } from 'ahooks';
import { Spin } from 'antd';
import { GetPPTConfigList } from '@/server/WuyingAI/GetPPTConfigList';
import ConfigSelect from './config-select';
import { useStyles } from './index.styles';

// 定义收集到的配置值的类型
interface CollectedConfig {
  [key: string]: string;
}

// 定义暴露给父组件的接口
export interface ConfigPanelRef {
  formattedConfigDescription: string;
  collectedConfig: CollectedConfig;
}

const ConfigPanel = forwardRef<ConfigPanelRef, { className?: string; smallMode?: boolean }>((props, ref) => {
  const { styles, cx } = useStyles();

  // 状态：收集所有ConfigSelect的选中值
  const [collectedConfig, setCollectedConfig] = useState<CollectedConfig>({});

  // 使用useRequest获取配置数据
  const { data: configData, loading } = useRequest(
    async () => {
      const ret = await GetPPTConfigList();
      return ret?.Data;
    },
    {
      onSuccess: (data) => {
        // 使用传入的data参数而不是configData
        const initialConfig: CollectedConfig = {};
        Object.keys(data).forEach((key) => {
          const config = data[key as keyof typeof data];
          if (config.Items && config.Items.length > 0) {
            initialConfig[key] = config.Items[0];
          }
        });
        setCollectedConfig(initialConfig);
      },
    },
  );

  // 处理单个配置项的值变化
  const handleConfigChange = (key: string, value: string) => {
    setCollectedConfig((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // 将items转换为ConfigSelect需要的MenuProps格式
  const convertItemsToMenuProps = (items: readonly string[], key: string) => {
    return items.map((item) => ({
      key: item,
      label: item,
      onClick: () => handleConfigChange(key, item),
    }));
  };

  // 使用useMemo生成格式化的配置描述
  const formattedConfigDescription = useMemo(() => {
    if (!collectedConfig || Object.keys(collectedConfig).length === 0 || !configData) {
      return '';
    }

    const descriptionParts: string[] = [];

    Object.keys(configData).forEach((key) => {
      const config = configData[key as keyof typeof configData];
      const selectedValue = collectedConfig[key];

      if (selectedValue && config.Title) {
        descriptionParts.push(`${config.Title}：${selectedValue}`);
      }
    });

    return descriptionParts.join('；') + '；';
  }, [collectedConfig, configData]);

  // 使用useImperativeHandle直接暴露值给父组件
  useImperativeHandle(ref, () => ({
    formattedConfigDescription,
    collectedConfig,
  }), [formattedConfigDescription, collectedConfig]);

  // 如果正在加载，显示加载状态
  if (loading || !configData) {
    return <></>;
  }

  return (
    <div className={cx(styles.configPanel, props.smallMode && styles.smallMode, props.className)}>
      {Object.keys(configData)?.map((key) => {
        const config = configData[key as keyof typeof configData];
        return (
          <ConfigSelect
            key={key}
            title={config.Title}
            items={convertItemsToMenuProps(config?.Items, key)}
            value={collectedConfig[key]}
            smallMode={props.smallMode}
            onChange={(value) => handleConfigChange(key, value)}
          />
        );
      })}
    </div>
  );
});

export default ConfigPanel;
