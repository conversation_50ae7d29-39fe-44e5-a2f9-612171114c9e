import { useControllableValue } from 'ahooks';
import { Button, Dropdown, Space, MenuProps, Menu } from 'antd';
import { Iconfont } from '@/components/icon';
import { useStyles } from './index.styles';

interface ConfigSelectProps {
  contentClassName?: string;
  value?: any;
  onChange?: (value: any) => void;
  items: MenuProps['items'];
  title: string;
  smallMode?: boolean;
}

const ConfigSelect = (props: ConfigSelectProps) => {
  const { items, title, contentClassName, smallMode = false } = props;
  const [config, setConfig] = useControllableValue(props);
  const { styles, cx } = useStyles();

  const handleMenuClick = ({ key }: { key: string }) => {
    setConfig(key);
  };

  return (
    <Dropdown
      menu={{
        items,
        onClick: handleMenuClick,
        selectable: true,
        selectedKeys: config ? [config] : [],
      }}
    >
      <div className={cx(styles.content, contentClassName, smallMode && styles.smallModeContent)}>
        {
          !smallMode && <span className={styles.title}>{title}</span>
        }
        <span className={cx(styles.value, smallMode && styles.smallModeValue)}>{config || '请选择'}</span>
        <Iconfont type="chevron--down" className={styles.arrow} />
      </div>
    </Dropdown>
  );
};

export default ConfigSelect;