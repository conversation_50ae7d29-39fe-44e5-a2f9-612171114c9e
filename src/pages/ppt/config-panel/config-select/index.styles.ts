import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ cx, css, token }) => {
  return {
    content: css`
      padding: 8px 12px;
      display: flex;
      align-items: center;
      border-radius: 12px;
      /* 填充🌞/F20 内容背景 */
      /* 样式描述：页面背景 */
      background: #FFFFFF;
      box-sizing: border-box;
      /* 线条🌞/L30 一般 边框 */
      border: 1px solid #D4D6DB;
      cursor: pointer;

      &:hover {
        border-color: #0075FF;
      }
    `,
    smallModeContent: css`
      padding: 8px 6px;
    `,
    title: css`
      color: #B8BBC2;
      margin-right: 8px;
    `,
    value: css`
      margin-right: 2px;
      color: #474A52;
      min-width: 30px;
      max-width: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex-shrink: 0;
    `,
    smallModeValue: css`
      max-width: 42px;
    `,
    arrow: css`
      margin-left: auto;
      width: 20px;
      height: 20px;
      fill: #B8BBC2;
    `,
  };
});
