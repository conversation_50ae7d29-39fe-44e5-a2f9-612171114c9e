import { Spin, notification } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import cx from 'classnames';
import ChatSender from '@/components/chat-sender';
import Welcome from '@/components/welcome';
import ChatHeader from '@/components/chat-header';
import ChatList from '@/components/chat-list';
import useChat from '@/hooks/useChat';
import { useStyles } from './index.styles';
import { AgentIdEnum } from '@/constant/enum';
import { ResourceItem } from '@/server/WuyingAI/SendMessage';
// import { getLoginInfo } from '@/utils';
import { useTabContext } from '@/contexts/TabContext';
import FileManager from '@/components/file-manager';
// import { useScript } from '@/hooks/useScript';
import useAiPPT from '@/hooks/useAiPPT';
// import FileListManager from './file-list-manager';
import pptState from '@/model/pptModel';
import ConfigPanel, { ConfigPanelRef } from './config-panel';

const Ppt: React.FC = () => {
  const { styles } = useStyles();
  const { initStatus, initPPT, openEditTab } = useAiPPT();

  const { addTab, tabVisible, hideTabs, closeTab, showTabs } = useTabContext();

  const { sendMessage, loading, messages, setMessages, resetChat, stopChat, setSessionInfo, sessionInfo } =
    useChat({
      agentId: AgentIdEnum.ppt,
    });

  // const { username } = getLoginInfo();

  // 当前执行环境
  const [selectedEnvironment, setSelectedEnvironment] = useState<{
    Name: string;
    DesktopId: string;
  } | null>(null);

  // 引用ConfigPanel组件
  const configPanelRef = useRef<ConfigPanelRef>(null);

  // ==================== Event ====================
  const onSubmit = (val: string, resources: ResourceItem[]) => {
    if (!val) return;

    if (loading) {
      notification.error({
        message: '请求正在处理中，请等待请求完成。',
      });
      return;
    }

    const valWithConfig = `${configPanelRef.current?.formattedConfigDescription} ${val}`;
    // const valWithConfig = `${val}`;
    sendMessage(valWithConfig, resources, selectedEnvironment?.DesktopId);
  };

  // ==================== Nodes ====================
  const chatList = messages?.length ? (
    <ChatList agentId={AgentIdEnum.ppt} messages={messages} loading={loading} sessionId={sessionInfo.SessionId} updateMessages={setMessages} />
  ) : (
    <Welcome text={'PPT Agent'} tip="自由创作，一键即达" />
  );

  const handleOpenTab = () => {
    if (tabVisible) {
      hideTabs();
    } else {
      showTabs();
    }
  };

  useEffect(() => {
    initPPT();
  }, []);

  useEffect(() => {
    return () => {
      // 离开ppt agent，清tabs
      closeTab('ppt-detail');
      pptState.actions.resetPptInfo();
    };
  }, [sessionInfo?.SessionId]);

  if (initStatus === 'loading') {
    return null;
  }

  // ==================== Render =================
  return (
    <div className={styles.layout}>
      <ChatHeader
        agentId={AgentIdEnum.ppt}
        selectedEnvironment={selectedEnvironment}
        onSelectEnvironment={setSelectedEnvironment}
        onNewChat={resetChat}
        handleOpenTab={handleOpenTab}
      />

      <div
        className={cx(styles.chat, {
          [styles.layoutChatting]: messages.length > 0,
        })}
      >
        {chatList}
        <div>
          <ChatSender
            onSubmit={onSubmit}
            loading={loading}
            extraHeader={<ConfigPanel ref={configPanelRef} smallMode={tabVisible} className={styles.configPanel} />}
            agentId={AgentIdEnum.ppt}
            sessionInfo={sessionInfo}
            setSessionInfo={setSessionInfo}
            stopChat={stopChat}
            placeholder={'请输入PPT主题'}
          />
        </div>
      </div>
    </div>
  );
};

export default Ppt;
