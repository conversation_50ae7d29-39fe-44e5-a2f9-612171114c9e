import React from 'react';
import { Card, List, Typography, Space, Image, Tag, Row, Col, Divider } from 'antd';
import { FileTextOutlined, FilePptOutlined } from '@ant-design/icons';
import { useStyles } from './index.styles';
import SessionArtifacts from './components/SessionFiles';
import SessionMessages from './components/SessionMessages';

const KnowledgeBaseSessionDetail: React.FC = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.container}>
      <Row style={{ height: '100%' }}>
        {/* 左侧：会话文件 */}
        <Col span={14} className={styles.leftPanel}>
          <SessionArtifacts />
        </Col>

        {/* 右侧：会话内容 */}
        <Col span={10} className={styles.rightPanel}>
          <SessionMessages />
        </Col>
      </Row>
    </div>
  );
};

export default KnowledgeBaseSessionDetail;
