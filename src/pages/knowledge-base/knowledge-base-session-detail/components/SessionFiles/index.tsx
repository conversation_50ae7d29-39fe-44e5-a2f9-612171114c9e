import React from 'react';
import { Typography, Table } from 'antd';
import { useStyles } from './index.styles';
import { ListKnowledgeBaseSessionFiles } from '@/server/WuyingAI/ListKnowledgeBaseSessionFiles';
import { useAntdTableWithToken } from '@/hooks/useAntdTableWithToken';
import { useSessionArtifactsColumns } from '@/pages/knowledge-base/components/useSessionArtifactsColumns';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';

const { Title } = Typography;

const SessionFiles: React.FC = () => {
  const { styles } = useStyles();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;
  const { sessionId } = knowledgeBaseParams;

  const columns = useSessionArtifactsColumns();

  const { tableProps } = useAntdTableWithToken(
    (params) => ListKnowledgeBaseSessionFiles({
      KbId: kbId!,
      SessionId: sessionId!,
      ...params,
    }),
    {
      defaultPageSize: 20,
      refreshDeps: [kbId, sessionId],
      ready: !!kbId && !!sessionId,
    },
  );

  return (
    <div>
      <div className={styles.panelHeader}>
        <Title level={5} className={styles.headerTitle}>
          会话文件
        </Title>
      </div>
      <Table {...tableProps} columns={columns} showHeader={false} rowKey="FileId" bordered={false} />
    </div>
  );
};

export default SessionFiles;
