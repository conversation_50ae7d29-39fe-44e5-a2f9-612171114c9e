import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  panelHeader: {
    padding: '16px 20px',
  },
  headerTitle: {
    fontSize: '14px',
    fontWeight: 500,
    color: token.colorText,
    margin: 0,
  },
  fileName: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  fileIcon: {
    width: '28px',
    height: '28px',
  },
  fileNameContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  fileNameText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#474A52',
    maxWidth: '400px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  metaText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#8C909C',
  },
}));
