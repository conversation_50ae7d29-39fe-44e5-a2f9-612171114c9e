import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    height: '624px',
    background: token.colorBgContainer,
    borderRadius: token.borderRadius,
    overflow: 'hidden',
  },
  leftPanel: {
    height: '100%',
    borderRight: `1px solid ${token.colorBorder}`,
    display: 'flex',
    flexDirection: 'column',
  },
  rightPanel: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  panelHeader: {
    padding: '16px 20px',
  },
  headerTitle: {
    fontSize: '14px',
    fontWeight: 500,
    color: token.colorText,
    margin: 0,
  },
  fileListContainer: {
    flex: 1,
    overflow: 'auto',
    padding: '0',
  },
  fileItem: {
    padding: '20px',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    '&:hover': {
      background: token.colorFillTertiary,
    },
  },
  fileInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  fileName: {
    fontSize: '14px',
    color: token.colorText,
    margin: 0,
    flex: 1,
  },
  fileMetaInfo: {
    display: 'flex',
    gap: '24px',
    fontSize: '14px',
    color: token.colorTextSecondary,
  },
  conversationContainer: {
    flex: 1,
    padding: '20px',
    overflow: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '32px',
  },
  conversationSummary: {
    fontSize: '14px',
    color: token.colorTextSecondary,
    marginTop: '8px',
    lineHeight: 1.5,
  },
  messageContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
  },
  userMessage: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
  userBubble: {
    maxWidth: '70%',
    background: token.colorFillSecondary,
    borderRadius: '16px',
    padding: '16px',
    fontSize: '14px',
    color: token.colorText,
  },
  botMessage: {
    display: 'flex',
    justifyContent: 'flex-start',
  },
  botBubble: {
    maxWidth: '85%',
    fontSize: '14px',
    color: token.colorTextSecondary,
    lineHeight: 1.5,
  },
}));
