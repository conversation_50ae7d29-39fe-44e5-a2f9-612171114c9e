import React, { useRef } from 'react';
import { Typography, Spin } from 'antd';
import { useStyles } from './index.styles';
import { ListKnowledgeBaseSessionMessages } from '@/server/WuyingAI/ListKnowledgeBaseSessionMessages';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { useInfiniteScrollWithToken } from '@/hooks/useInfiniteScrollWithToken';

const { Title } = Typography;

const SessionMessages: React.FC = () => {
  const { styles } = useStyles();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;
  const { sessionId } = knowledgeBaseParams;
  const ref = useRef<HTMLDivElement>(null);
  const { data: messages, loading } = useInfiniteScrollWithToken(
    (params) =>
      ListKnowledgeBaseSessionMessages({
        KbId: kbId!,
        SessionId: sessionId!,
        MaxResults: params.MaxResults,
        NextToken: params.NextToken,
      }).then((res) => ({
        ...res,
        Data: res.Data.reverse(),
      })),
    {
      refreshDeps: [kbId, sessionId],
      ready: !!kbId && !!sessionId,
      direction: 'top',
      target: ref,
    },
  );

  if (loading) {
    return <Spin />;
  }

  return (
    <>
      <div className={styles.panelHeader}>
        <Title level={5} className={styles.headerTitle}>
          会话内容
        </Title>
      </div>
      <div className={styles.conversationContainer} ref={ref}>
        <div className={styles.messageContainer} >
          {messages?.map((message) => (
            <div key={message.MessageId}>
              {message.MessageRole === 'user' ? (
                <div className={styles.userMessage}>
                  <div className={styles.userBubble}>{message.MessageContent}</div>
                </div>
              ) : (
                <div className={styles.botMessage}>
                  <div className={styles.botBubble}>{message.MessageContent}</div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default SessionMessages;
