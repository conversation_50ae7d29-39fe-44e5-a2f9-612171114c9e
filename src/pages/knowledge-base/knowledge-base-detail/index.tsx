import React, { useMemo, useCallback } from 'react';
import { Button, Tabs, Dropdown, Space } from 'antd';
import { ClockCircleOutlined, PlusOutlined } from '@ant-design/icons';
import RecentUpdates from './components/RecentUpdates';
import FileList from './components/FileList';
import SessionList from './components/SessionList';
import Settings from './components/Settings';
import { useStyles } from './index.styles';
import { DescribeKnowledgeBase } from '@/server/WuyingAI/DescribeKnowledgeBase';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import type { MenuProps } from 'antd';
import { Iconfont } from '@/components/icon';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useTabContext } from '@/contexts/TabContext';
import { KNOWLEDGE_BASE_TABS } from '@/constant/tabs';
import { useSnapshot } from 'valtio';
import { GetWyDrive } from '@/server/ecd20250730/GetWyDrive';

const KnowledgeBaseDetail: React.FC = () => {
  const { styles } = useStyles();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { addTab } = useTabContext();
  const { kbId } = knowledgeBaseParams;
  const detailTab = knowledgeBaseParams.detailTab || 'recentUpdates';
  const { knowledgeBaseRefresh } = useSnapshot(knowledgeBaseState);
  const { setSelectedSessions, setSessionSelectionModalOpen, setKnowledgeBaseRefresh, setSessionListRefresh, setFileListRefresh, setRecentUpdatesRefresh } = knowledgeBaseState.actions;

  const { data: knowledgeBaseData } = useRequest(
    () => {
      return DescribeKnowledgeBase({ KbId: kbId! });
    },
    {
      refreshDeps: [kbId, knowledgeBaseRefresh],
      ready: !!kbId,
      onSuccess: (data) => {
        knowledgeBaseState.actions.setKnowledgeBaseInfo(data.Data);
      },
    },
  );

  const { data: wyDriveData } = useRequest(
    () => {
      return GetWyDrive({});
    },
    {
      ready: !!kbId,
    },
  );

  const hasWyDrive = useMemo(() => {
    return !!wyDriveData?.WyDrive?.WyDriveId;
  }, [wyDriveData]);

  const handleAddDocument = useCallback(() => {
    addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseAddFiles);
  }, [addTab]);

  const handleAddSession = useCallback(() => {
    addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseAddSession);
    setSelectedSessions([]);
    setSessionSelectionModalOpen(true);
  }, [addTab, setSelectedSessions, setSessionSelectionModalOpen]);

  const handleTabChange = (key: string) => {
    knowledgeBaseState.actions.setKnowledgeBaseParams({
      detailTab: key,
    });
    setKnowledgeBaseRefresh();
    if (key === 'recentUpdates') {
      setRecentUpdatesRefresh();
    } else if (key === 'fileList') {
      setFileListRefresh();
    } else if (key === 'sessionList') {
      setSessionListRefresh();
    }
  };

  const items = useMemo(() => [
    {
      key: 'recentUpdates',
      label: '最近更新',
      children: <RecentUpdates showTitle={false} kbId={kbId} key="knowledge-base-detail" />,
    },
    {
      key: 'fileList',
      label: `文件(${knowledgeBaseData?.Data?.DocumentCount || 0})`,
      children: <FileList />,
    },
    {
      key: 'sessionList',
      label: `会话(${knowledgeBaseData?.Data?.SessionCount || 0})`,
      children: <SessionList />,
    },
    {
      key: 'settings',
      label: '设置',
      children: <Settings />,
    },
  ], [kbId, knowledgeBaseData?.Data?.DocumentCount, knowledgeBaseData?.Data?.SessionCount]);

  const dropdownItems: MenuProps['items'] = useMemo(() => [
    {
      key: '1',
      label: (
        <Space align="center">
          <Iconfont type="document--add" style={{ width: '16px', height: '16px', marginTop: '6px' }} disabled={!hasWyDrive} />
          <span>添加文件</span>
        </Space>
      ),
      onClick: handleAddDocument,
      disabled: !hasWyDrive,
    },
    {
      key: '2',
      label: (
        <Space align="center">
          <Iconfont type="chat--outline" style={{ width: '16px', height: '16px', marginTop: '6px' }} />
          <span>添加会话</span>
        </Space>
      ),
      onClick: handleAddSession,
    },
  ], [handleAddDocument, handleAddSession, hasWyDrive]);

  return (
    <div className={styles.container}>
      <div className={styles.headerContainer}>
        <div className={styles.infoContainer}>
          <div className={styles.title}>{knowledgeBaseData?.Data?.Name}</div>
          <div className={styles.detailsContainer}>

            {knowledgeBaseData?.Data?.Description && (
              <>
                <div className={styles.descriptionContainer}>
                  <div className={styles.description}>{knowledgeBaseData?.Data?.Description}</div>
                </div>
                <div className={styles.divider} />
              </>
            )}
            <div className={styles.updateInfoContainer}>
              <ClockCircleOutlined className={styles.updateIcon} />
              <div className={styles.updateText}>
                {dayjs(knowledgeBaseData?.Data?.GmtModified).format('YYYY-MM-DD HH:mm:ss')} 更新
              </div>
            </div>
          </div>
        </div>
        <Dropdown menu={{ items: dropdownItems }} placement="bottomRight">
          <Button type="primary" className={styles.addButton}>
            <PlusOutlined className={styles.addIcon} />
            <span className={styles.addText}>添加</span>
          </Button>
        </Dropdown>
      </div>
      {/* 主要内容区域 */}
      <div className={styles.content}>
        <Tabs items={items} activeKey={detailTab} onChange={handleTabChange} />
      </div>
    </div>
  );
};

export default KnowledgeBaseDetail;
