import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    borderRadius: '0px 8px 8px 8px',
    background: '#FFFFFF',
    overflow: 'hidden',
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  fileName: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  fileIcon: {
    width: '28px',
    height: '28px',
  },
  fileNameContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  fileNameText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#474A52',
    maxWidth: '400px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  aiGeneratedTag: {
    borderRadius: '4px',
    background: 'linear-gradient(115deg, #52B7FF, #5E7EFF 56%)',
    border: 'none',
    color: '#FFFFFF',
    fontSize: '12px',
    fontWeight: 500,
    padding: '2px 6px',
    lineHeight: '16px',
  },
  metaText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#8C909C',
  },
  actionButton: {
    border: 'none',
    background: 'transparent',
    cursor: 'pointer',
    padding: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionInfo: {
    position: 'absolute',
    bottom: '16px',
    left: '20px',
    right: '20px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    background: '#fff',
    padding: '8px 0',
    borderTop: '1px solid #f0f0f0',
    zIndex: 10,
  },
  selectedText: {
    fontSize: '14px',
    color: '#474A52',
  },
  removeButton: {
    borderRadius: '12px',
    background: '#F2F5FA',
    border: 'none',
    padding: '8px 12px',
    fontSize: '14px',
    fontWeight: 500,
    color: '#B8BBC2',
    cursor: 'pointer',
    '&:hover': {
      background: '#e6f0ff',
    },
    '&:disabled': {
      background: '#F2F5FA',
      cursor: 'not-allowed',
    },
  },
  actionIcon: {
    width: '16px',
    height: '16px',
    cursor: 'pointer',
    fill: '#474A52',
  },
  disabledActionIcon: {
    fill: '#B8BBC2',
  },
  footer: {
    position: 'absolute',
    bottom: '0',
    left: '0',
    right: '0',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '8px 16px',
  },
  footerLeft: {
    display: 'flex',
    alignItems: 'center',
    gap: '40px',
  },
  footerRight: {
    display: 'flex',
    alignItems: 'center',
  },
  selectAllText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#474A52',
  },
}));
