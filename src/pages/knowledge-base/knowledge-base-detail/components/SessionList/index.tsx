import React, { useMemo, useState } from 'react';
import { Table, Space, App, Checkbox, Pagination, CheckboxChangeEvent, Button } from 'antd';
import type { ColumnsType, TableRowSelection } from 'antd/es/table/interface';
import {
  ListKnowledgeBaseSessions,
  SessionItem,
} from '@/server/WuyingAI/ListKnowledgeBaseSessions';
import { useAntdTableWithToken } from '@/hooks/useAntdTableWithToken';
import { useStyles } from './index.styles';
import dayjs from 'dayjs';
import { Iconfont } from '@/components/icon';
import { DeleteKnowledgeBaseSessions } from '@/server/WuyingAI/DeleteKnowledgeBaseSessions';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { useTabContext } from '@/contexts/TabContext';
import { KNOWLEDGE_BASE_TABS } from '@/constant/tabs';
import { getFileOrSessionStatus } from '../../utils';
import { KnowledgeBaseLogStatus } from '@/server/WuyingAI/ListKnowledgeBaseLogs';

const SessionList: React.FC = () => {
  const { styles, cx } = useStyles();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const { modal, message } = App.useApp();
  const { sessionListRefresh } = useSnapshot(knowledgeBaseState);
  const { setSessionListRefresh, setKnowledgeBaseRefresh, setRecentUpdatesRefresh } = knowledgeBaseState.actions;
  const { addTab } = useTabContext();

  const { tableProps } = useAntdTableWithToken(
    (params) => ListKnowledgeBaseSessions({ KbId: kbId!, ...params }),
    {
      defaultPageSize: 20,
      refreshDeps: [kbId, sessionListRefresh],
      ready: !!kbId,
    },
  );

  const columns: ColumnsType<SessionItem> = [
    {
      title: '会话名称',
      dataIndex: 'SessionName',
      key: 'SessionName',
      width: '50%',
      render: (text: string, record: SessionItem) => (
        <div className={styles.fileName}>
          <div className={styles.fileNameContainer}>
            <span className={styles.fileNameText}>{record.SessionName}</span>
          </div>
        </div>
      ),
    },
    {
      title: '修改时间',
      dataIndex: 'GmtModified',
      key: 'GmtModified',
      width: '25%',
      render: (text: string) => (
        <span className={styles.metaText}>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'Status',
      key: 'Status',
      width: '15%',
      render: (text: KnowledgeBaseLogStatus) => <span className={styles.metaText}>{getFileOrSessionStatus(text)}</span>,
    },
    {
      title: '操作',
      key: 'action',
      render: (text: string, record: SessionItem) => {
        return (
          <Space size={20}>
            <Iconfont
              type="view--outline"
              className={styles.actionIcon}
              onClick={() => handleViewSession(record)}
            />
            <Iconfont
              type="document-move"
              className={styles.actionIcon}
              disabled={record.Status === KnowledgeBaseLogStatus.Processing}
              onClick={() => handleRemoveSelected([record?.SessionId])}
            />
          </Space>
        );
      },
    },
  ];

  const rowSelection: TableRowSelection<SessionItem> = useMemo(() => ({
    preserveSelectedRowKeys: true,
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: SessionItem) => ({
      disabled: record.Status === KnowledgeBaseLogStatus.Processing,
    }),
  }), [selectedRowKeys]);

  const handleRemoveSelected = (sessionIdList: string[]) => {
    const title =
      sessionIdList.length > 1 ? `确定要移除选中的${sessionIdList.length}个会话？` : '确定要移除该会话？';
    modal.confirm({
      title,
      content: '移除后，当前知识库将无法调用该会话。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        if (!kbId) {
          message.error('知识库ID不存在');
          return;
        }
        const res = await DeleteKnowledgeBaseSessions({
          KbId: kbId,
          SessionIdList: sessionIdList,
        });
        if (res.Code === '200') {
          message.success('移除成功');
          setSelectedRowKeys([]);
          setSessionListRefresh();
          setKnowledgeBaseRefresh();
          setRecentUpdatesRefresh();
        } else {
          message.error('移除失败');
        }
      },
    });
  };

  const handleViewSession = (record: SessionItem) => {
    knowledgeBaseState.actions.setKnowledgeBaseParams({
      sessionId: record.SessionId,
    });
    addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseSessionDetail);
  };

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      setSelectedRowKeys(tableProps.dataSource?.filter((item) => item.Status !== KnowledgeBaseLogStatus.Processing).map((item) => item.SessionId) || []);
    } else {
      setSelectedRowKeys([]);
    }
  };

  return (
    <div className={styles.container}>
      <Table<SessionItem>
        {...tableProps}
        showHeader={false}
        columns={columns}
        rowKey="SessionId"
        rowSelection={rowSelection}
        size="middle"
        scroll={{ y: '100%' }}
        pagination={false}
        bordered={false}
      />
      <div className={styles.footer}>
        <div className={styles.footerLeft}>
          <Checkbox
            onChange={handleSelectAll}
            disabled={tableProps.dataSource?.filter((item) => item.Status !== KnowledgeBaseLogStatus.Processing).length === 0}
            checked={selectedRowKeys.length > 0}
            indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < tableProps.dataSource?.filter((item) => item.Status !== KnowledgeBaseLogStatus.Processing).length}
          >
            <span className={styles.selectAllText}>
              {`已选中${selectedRowKeys.length}项`}
            </span>
          </Checkbox>
          <Button type="default" onClick={() => handleRemoveSelected(selectedRowKeys as string[])} disabled={selectedRowKeys.length === 0}>
            <Iconfont
              type="document-move"
              className={cx(styles.actionIcon, {
                [styles.disabledActionIcon]: selectedRowKeys.length === 0,
              })}
            />
            <span>移除</span>
          </Button>
        </div>
        <div className={styles.footerRight}>
          <Pagination {...tableProps.pagination} />
        </div>
      </div>
    </div>
  );
};

export default SessionList;
