import React, { useState } from 'react';
import { Table, Tag, Image, Space, App, Checkbox, Pagination, CheckboxChangeEvent, Button } from 'antd';
import type { ColumnsType, TableRowSelection } from 'antd/es/table/interface';
import {
  ListKnowledgeBaseDocuments,
  DocumentItem,
} from '@/server/WuyingAI/ListKnowledgeBaseDocuments';
import { useStyles } from './index.styles';
import dayjs from 'dayjs';
import { Iconfont } from '@/components/icon';
import { DeleteKnowledgeBaseDocuments } from '@/server/WuyingAI/DeleteKnowledgeBaseDocuments';
import { formatFileSize } from '@/utils/file';
import { useAntdTableWithToken } from '@/hooks/useAntdTableWithToken';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { GetKbFilePreviewUrl } from '@/server/WuyingAI/GetKbFilePreviewUrl';
import usePreviewUrl from '@/hooks/usePreviewUrl';
import { getFileIconByFileName } from '@/utils/file';
import { getFileOrSessionStatus } from '../../utils';
import { KnowledgeBaseLogStatus } from '@/server/WuyingAI/ListKnowledgeBaseLogs';

const FileList: React.FC = () => {
  const { styles, cx } = useStyles();
  const { knowledgeBaseParams, fileListRefresh } = useSnapshot(knowledgeBaseState);
  const { setFileListRefresh, setKnowledgeBaseRefresh, setRecentUpdatesRefresh } = knowledgeBaseState.actions;
  const { kbId } = knowledgeBaseParams;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const { modal, message } = App.useApp();
  const { previewUrl } = usePreviewUrl();

  const { tableProps } = useAntdTableWithToken(
    (params) => {
      return ListKnowledgeBaseDocuments({ KbId: kbId!, ...params });
    },
    {
      defaultPageSize: 20,
      refreshDeps: [kbId, fileListRefresh],
      ready: !!kbId,
    },
  );

  const columns: ColumnsType<DocumentItem> = [
    {
      title: '文件名',
      dataIndex: 'FileName',
      key: 'FileName',
      width: '35%',
      render: (text: string, record: DocumentItem) => (
        <div className={styles.fileName}>
          <Iconfont type={getFileIconByFileName(record.FileName)} className={styles.fileIcon} />
          <div className={styles.fileNameContainer}>
            <span className={styles.fileNameText}>{record.FileName}</span>
            {record.IsAiGenerated && <Tag className={styles.aiGeneratedTag}>AI生成</Tag>}
          </div>
        </div>
      ),
    },
    {
      title: '修改时间',
      dataIndex: 'GmtModified',
      key: 'GmtModified',
      width: '25%',
      render: (text: string) => (
        <span className={styles.metaText}>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
    {
      title: '大小',
      dataIndex: 'FileSize',
      key: 'FileSize',
      width: '15%',
      render: (text: number) => <span className={styles.metaText}>{formatFileSize(text)}</span>,
    },
    {
      title: '状态',
      dataIndex: 'Status',
      key: 'Status',
      width: '15%',
      render: (text: KnowledgeBaseLogStatus) => <span className={styles.metaText}>{getFileOrSessionStatus(text)}</span>,
    },
    {
      title: '操作',
      key: 'action',
      render: (text: string, record: DocumentItem) => {
        return (
          <Space size={20}>
            <Iconfont
              type="view--outline"
              className={styles.actionIcon}
              disabled={record.Status === KnowledgeBaseLogStatus.Processing}
              onClick={() => handlePreview(record)}
            />
            <Iconfont
              type="document-move"
              className={styles.actionIcon}
              disabled={record.Status === KnowledgeBaseLogStatus.Processing}
              onClick={() => handleRemoveSelected([record?.FileName])}
            />
          </Space>
        );
      },
    },
  ];

  const rowSelection: TableRowSelection<DocumentItem> = {
    preserveSelectedRowKeys: true,
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: DocumentItem) => ({
      disabled: record.Status === KnowledgeBaseLogStatus.Processing,
    }),
  };

  const handleRemoveSelected = (fileNameList: string[]) => {
    const title =
      fileNameList.length > 1 ? `确定要移除选中的${fileNameList.length}个文件？` : '确定要移除该文件？';
    modal.confirm({
      title,
      content: '移除后，当前知识库将无法调用该文件。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        if (!kbId) {
          message.error('知识库ID不存在');
          return;
        }
        await DeleteKnowledgeBaseDocuments({
          KbId: kbId,
          FileNameList: fileNameList,
        });
        message.success('移除成功');
        setSelectedRowKeys([]);
        setFileListRefresh();
        setKnowledgeBaseRefresh();
        setRecentUpdatesRefresh();
      },
    });
  };

  const handlePreview = async (record: DocumentItem) => {
    if (!record.DocumentId) return;
    const res = await GetKbFilePreviewUrl({
      FileId: record.DocumentId,
    });
    if (!res?.Data?.PreviewUrl) {
      message.error(res?.Message || '文件预览地址不存在');
      return;
    }
    previewUrl(res?.Data?.PreviewUrl || '', {
      label: record.FileName,
    });
  };

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      setSelectedRowKeys(tableProps.dataSource?.filter((item) => item.Status !== KnowledgeBaseLogStatus.Processing).map((item) => item.FileName) || []);
    } else {
      setSelectedRowKeys([]);
    }
  };

  return (
    <div className={styles.container}>
      <Table<DocumentItem>
        {...tableProps}
        columns={columns}
        rowKey="FileName"
        rowSelection={rowSelection}
        showHeader={false}
        size="middle"
        scroll={{ y: '100%' }}
        pagination={false}
        bordered={false}
      />
      <div className={styles.footer}>
        <div className={styles.footerLeft}>
          <Checkbox
            onChange={handleSelectAll}
            disabled={tableProps.dataSource?.filter((item) => item.Status !== KnowledgeBaseLogStatus.Processing).length === 0}
            checked={selectedRowKeys.length > 0}
            indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < tableProps.dataSource?.filter((item) => item.Status !== KnowledgeBaseLogStatus.Processing).length}
          >
            <span className={styles.selectAllText}>
              {`已选中${selectedRowKeys.length}项`}
            </span>
          </Checkbox>
          <Button type="default" onClick={() => handleRemoveSelected(selectedRowKeys as string[])} disabled={selectedRowKeys.length === 0}>
            <Iconfont
              type="document-move"
              className={cx(styles.actionIcon, {
                [styles.disabledActionIcon]: selectedRowKeys.length === 0,
              })}
            />
            <span>移除</span>
          </Button>
        </div>
        <div className={styles.footerRight}>
          <Pagination {...tableProps.pagination} />
        </div>
      </div>
    </div>
  );
};

export default FileList;
