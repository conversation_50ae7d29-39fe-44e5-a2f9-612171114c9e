import React, { useMemo, useState } from 'react';
import { Card, Typography, Button, Modal, Form, Input, App } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { UpdateKnowledgeBase, ERROR_CODE_UPDATE_KNOWLEDGE_BASE } from '@/server/WuyingAI/UpdateKnowledgeBase';
import { DeleteKnowledgeBase } from '@/server/WuyingAI/DeleteKnowledgeBase';
import { useStyles } from './index.styles';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { useTabContext } from '@/contexts/TabContext';
import { KNOWLEDGE_BASE_NAME_RULES } from '@/pages/knowledge-base/home/<USER>/CreateKnowledgeBaseModal';

const { Text } = Typography;
const { TextArea } = Input;

const Settings: React.FC = () => {
  const { styles } = useStyles();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { knowledgeBaseInfo } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;
  const { closeCurrentTab } = useTabContext();
  const { message } = App.useApp();

  const handleEdit = () => {
    form.setFieldsValue(knowledgeBaseInfo);
    setEditModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      if (!kbId) {
        message.error('知识库ID不存在');
        return;
      }
      const values = await form.validateFields();
      setConfirmLoading(true);
      const res = await UpdateKnowledgeBase({
        KbId: kbId || '',
        Name: values.Name,
        Description: values.Description,
      });
      if (res.Code === '200') {
        knowledgeBaseState.actions.setKnowledgeBaseInfo({
          ...knowledgeBaseInfo,
          ...values,
        });
        setEditModalVisible(false);
        knowledgeBaseState.actions.setKnowledgeBaseRefresh();
        message.success('知识库信息更新成功');
      } else {
        message.error(ERROR_CODE_UPDATE_KNOWLEDGE_BASE[res.Code as unknown as keyof typeof ERROR_CODE_UPDATE_KNOWLEDGE_BASE] || res.Message);
      }
    } catch (error) {
      message.error('知识库信息更新失败');
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '删除知识库',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个知识库吗？删除后无法恢复。',
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        // 处理删除逻辑
        if (!kbId) {
          message.error('知识库ID不存在');
          return;
        }
        await DeleteKnowledgeBase({ KbId: kbId || '' });
        message.success('知识库删除成功');
        closeCurrentTab();
        knowledgeBaseState.actions.setKnowledgeBaseRefresh();
      },
    });
  };

  const formInitialValues = useMemo(() => {
    return {
      Name: knowledgeBaseInfo.Name,
      Description: knowledgeBaseInfo.Description,
    };
  }, [knowledgeBaseInfo]);

  return (
    <div className={styles.container}>
      <div className={styles.settingsGrid}>
        {/* 编辑信息卡片 */}
        <Card className={styles.settingCard}>
          <div className={styles.cardContent}>
            <Text className={styles.cardTitle}>{knowledgeBaseInfo.Name}</Text>
            <Text className={styles.cardDescription}>{knowledgeBaseInfo.Description}</Text>
          </div>
          <Button className={styles.editButton} onClick={handleEdit}>
            编辑
          </Button>
        </Card>

        {/* 删除知识库卡片 */}
        <Card className={styles.deleteCard}>
          <div className={styles.cardContent}>
            <Text className={styles.cardTitle}>删除知识库</Text>
          </div>
          <Button className={styles.deleteButton} onClick={handleDelete}>
            删除
          </Button>
        </Card>
      </div>

      {/* 编辑模态框 */}
      <Modal
        title="编辑知识库信息"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        okText="保存"
        cancelText="取消"
        width={600}
        confirmLoading={confirmLoading}
      >
        <Form
          form={form}
          layout="vertical"
          className={styles.modalForm}
          initialValues={formInitialValues}
        >
          <Form.Item label="知识库名称" name="Name" rules={KNOWLEDGE_BASE_NAME_RULES}>
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            label="知识库描述"
            name="Description"
            rules={[{ max: 200, message: '描述不能超过200个字符' }]}
          >
            <TextArea rows={4} placeholder="请输入知识库描述" showCount maxLength={200} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Settings;
