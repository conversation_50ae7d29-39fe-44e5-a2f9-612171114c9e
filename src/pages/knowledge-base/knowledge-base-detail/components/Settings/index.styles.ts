import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    background: token.colorBgContainer,
    borderRadius: '0px 8px 8px 8px',
    height: '500px',
    width: '100%',
  },
  settingsGrid: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    maxWidth: '570px',
  },
  settingCard: {
    borderRadius: '12px',
    border: `1px solid ${token.colorBorder}`,
    boxShadow: 'none',
    '& .ant-card-body': {
      padding: '12px 16px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
  },
  cardContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    flex: 1,
  },
  cardTitle: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: token.colorText,
    margin: 0,
  },
  cardDescription: {
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: '16px',
    color: token.colorTextSecondary,
    margin: 0,
  },
  editButton: {
    borderRadius: '8px',
    background: token.colorFillSecondary,
    border: 'none',
    fontSize: '14px',
    fontWeight: 500,
    color: token.colorTextSecondary,
    padding: '8px 12px',
    height: '36px',
  },
  deleteCard: {
    borderRadius: '12px',
    border: `1px solid ${token.colorBorder}`,
    boxShadow: 'none',
    '& .ant-card-body': {
      padding: '12px 16px',
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'center',
      minHeight: '60px',
    },
  },
  deleteButton: {
    borderRadius: '8px',
    background: '#FFE8E8',
    border: 'none',
    fontSize: '14px',
    fontWeight: 500,
    color: '#E51A1A',
    padding: '8px 12px',
    height: '36px',
    '&:hover': {
      background: '#FFCDCD!important',
      color: '#E51A1A!important',
    },
  },
  modalForm: {
    '& .ant-form-item-label': {
      fontWeight: 500,
    },
  },
}));
