import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },

  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    boxSizing: 'border-box',
    padding: '16px 20px 0 20px',
    alignItems: 'center',
    width: '100%',
    height: '72px',
    backgroundColor: '#ffffff',
    borderRadius: '16px 16px 0 0',
    // marginBottom: '24px',
    // boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  },

  infoContainer: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    gap: '4px',
    justifyContent: 'center',
    flex: 1,
    height: '40px',
  },

  title: {
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '20px',
    color: '#1F2024',
    margin: 0,
  },

  detailsContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    gap: '12px',
    alignItems: 'center',
    height: '16px',
  },

  descriptionContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
    maxWidth: '288px',
    height: '16px',
  },

  description: {
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: '16px',
    color: '#8C909C',
    margin: 0,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },

  divider: {
    width: '1px',
    height: '8px',
    backgroundColor: '#E5E6EB',
  },

  updateInfoContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    gap: '4px',
    alignItems: 'center',
    height: '16px',
  },

  updateIcon: {
    width: '16px',
    height: '16px',
    fontSize: '16px',
    color: '#8C909C',
  },

  updateText: {
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: '16px',
    color: '#8C909C',
    margin: 0,
  },

  addButton: {
    borderRadius: '12px',
    background: '#0075FF',
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    gap: '4px',
    boxSizing: 'border-box',
    padding: '8px 12px',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: '72px',
    height: '36px',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.3s ease',

    '&:hover': {
      background: '#0066E5',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(0, 117, 255, 0.3)',
    },
  },

  addIcon: {
    fontSize: '16px',
    color: '#ffffff',
  },

  addText: {
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '20px',
    color: '#FFFFFF',
    textAlign: 'center',
    margin: 0,
  },

  content: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: '24px',
    borderRadius: '0 0 16px 16px',
    '.ant-tabs': {
      height: '100%',
    },
    '.ant-tabs-content-holder': {
      flexBasis: 0,
    },
    '.ant-tabs-content': {
      height: '100%',
    },
  },
}));
