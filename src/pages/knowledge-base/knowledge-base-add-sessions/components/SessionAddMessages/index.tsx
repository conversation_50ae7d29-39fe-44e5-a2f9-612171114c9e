import React, { useMemo, useRef, useEffect, useCallback } from 'react';
import { List, Checkbox, Typography, CheckboxChangeEvent } from 'antd';
import { useStyles } from './index.styles';

import { useRequest } from 'ahooks';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { EventItem, ListSessionHistoryMessage } from '@/server/WuyingAI/ListSessionHistoryMessage';
import { ListUserSessionKbMessage } from '@/server/WuyingAI/ListUserSessionKbMessage';
import { useInfiniteScrollWithToken } from '@/hooks/useInfiniteScrollWithToken';

const { Text } = Typography;

const SessionAddMessage: React.FC = () => {
  const { styles } = useStyles();
  const { selectedSessions, messageIds } = useSnapshot(knowledgeBaseState);
  const { setMessageIds } = knowledgeBaseState.actions;
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;
  const ref = useRef<HTMLDivElement>(null);
  const { data: messageData } = useInfiniteScrollWithToken(
    (params) => {
      return ListSessionHistoryMessage({
        SessionId: selectedSessions[0].SessionId,
        KbId: kbId!,
        MaxResults: params.MaxResults,
        NextToken: params.NextToken,
      }).then((res) => {
        return {
          Data: res.Data.Events.reverse(),
          NextToken: res.NextToken,
        };
      });
    },
    {
      ready: selectedSessions.length > 0 && !!kbId,
      onSuccess: (result) => {
        const selectedMessageIds = result.list
          .filter((item: EventItem) => item.IsInKb === true)
          .map((item: EventItem) => item.MessageId);
        const combined = Array.from(new Set([...originMessageIds, ...messageIds, ...selectedMessageIds]));
        setMessageIds(combined);
      },
      target: ref,
      direction: 'top',
    },
  );

  // 获取知识库中所有MessageIds
  const { data: kbMessageData } = useRequest(
    () => {
      return ListUserSessionKbMessage({
        SessionId: selectedSessions[0].SessionId,
        KbId: kbId!,
      });
    },
  );

  const originMessageIds = useMemo(() => {
    return kbMessageData?.Data?.MessageIds || [];
  }, [kbMessageData]);


  // 将originMessageIds合并到全量的 messageIds 中，确保后续基于完整集合进行增删
  useEffect(() => {
    if (originMessageIds.length) {
      const combined = Array.from(new Set([...messageIds, ...originMessageIds]));
      if (combined.length !== messageIds.length) {
        setMessageIds(combined);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [originMessageIds]);


  const messageItems = useMemo(() => {
    return messageData?.filter((item) => !!item.Content);
  }, [messageData]);

  const handleMessageSelect = useCallback((messageId: string, checked: boolean) => {
    let updatedIds: string[];
    if (checked) {
      // 勾选：在已有选中集合（messageIds）的基础上新增
      updatedIds = Array.from(new Set([...messageIds, messageId]));
    } else {
      // 取消勾选：从完整集合（messageIds）中移除
      updatedIds = Array.from(new Set(messageIds)).filter(
        (id) => id !== messageId,
      );
    }
    setMessageIds(updatedIds);
  }, [messageIds, setMessageIds]);

  const handleToggleAll = useCallback((e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      const allIds = Array.from(
        new Set([
          ...originMessageIds,
          ...messageIds,
          ...(messageItems?.map((item) => item.MessageId) || []),
        ]),
      );
      setMessageIds(allIds);
    } else {
      // 取消全选：移除当前列表中的所有消息
      const remainIds = messageIds.filter(
        (id) => !(messageItems?.some((item) => item.MessageId === id)),
      );
      setMessageIds(remainIds);
    }
  }, [originMessageIds, messageIds, messageItems, setMessageIds]);

  return (
    <div className={styles.rightPanel}>
      <div className={styles.panelHeader}>
        <Text className={styles.panelTitle}>会话内容（已选择 {messageIds.length} 条）</Text>
      </div>

      <div className={styles.messageList} ref={ref}>
        <List
          dataSource={messageItems}
          renderItem={(message) => (
            <List.Item className={styles.messageItem}>
              {message.Role === 'user' ? (
                <div className={styles.userMessageContainer}>
                  <Checkbox
                    checked={messageIds.includes(message.MessageId)}
                    onChange={(e) => handleMessageSelect(message.MessageId, e.target.checked)}
                    className={styles.messageCheckbox}
                  />
                  <div className={styles.userMessageBubble}>
                    <Text className={styles.messageText}>{message.Content}</Text>
                  </div>
                </div>
              ) : (
                <div className={styles.botMessageContainer}>
                  <Checkbox
                    checked={messageIds.includes(message.MessageId)}
                    onChange={(e) => handleMessageSelect(message.MessageId, e.target.checked)}
                    className={styles.messageCheckbox}
                  />
                  <Text className={styles.botMessageText}>{message.Content}</Text>
                </div>
              )}
            </List.Item>
          )}
        />
      </div>

      <div className={styles.panelFooter}>
        <Checkbox
          checked={messageItems?.length ? messageItems.every((item) => messageIds.includes(item.MessageId)) : false}
          onChange={handleToggleAll}
        >
          <span className={styles.selectAllText}>
            {messageIds.length === messageItems?.length ? '取消全选' : '全选'}
          </span>
        </Checkbox>
      </div>
    </div>
  );
};

export default SessionAddMessage;
