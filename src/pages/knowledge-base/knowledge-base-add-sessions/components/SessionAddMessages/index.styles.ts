import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    display: 'flex',
    width: '1180px',
    overflow: 'hidden',
  },

  rightPanel: {
    display: 'flex',
    flexDirection: 'column',
    // width: '450px',
    height: '100%',
    flexBasis: '35%',
  },

  panelHeader: {
    padding: '16px 20px',
    background: token.colorBgContainer,
    height: '52px',
    display: 'flex',
    alignItems: 'center',
  },

  panelTitle: {
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '20px',
    color: token.colorText,
  },

  fileList: {
    flex: 1,
    overflow: 'auto',
    background: token.colorBgContainer,
    '& .ant-list': {
      padding: 0,
    },
    '& .ant-list-item': {
      border: 'none',
      borderBottom: `1px solid ${token.colorBorderSecondary}`,
      padding: '20px',
      margin: 0,
    },
    '& .ant-list-item:hover': {
      background: token.colorFillTertiary,
    },
    '& .ant-list-item:last-child': {
      borderBottom: 'none',
    },
  },

  fileItem: {
    display: 'flex !important',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },

  fileInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    flex: 1,
  },

  fileDetails: {
    display: 'flex',
    alignItems: 'center',
  },

  fileName: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },

  fileNameText: {
    fontSize: '14px',
    color: token.colorText,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    maxWidth: '400px',
  },

  aiTag: {
    borderRadius: '4px',
    background: 'linear-gradient(115deg, #52B7FF, #5E7EFF 56%)',
    border: 'none',
    color: '#FFFFFF',
    fontSize: '12px',
    fontWeight: 500,
    padding: '2px 6px',
    lineHeight: '16px',
    margin: 0,
  },

  fileMetadata: {
    display: 'flex',
    gap: '48px',
    alignItems: 'center',
  },

  fileDate: {
    fontSize: '14px',
    color: token.colorTextTertiary,
    width: '130px',
  },

  fileSize: {
    fontSize: '14px',
    color: token.colorTextTertiary,
    width: '60px',
  },

  messageList: {
    flex: 1,
    overflow: 'auto',
    padding: '20px',
    background: token.colorBgContainer,
    '& .ant-list': {
      padding: 0,
    },
    '& .ant-list-item': {
      border: 'none',
      padding: '0 0 20px 0',
      margin: 0,
    },
    '& .ant-list-item:last-child': {
      paddingBottom: 0,
    },
  },

  messageItem: {
    display: 'flex !important',
    width: '100%',
  },

  userMessageContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    width: '100%',
    justifyContent: 'flex-end',
  },

  botMessageContainer: {
    display: 'flex',
    alignItems: 'flex-between',
    gap: '12px',
    width: '100%',
  },

  messageCheckbox: {
    alignSelf: 'flex-start',
    marginTop: '16px',
  },

  userMessageBubble: {
    borderRadius: '16px',
    background: token.colorFillSecondary,
    padding: '16px',
    maxWidth: '283px',
    wordBreak: 'break-word',
  },

  messageText: {
    fontSize: '14px',
    lineHeight: '20px',
    color: token.colorText,
  },

  botMessageText: {
    fontSize: '14px',
    lineHeight: '20px',
    color: token.colorTextSecondary,
    maxWidth: '348px',
    wordBreak: 'break-word',
    marginTop: '16px',
  },

  panelFooter: {
    padding: '16px 20px',
    display: 'flex',
    alignItems: 'center',
  },

  selectAllButton: {
    padding: 0,
    height: 'auto',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    '&:hover': {
      background: 'transparent',
    },
  },

  selectAllText: {
    fontSize: '14px',
    lineHeight: '20px',
    color: token.colorTextSecondary,
  },
}));
