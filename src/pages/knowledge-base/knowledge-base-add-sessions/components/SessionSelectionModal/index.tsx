import React, { useRef, useState } from 'react';
import { Modal, Typography, Tag, Radio, Spin } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useStyles } from './index.styles';
import { ListUserSessions } from '@/server/WuyingAI/ListUserSessions';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { Session } from '@/server/WuyingAI/ListUserSessions';
import { useInfiniteScrollWithToken } from '@/hooks/useInfiniteScrollWithToken';
import { useSnapshot } from 'valtio';

const { Text } = Typography;

interface SessionSelectionModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (selectedSessions: Session[]) => void;
  sessionList?: Session[];
}

const SessionSelectionModal: React.FC<SessionSelectionModalProps> = ({
  open,
  onCancel,
  onConfirm,
}) => {
  const { styles } = useStyles();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;
  const [tempSelectedSessions, setTempSelectedSessions] = useState<Session[]>([]);
  const { setSelectedSessions } = knowledgeBaseState.actions;
  const ref = useRef<HTMLDivElement>(null);
  const { data: listUserSessionsData, loading } = useInfiniteScrollWithToken(
    (params) =>
      ListUserSessions({
        MaxResults: params.MaxResults,
        NextToken: params.NextToken,
        KbId: kbId!,
      }).then((res) => {
        return {
          Data: res.Data.Sessions,
          NextToken: res.NextToken,
        };
      }),
    {
      target: ref,
      ready: kbId && open,
      refreshDeps: [kbId, open],
    },
  );

  const handleConfirm = () => {
    setSelectedSessions(tempSelectedSessions);
    onConfirm(tempSelectedSessions);
    setTempSelectedSessions([]);
  };

  const handleCancel = () => {
    setTempSelectedSessions([]);
    onCancel();
  };

  const selectedCount = tempSelectedSessions.length;

  const radioOptions = listUserSessionsData?.map((session) => {
    const label = (
      <div className={styles.sessionItem}>
        <div className={styles.sessionLeft}>
          {/* <Avatar size={28} icon={<UserOutlined />} className={styles.avatar} /> */}
          <div className={styles.sessionInfo}>
            <Text className={styles.sessionTitle}>{session.Title}</Text>
            {session.IsInKb && <Tag className={styles.addedTag}>已添加</Tag>}
          </div>
        </div>
        {session.IsInKb && (
          <div className={styles.tipInfo}>
            <InfoCircleOutlined className={styles.tipIcon} />
            <Text className={styles.tipText}>选择后可调整上次添加的制品与会话内容</Text>
          </div>
        )}
      </div>
    );
    return {
      label,
      value: session.SessionId,
    };
  });

  const handleRadioChange = (e: any) => {
    const val = e.target.value;
    const session = listUserSessionsData?.find(
      (session) => session.SessionId === val,
    );
    if (session) {
      setTempSelectedSessions([session]);
    }
  };

  return (
    <Modal
      title="选择会话"
      open={open}
      onCancel={handleCancel}
      width={625}
      className={styles.modal}
      cancelText="取消"
      okText="确定"
      onOk={handleConfirm}
      okButtonProps={{
        disabled: selectedCount === 0,
      }}
    >
      <div className={styles.content}>
        {/* 信息提示框 */}
        <div className={styles.infoBox}>
          <InfoCircleOutlined className={styles.infoIcon} />
          <Text className={styles.infoText}>
            选择要添加到当前知识库的历史大模型会话，这些会话将成为知识库的一部分。
          </Text>
        </div>
        {/* 会话列表 */}
        {
          loading ? (
            <Spin spinning />
          ) : (
            <div className={styles.sessionList} ref={ref}>
              <Radio.Group
                value={tempSelectedSessions[0]?.SessionId}
                options={radioOptions}
                onChange={handleRadioChange}
              />
            </div>
          )
        }
      </div>
    </Modal>
  );
};

export default SessionSelectionModal;
