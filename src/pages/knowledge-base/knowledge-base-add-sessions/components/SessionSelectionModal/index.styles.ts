import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  modal: {
    '& .ant-modal-header': {
      borderBottom: 'none',
      paddingBottom: '0',
    },
    '& .ant-modal-title': {
      fontSize: '18px',
      fontWeight: 500,
      lineHeight: '24px',
      color: token.colorText,
    },
  },

  content: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    minHeight: '400px',
    maxHeight: '500px',
  },

  infoBox: {
    borderRadius: '12px',
    background: '#EAF3FF',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    padding: '12px',
    marginBottom: '8px',
  },

  infoIcon: {
    fontSize: '16px',
    color: token.colorPrimary,
    flexShrink: 0,
  },

  infoText: {
    fontSize: '14px',
    lineHeight: '20px',
    color: token.colorTextSecondary,
  },

  sessionList: {
    overflow: 'auto',
    height: '100%',
    '.ant-radio-group': {
      display: 'flex',
      flexDirection: 'column',
      gap: '12px',
    },
    '.ant-radio-label': {
      flex: 1,
    },
  },

  sessionItem: {
    display: 'flex !important',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },

  sessionLeft: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    flex: 1,
  },

  avatar: {
    flexShrink: 0,
  },

  sessionInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    flex: 1,
  },

  sessionTitle: {
    fontSize: '14px',
    lineHeight: '20px',
    color: token.colorText,
  },

  addedTag: {
    borderRadius: '4px',
    background: '#009244',
    border: 'none',
    color: '#FFFFFF',
    fontSize: '12px',
    fontWeight: 500,
    padding: '2px 6px',
    lineHeight: '16px',
    margin: 0,
  },

  tipInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    marginLeft: '12px',
  },

  tipIcon: {
    fontSize: '16px',
    color: token.colorTextTertiary,
  },

  tipText: {
    fontSize: '12px',
    lineHeight: '16px',
    color: token.colorTextTertiary,
  },

  footer: {
    display: 'flex',
    justifyContent: 'flex-end',
    padding: '20px 0',
    borderTop: `1px solid ${token.colorBorderSecondary}`,
    marginTop: '16px',
  },

  cancelButton: {
    borderRadius: '12px',
    background: token.colorFillSecondary,
    border: 'none',
    fontSize: '16px',
    fontWeight: 500,
    color: token.colorTextSecondary,
    padding: '10px 14px',
    height: '44px',
    width: '92px',
    '&:hover': {
      background: token.colorFillTertiary,
      color: token.colorTextSecondary,
    },
  },

  confirmButton: {
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: 500,
    padding: '10px 14px',
    height: '44px',
    width: '92px',
  },
}));
