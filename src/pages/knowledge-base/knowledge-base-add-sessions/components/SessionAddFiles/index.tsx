import React, { useEffect, useMemo, useCallback } from 'react';
import { Typography, Table, Checkbox, CheckboxChangeEvent } from 'antd';
import { useStyles } from './index.styles';
import { ListUserSessionArtifacts } from '@/server/WuyingAI/ListUserSessionArtifacts';
import { useAntdTableWithToken } from '@/hooks/useAntdTableWithToken';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { useSessionArtifactsColumns } from '@/pages/knowledge-base/components/useSessionArtifactsColumns';
import { useRequest } from 'ahooks';
import { ListUserSessionKbFile } from '@/server/WuyingAI/ListUserSessionKbFile';

const { Text } = Typography;

const SessionAddFile: React.FC = () => {
  const { styles } = useStyles();
  const { selectedSessions, fileIds } = useSnapshot(knowledgeBaseState);
  const { setFileIds } = knowledgeBaseState.actions;
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { kbId } = knowledgeBaseParams;

  const rowSelection = useMemo(() => {
    return {
      preserveSelectedRowKeys: true,
      selectedRowKeys: fileIds,
      onChange: (selectedRowKeys: React.Key[]) => {
        setFileIds(selectedRowKeys.map((key) => key.toString()));
      },
    };
  }, [fileIds, setFileIds]);

  const { tableProps } = useAntdTableWithToken(
    (params) =>
      ListUserSessionArtifacts({
        SessionId: selectedSessions[0].SessionId,
        ArtifactTypes: ['artifactFile', 'sessionFile'],
        KbId: kbId!,
        ...params,
      }).then((res) => {
        return {
          Data: res.Data,
          TotalCount: res.TotalCount || 0,
          NextToken: res.NextToken,
          Code: res.Code,
        };
      }),
    {
      defaultPageSize: 100, // 默认100条
      ready: selectedSessions.length > 0 && !!kbId,
      onSuccess: (result) => {
        const selectedFileIds = result.Data
          .filter((item) => item.IsInKb === true)
          .map((item) => item.ArtifactId);
        const combined = Array.from(new Set([...(originFileIds || []), ...fileIds, ...selectedFileIds]));
        setFileIds(combined);
      },
    },
  );

  const { data: kbFileData } = useRequest(() => {
    return ListUserSessionKbFile({
      SessionId: selectedSessions[0].SessionId,
      KbId: kbId!,
    });
  });

  const originFileIds = useMemo(() => {
    return kbFileData?.Data?.FileIds || [];
  }, [kbFileData]);

  // 将 originFileIds 合并进全量 fileIds，确保用户勾选/取消时基于完整集合计算
  useEffect(() => {
    if (originFileIds.length) {
      const combined = Array.from(new Set([...(fileIds || []), ...originFileIds]));
      if (combined.length !== fileIds.length) {
        setFileIds(combined);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [originFileIds]);

  const columns = useSessionArtifactsColumns();

  const selectedCount = useMemo(() => fileIds.length, [fileIds]);

  const handleToggleAll = useCallback((e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      setFileIds(tableProps.dataSource?.map((item) => item.ArtifactId) || []);
    } else {
      setFileIds([]);
    }
  }, [tableProps.dataSource, setFileIds]);

  return (
    <div className={styles.leftPanel}>
      <div className={styles.panelHeader}>
        <Text className={styles.panelTitle}>会话文件（已选择 {fileIds.length || 0} 个）</Text>
      </div>
      <Table
        {...tableProps}
        columns={columns}
        rowKey="ArtifactId"
        pagination={false}
        showHeader={false}
        rowSelection={rowSelection as any}
        bordered={false}
      />
      {/* 底部操作栏 */}
      {
        tableProps.dataSource?.length > 0 && (
          <div className={styles.footer}>
            <Checkbox
              checked={selectedCount > 0}
              onChange={handleToggleAll}
              indeterminate={selectedCount > 0 && selectedCount < tableProps.dataSource?.length}
            >
              <span>
                {selectedCount > 0 ? '取消全选' : '全选'}
              </span>
            </Checkbox>
          </div>
        )
      }
    </div>
  );
};

export default SessionAddFile;
