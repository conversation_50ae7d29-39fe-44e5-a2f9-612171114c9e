import React, { useState } from 'react';
import { Button, Typography, App } from 'antd';
import { useStyles } from './index.styles';
import SessionSelectionModal from './components/SessionSelectionModal';
import SessionAdd from './components/SessionAdd';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { CreateKnowledgeBaseSession } from '@/server/WuyingAI/CreateKnowledgeBaseSession';
import { useTabContext } from '@/contexts/TabContext';
import { UpdateKnowledgeBaseSession } from '@/server/WuyingAI/UpdateKnowledgeBaseSession';
import { KNOWLEDGE_BASE_TABS } from '@/constant/tabs';
import { Iconfont } from '@/components/icon';

const { Text } = Typography;

const EmptyContent = () => {
  const { styles } = useStyles();
  return (
    <div className={styles.emptyState}>
      <Iconfont type="ILL-FOLDER" className={styles.emptyIcon} />
      <Text className={styles.emptyText}>暂无会话，请添加</Text>
    </div>
  );
};

const KnowledgeBaseAddSessions: React.FC = () => {
  const { styles } = useStyles();
  const { knowledgeBaseParams, sessionSelectionModalOpen } = useSnapshot(knowledgeBaseState);
  const { message } = App.useApp();
  const { kbId } = knowledgeBaseParams;
  const { closeCurrentTab, addTab } = useTabContext();
  const [addSessionLoading, setAddSessionLoading] = useState(false);

  const {
    fileIds,
    messageIds,
    selectedSessions,
  } = useSnapshot(knowledgeBaseState);
  const {
    setSessionListRefresh,
    setRecentUpdatesRefresh,
    setKnowledgeBaseRefresh,
    setSelectedSessions,
    setFileIds,
    setMessageIds,
    setKnowledgeBaseParams,
    setSessionSelectionModalOpen,
  } = knowledgeBaseState.actions;

  const handleCancel = () => {
    closeCurrentTab();
    setSelectedSessions([]);
    setFileIds([]);
    setMessageIds([]);
  };

  const handleSelectSessions = () => {
    setSessionSelectionModalOpen(true);
  };

  const handleAddSessions = async () => {
    try {
      setAddSessionLoading(true);
      const isInKb = selectedSessions[0].IsInKb;
      const service = isInKb ? UpdateKnowledgeBaseSession : CreateKnowledgeBaseSession;
      const params = {
        KbId: kbId!,
        SessionId: selectedSessions[0].SessionId,
        FileIdList: fileIds.slice().filter(Boolean),
        MessageIdList: messageIds.slice().filter(Boolean),
      };
      const res = await service(params);
      if (res.Code === '200') {
        message.success(isInKb ? '更新成功' : '添加成功');
        setSessionListRefresh();
        setRecentUpdatesRefresh();
        setKnowledgeBaseRefresh();
        closeCurrentTab();
        setFileIds([]);
        setMessageIds([]);
        setKnowledgeBaseParams({
          detailTab: 'recentUpdates',
        });
        addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseDetail); // 切换到知识库详情tab。防止之前关闭过tab，导致无法切换到详情tab，使用addTab切换
      } else {
        message.error(isInKb ? '更新失败' : '添加失败');
      }
    } finally {
      setAddSessionLoading(false);
    }
  };

  const handleModalCancel = () => {
    setSessionSelectionModalOpen(false);
  };

  const handleModalConfirm = () => {
    setSessionSelectionModalOpen(false);
  };

  return (
    <div className={styles.container}>
      <div className={styles.selectionPanel}>
        {/* 头部 */}
        <div className={styles.header}>
          <div className={styles.title}>
            <Text className={styles.titleText}>
              {selectedSessions.length > 0 ? '请确认要添加的会话文件与内容' : '请先选择会话'}
            </Text>
          </div>
          <div className={styles.actionButtons}>
            <Button className={styles.cancelButton} onClick={handleCancel}>
              取消
            </Button>
            {selectedSessions.length > 0 ? (
              <Button
                type="primary"
                className={styles.confirmButton}
                onClick={handleAddSessions}
                disabled={fileIds.length === 0 && messageIds.length === 0}
                loading={addSessionLoading}
              >
                添加
              </Button>
            ) : (
              <Button
                type="primary"
                className={styles.confirmButton}
                onClick={handleSelectSessions}
              >
                选择会话
              </Button>
            )}
          </div>
        </div>
        {/* 内容区域 */}
        <div className={styles.content}>
          {selectedSessions.length > 0 ? <SessionAdd /> : <EmptyContent />}
        </div>
      </div>
      {/* 会话选择弹窗 */}
      <SessionSelectionModal
        open={sessionSelectionModalOpen}
        onCancel={handleModalCancel}
        onConfirm={handleModalConfirm}
      />
    </div>
  );
};

export default KnowledgeBaseAddSessions;
