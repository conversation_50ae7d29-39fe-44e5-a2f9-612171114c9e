import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    height: '100%',
  },

  selectionPanel: {
    borderRadius: '16px',
    background: token.colorBgContainer,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    overflow: 'hidden',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
    height: '100%',
  },

  header: {
    background: token.colorBgContainer,
    borderBottom: `1px solid ${token.colorBorder}`,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '16px 20px',
    width: '100%',
    height: '68px',
    boxSizing: 'border-box',
  },

  title: {
    display: 'flex',
    alignItems: 'center',
    height: '20px',
  },

  titleText: {
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '20px',
    color: token.colorText,
  },

  actionButtons: {
    display: 'flex',
    gap: '8px',
    height: '36px',
  },

  cancelButton: {
    borderRadius: '12px',
    background: token.colorFillSecondary,
    border: 'none',
    fontSize: '14px',
    fontWeight: 500,
    color: token.colorTextSecondary,
    padding: '8px 12px',
    height: '36px',
    width: '80px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '&:hover': {
      background: token.colorFillTertiary,
      color: token.colorTextSecondary,
    },
  },

  confirmButton: {
    borderRadius: '12px',
    background: token.colorPrimary,
    fontSize: '14px',
    fontWeight: 500,
    padding: '8px 12px',
    height: '36px',
    width: '80px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: 'none',
  },

  content: {
    display: 'flex',
    width: '100%',
    flex: 1,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },

  emptyState: {
    display: 'flex',
    flexDirection: 'column',
    gap: '5px',
    justifyContent: 'center',
    alignItems: 'center',
    width: '112px',
    height: '105px',
  },

  emptyIcon: {
    width: '80px',
    height: '80px',
    objectFit: 'contain',
  },

  emptyText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: token.colorTextTertiary,
    textAlign: 'center',
    width: '112px',
  },
}));
