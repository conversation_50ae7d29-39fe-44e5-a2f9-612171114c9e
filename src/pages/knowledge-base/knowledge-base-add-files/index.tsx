import React, { useState, useMemo, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Button,
  Alert,
  Table,
  Space,
  Typography,
  Tag,
  Divider,
  App,
  Checkbox,
  CheckboxChangeEvent,
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useAntdTable } from 'ahooks';
import type { ColumnsType } from 'antd/es/table';
import { useStyles } from './index.styles';
import { getFileIconByFileName } from '@/utils/file';
import { Iconfont } from '@/components/icon';
import { DescribeWyDriveFiles, FileData } from '@/server/ecd/DescribeWyDriveFiles';
import { CreateKnowledgeBaseDocument } from '@/server/WuyingAI/CreateKnowledgeBaseDocument';
import { ListKnowledgeBaseDocuments } from '@/server/WuyingAI/ListKnowledgeBaseDocuments';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { useTabContext } from '@/contexts/TabContext';
import { KNOWLEDGE_BASE_TABS } from '@/constant/tabs';

const { Text } = Typography;

interface FileItem {
  id: string;
  name: string;
  type: 'FILE' | 'FOLDER';
  isAiGenerated?: boolean;
}

// 文件冲突对话框内容组件
interface FileConflictContentProps {
  conflictFiles: FileItem[];
}

const FileConflictContent: React.FC<FileConflictContentProps> = ({ conflictFiles }) => {
  const { styles } = useStyles();

  return (
    <div className={styles.modalContent}>
      {/* 警告提示框 */}
      <div className={styles.warningBox}>
        <InfoCircleOutlined className={styles.warningIcon} />
        <span className={styles.warningText}>
          以下文件在当前知识库中已存在。您可以选择覆盖当前知识库中的同名文件。也可以取消添加，并对文件进行重命名，再进行添加。
        </span>
      </div>

      {/* 文件列表 */}
      <div className={styles.conflictFileList}>
        {conflictFiles.map((file, index) => (
          <div
            key={file.id}
            className={`${styles.conflictFileItem} ${
              index < conflictFiles.length - 1 ? styles.conflictFileItemBorder : ''
            }`}
          >
            <div className={styles.conflictFileInfo}>
              <Iconfont type={getFileIconByFileName(file.name)} className={styles.conflictFileIcon} />
              <span className={styles.conflictFileName}>{file.name}</span>
            </div>
            <span className={styles.conflictFileSize}>2.5 MB</span>
          </div>
        ))}
      </div>
    </div>
  );
};

// 转换API数据到FileItem格式
const transformFileData = (files: FileData[]): FileItem[] => {
  return files?.map((file) => ({
    id: file.FileId,
    name: file.FileName,
    type: file.FileType,
    isAiGenerated: file.Tags?.some((tag) => tag.TagDesc === 'AI生成' || tag.TagCode === 'AI_PRODUCT'),
  })) || [];
};

const KnowledgeBaseAddFiles: React.FC = () => {
  const { styles } = useStyles();
  const { message, modal } = App.useApp();
  const { knowledgeBaseParams } = useSnapshot(knowledgeBaseState);
  const { setRecentUpdatesRefresh, setKnowledgeBaseRefresh, setFileListRefresh } = knowledgeBaseState.actions;
  const { kbId } = knowledgeBaseParams;
  const { closeCurrentTab, addTab } = useTabContext();
  const [addFileLoading, setAddFileLoading] = useState(false);

  const [showAlert, setShowAlert] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<FileItem[]>([]);

  // 当前所在的父级文件夹 ID（undefined 表示根目录）
  const [parentFolderId, setParentFolderId] = useState<string>();
  // 面包屑路径，默认根目录
  const [breadcrumbPath, setBreadcrumbPath] = useState<Array<{ name: string; id?: string }>>([
    { name: '我的文件', id: undefined },
  ]);

  // 获取表格数据的函数
  const getTableData = useCallback(async ({ current, pageSize }: { current: number; pageSize: number }) => {
    const response = await DescribeWyDriveFiles({
      PageSize: pageSize,
      PageNumber: current,
      ProductType: 'CloudDesktop',
      ParentFolderId: parentFolderId,
    });
    const transformedFiles = transformFileData(response.Files);
    return {
      total: response.TotalCount || transformedFiles.length,
      list: transformedFiles,
    };
  }, [parentFolderId]);

  // 使用 useAntdTable
  const { tableProps, loading } = useAntdTable(getTableData, {
    defaultPageSize: 20,
    refreshDeps: [parentFolderId],
  });

  // 表格列配置
  const columns: ColumnsType<FileItem> = useMemo(() => [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: FileItem) => (
        <Space size={8}>
          <Iconfont type={getFileIconByFileName(name, record.type)} className={styles.fileIcon} />
          <div className={styles.fileName}>
            <Text className={styles.fileNameText}>{name}</Text>
            {record.isAiGenerated && <Tag className={styles.aiTag}>AI生成</Tag>}
          </div>
        </Space>
      ),
    },
  ], [styles]);

  const rowSelection = useMemo(() => ({
    preserveSelectedRowKeys: true,
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: FileItem[]) => {
      setSelectedRowKeys(keys as string[]);
      setSelectedRows(rows);
    },
    getCheckboxProps: (record: FileItem) => ({
      disabled: record.type === 'FOLDER',
    }),
  }), [selectedRowKeys]);

  const handleCancel = useCallback(() => {
    closeCurrentTab();
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, [closeCurrentTab]);

  const handleEnterFolder = useCallback((folder: FileItem) => {
    if (folder.type !== 'FOLDER') return;
    setParentFolderId(folder.id);
    setBreadcrumbPath((prev) => [...prev, { name: folder.name, id: folder.id }]);
  }, []);

  const handleBreadcrumbClick = useCallback((index: number) => {
    if (index === breadcrumbPath.length - 1) return; // 点击当前目录无效
    const target = breadcrumbPath[index];
    setParentFolderId(target.id);
    setBreadcrumbPath(breadcrumbPath.slice(0, index + 1));
  }, [breadcrumbPath]);

  const handleBack = useCallback(() => {
    if (breadcrumbPath.length <= 1) return; // 已经是根目录
    setBreadcrumbPath((prev) => {
      const newPath = prev.slice(0, prev.length - 1);
      const last = newPath[newPath.length - 1];
      setParentFolderId(last.id);
      return newPath;
    });
  }, [breadcrumbPath]);

  const handleAdd = useCallback(async () => {
    try {
      if (!kbId) {
        message.error('知识库ID不存在');
        return;
      }
      const handleCreateKnowledgeBaseDocument = async () => {
        try {
          setAddFileLoading(true);
          const response = await CreateKnowledgeBaseDocument({
            KbId: kbId!,
            DocumentList: selectedRows.map((file) => ({
              FileName: file.name,
              FileId: file.id,
            })),
          });
          if (response.Code === '200') {
            message.success('添加成功');
            setRecentUpdatesRefresh();
            setKnowledgeBaseRefresh();
            setFileListRefresh();
            closeCurrentTab();
            addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseDetail);
          } else {
            message.error(response.Message);
          }
        } finally {
          setAddFileLoading(false);
        }
      };
      const checkConflictFiles = async () => {
        const fileNameList = selectedRows.map((file) => file.name);
        if (fileNameList.length === 0) {
          message.error('请选择文件');
          return;
        }
        if (fileNameList.length > 200) {
          message.error('最多添加200个文件');
          return;
        }
        // 判断fileNameList里是否存在重复的文件名，并返回重复的文件名
        const duplicateFileName = fileNameList.filter((fileName, index) => fileNameList.indexOf(fileName) !== index);
        if (duplicateFileName.length > 0) {
          message.error(`文件名 ${duplicateFileName.join('、')} 重复，无法添加`);
          return;
        }
        const listKnowledgeBaseDocumentsResponse = await ListKnowledgeBaseDocuments({
          KbId: kbId!,
          FileNameList: fileNameList,
          MaxResults: fileNameList.length, // 和fileNameList.length保持一致
        });
        if (listKnowledgeBaseDocumentsResponse.Code === '200') {
          const conflictFiles = listKnowledgeBaseDocumentsResponse.Data?.map((item) => ({
            id: item.DocumentId,
            name: item.FileName,
            type: 'FILE' as const,
            isAiGenerated: item.IsAiGenerated,
          }));
          if (conflictFiles.length > 0) {
            modal.confirm({
              title: '检测到有同名文件',
              icon: null,
              width: 600,
              className: 'file-conflict-modal',
              content: <FileConflictContent conflictFiles={conflictFiles} />,
              okText: `全部覆盖(${conflictFiles.length}个文件)`,
              cancelText: '取消添加',
              onOk: () => {
                handleCreateKnowledgeBaseDocument();
              },
            });
          } else {
            handleCreateKnowledgeBaseDocument();
          }
        }
      };
      checkConflictFiles();
    } finally {
      setAddFileLoading(false);
    }
  }, [kbId, selectedRows, message, setRecentUpdatesRefresh, setKnowledgeBaseRefresh, setFileListRefresh, closeCurrentTab, addTab, modal]);

  // 当前页选中数量
  const currentPageSelectedCount = useMemo(() => {
    return tableProps.dataSource?.filter((item) => selectedRowKeys.includes(item.id)).length || 0;
  }, [tableProps.dataSource, selectedRowKeys]);

  // 当前页文件数据
  const currentPageFileData = useMemo(() => tableProps.dataSource?.filter((item) => item.type === 'FILE') || [], [tableProps.dataSource]);

  // 当前页文件数量
  const currentPageFileCount = useMemo(() => currentPageFileData.length, [currentPageFileData]);

  const handleToggleAll = useCallback((e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      setSelectedRowKeys(currentPageFileData.map((item) => item.id));
    } else {
      setSelectedRowKeys([]);
    }
  }, [currentPageFileData, setSelectedRowKeys]);

  return (
    <div className={styles.container}>
      {/* 顶部导航栏 */}
      <div className={styles.header}>
        <div className={styles.breadcrumbContainer}>
          <Iconfont type="undo" className={styles.folderIcon} onClick={handleBack} />
          <Breadcrumb
            separator="/"
            items={breadcrumbPath.map((item, index) => ({
              title: (
                <span
                  className={index === breadcrumbPath.length - 1 ? styles.currentFolder : styles.breadcrumbItem}
                  style={{ cursor: index === breadcrumbPath.length - 1 ? 'default' : 'pointer' }}
                  onClick={() => handleBreadcrumbClick(index)}
                >
                  {item.name}
                </span>
              ),
            }))}
          />
        </div>

        <Space size={8}>
          <Button className={styles.cancelButton} onClick={handleCancel}>
            取消
          </Button>
          <Button
            type="primary"
            className={styles.addButton}
            onClick={handleAdd}
            disabled={selectedRowKeys.length === 0}
            loading={addFileLoading}
          >
            添加
          </Button>
        </Space>
      </div>

      <Divider className={styles.headerDivider} />

      {/* 提示信息栏 */}
      {showAlert && (
        <div className={styles.alertContainer}>
          <Alert
            message="只支持从无影文件夹添加文件，如需使用本地文件，请先上传到无影文件夹。"
            type="info"
            showIcon
            closable
            onClose={() => setShowAlert(false)}
            className={styles.alert}
          />
        </div>
      )}

      {/* 文件列表 */}
      <div className={styles.tableContainer}>
        <Table<FileItem>
          {...tableProps}
          columns={columns}
          rowKey="id"
          rowSelection={rowSelection}
          loading={loading}
          showHeader={false}
          onRow={(record) => ({
            onDoubleClick: () => handleEnterFolder(record),
          })}
          pagination={{
            showSizeChanger: false,
            ...tableProps.pagination,
          }}
          scroll={{ y: '500px' }}
          bordered={false}
        />
      </div>

      {/* 底部操作栏 */}
      {
        tableProps.dataSource?.length > 0 && (
          <div className={styles.footer}>
            <Checkbox
              checked={currentPageSelectedCount > 0}
              onChange={handleToggleAll}
              indeterminate={currentPageSelectedCount > 0 && currentPageSelectedCount < currentPageFileCount}
            >
              <span>
                {currentPageSelectedCount > 0 ? '取消全选' : '全选'}
              </span>
            </Checkbox>
          </div>
        )
      }
    </div>
  );
};

export default KnowledgeBaseAddFiles;
