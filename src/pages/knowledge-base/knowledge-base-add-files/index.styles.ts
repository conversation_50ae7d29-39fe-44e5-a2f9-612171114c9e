import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    borderRadius: '16px',
    background: token.colorBgContainer,
    overflow: 'hidden',
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },

  header: {
    background: token.colorBgContainer,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '16px 20px',
    height: '68px',
  },

  breadcrumbContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },

  folderIcon: {
    width: '20px',
    height: '20px',
    color: token.colorTextSecondary,
    cursor: 'pointer',
  },

  currentFolder: {
    color: `${token.colorTextTertiary} !important`,
  },

  breadcrumbItem: {
    color: token.colorText,
    '&:hover': {
      color: token.colorPrimary,
    },
  },

  cancelButton: {
    borderRadius: '12px',
    background: token.colorFillSecondary,
    border: 'none',
    fontSize: '14px',
    fontWeight: 500,
    color: token.colorTextSecondary,
    padding: '8px 12px',
    height: '36px',
    width: '80px',
  },

  addButton: {
    borderRadius: '12px',
    fontSize: '14px',
    fontWeight: 500,
    padding: '8px 12px',
    height: '36px',
    width: '80px',
  },

  headerDivider: {
    margin: 0,
    borderColor: token.colorBorder,
  },

  alertContainer: {
    padding: '12px 20px',
  },

  alert: {
    borderRadius: '12px',
    '& .ant-alert-message': {
      fontSize: '14px',
      color: token.colorTextSecondary,
    },
  },

  fileItem: {
    display: 'flex !important',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },

  fileInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    flex: 1,
  },

  fileDetails: {
    display: 'flex',
    alignItems: 'center',
  },

  fileName: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },

  fileNameText: {
    fontSize: '14px',
    color: token.colorText,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    maxWidth: '400px',
  },

  aiTag: {
    borderRadius: '4px',
    background: 'linear-gradient(115deg, #52B7FF, #5E7EFF 56%)',
    border: 'none',
    color: '#FFFFFF',
    fontSize: '12px',
    fontWeight: 500,
    padding: '2px 6px',
    lineHeight: '16px',
    margin: 0,
  },

  editIcon: {
    fontSize: '16px',
    color: token.colorTextSecondary,
    cursor: 'pointer',
    '&:hover': {
      color: token.colorPrimary,
    },
  },

  footer: {
    // position: 'absolute',
    // bottom: '28px',
    background: token.colorBgContainer,
    padding: '16px 20px',
    height: '68px',
    display: 'flex',
    alignItems: 'center',
  },

  selectedCount: {
    fontSize: '14px',
    color: token.colorTextSecondary,
    marginLeft: '8px',
  },

  // Modal 相关样式
  modalContent: {
    padding: '0',
  },

  warningBox: {
    borderRadius: '12px',
    background: '#FFE9BD',
    padding: '12px',
    display: 'flex',
    gap: '4px',
    marginBottom: '16px',
  },

  warningIcon: {
    fontSize: '16px',
    color: '#FF8F1F',
    marginTop: '2px',
  },

  warningText: {
    fontSize: '14px',
    lineHeight: '20px',
    color: '#474A52',
  },

  conflictFileList: {
    marginBottom: '20px',
  },

  conflictFileItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 0',
  },

  conflictFileItemBorder: {
    borderBottom: '1px solid #E6E8EB',
  },

  conflictFileInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },

  conflictFileName: {
    fontSize: '14px',
    color: '#474A52',
  },

  conflictFileSize: {
    fontSize: '14px',
    color: '#474A52',
  },

  conflictFileIcon: {
    width: '28px',
    height: '28px',
  },

  fileIcon: {
    width: '28px',
    height: '28px',
  },

  tableContainer: {
    flex: 1,
    padding: '0 20px',
  },
}));
