import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { createStyles } from 'antd-style';
import { FileItem } from '@/server/WuyingAI/ListKnowledgeBaseSessionFiles';
import { formatFileSize, getFileIconByFileName } from '@/utils/file';
import { Iconfont } from '@/components/icon';

export const useStyles = createStyles(({ token }) => ({
  fileName: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  fileIcon: {
    width: '28px',
    height: '28px',
  },
  fileNameContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  fileNameText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#474A52',
    maxWidth: '400px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  metaText: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: '#8C909C',
  },
}));

export const useSessionArtifactsColumns = () => {
  const { styles } = useStyles();
  const col: ColumnsType<FileItem> = [
    {
      title: '文件名',
      dataIndex: 'FileName',
      key: 'FileName',
      width: '60%',
      render: (text: string, record: FileItem) => (
        <div className={styles.fileName}>
          <Iconfont type={getFileIconByFileName(record.FileName)} className={styles.fileIcon} />
          <div className={styles.fileNameContainer}>
            <span className={styles.fileNameText}>{text}</span>
          </div>
        </div>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'GmtModified',
      key: 'GmtModified',
      width: '25%',
      render: (text: string) => (
        <span className={styles.metaText}>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'FileSize',
      key: 'FileSize',
      render: (text: number) => <span className={styles.metaText}>{formatFileSize(text)}</span>,
    },
  ];
  return col;
};
