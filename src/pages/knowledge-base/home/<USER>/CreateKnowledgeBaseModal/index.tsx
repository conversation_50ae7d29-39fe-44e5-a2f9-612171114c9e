import React from 'react';
import { Modal, Form, Input } from 'antd';
import { useStyles } from './index.styles';

interface CreateKnowledgeBaseProps {
  onCancel?: () => void;
  onCreate?: (data: { name: string; description: string }) => void;
  open?: boolean;
}

export const KNOWLEDGE_BASE_NAME_RULES = [
  { required: true, message: '请输入知识库名称' },
  { whitespace: true, message: '请输入知识库名称' },
  { max: 128, message: '知识库名称最多128个字符' },
  {
    pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s_-]*$/,
    message: '知识库名称不支持特殊字符，仅支持中英文、数字、空格、下划线和连字符', // TODO 和后端保持一致
  },
];

const CreateKnowledgeBase: React.FC<CreateKnowledgeBaseProps> = ({ onCancel, onCreate, open }) => {
  const { styles } = useStyles();
  const [form] = Form.useForm();

  const handleCancel = () => {
    form.resetFields();
    onCancel?.();
  };

  const handleCreate = async () => {
    try {
      const values = await form.validateFields();
      onCreate?.(values);
      form.resetFields();
    } catch (error) {
      // 验证失败，不进行操作
    }
  };

  return (
    <Modal
      open={open}
      onCancel={handleCancel}
      onOk={handleCreate}
      okText="新建"
      cancelText="取消"
      width={500}
      centered
      destroyOnHidden
      title="新建知识库"
    >
      <div>
        <div className={styles.content}>
          <div className={styles.infoRow}>
            <img
              src="https://img.alicdn.com/imgextra/i2/6000000005391/O1CN014VlOaO1ph8GdbLw8g_!!6000000005391-2-gg_dtc.png"
              className={styles.infoIcon}
              alt="信息图标"
            />
            <span className={styles.infoText}>
              知识库创建后可以添加文档、图片等文件，支持智能问答和内容检索
            </span>
          </div>

          <Form form={form} layout="vertical">
            <Form.Item name="name" label="知识库名称" rules={KNOWLEDGE_BASE_NAME_RULES}>
              <Input placeholder="请输入" />
            </Form.Item>

            <Form.Item name="description" label="描述">
              <Input placeholder="请输入" />
            </Form.Item>
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default CreateKnowledgeBase;
