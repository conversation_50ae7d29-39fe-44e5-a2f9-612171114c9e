import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  content: css`
    display: flex;
    flex-direction: column;
    gap: 12px;
    box-sizing: border-box;
    width: 100%;
    height: 188px;
  `,
  infoRow: css`
    display: flex;
    flex-direction: row;
    gap: 4px;
    width: 100%;
    height: 20px;
    align-items: center;
  `,
  infoIcon: css`
    width: 20px;
    height: 20px;
  `,
  infoText: css`
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #474a52;
  `,
}));
