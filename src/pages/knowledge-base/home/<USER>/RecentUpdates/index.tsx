import React, { useEffect, useRef, useState } from 'react';
import { ListKnowledgeBaseLogs } from '@/server/WuyingAI/ListKnowledgeBaseLogs';
import { useStyles } from './index.styles';
import UpdateItem from '../UpdateItem';
import { useSnapshot } from 'valtio';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { Spin, Empty } from 'antd';
import { useInfiniteScrollWithToken } from '@/hooks/useInfiniteScrollWithToken';
import { KnowledgeBaseLogStatus } from '@/server/WuyingAI/ListKnowledgeBaseLogs';
interface RecentUpdatesProps {
  className?: string;
  showTitle?: boolean;
  kbId?: string | null;
}

const RecentUpdates: React.FC<RecentUpdatesProps> = ({ className, showTitle = true, kbId }) => {
  const { styles } = useStyles();
  const { recentUpdatesRefresh } = useSnapshot(knowledgeBaseState);
  const [logsRefresh, setLogsRefresh] = useState(0);
  const ref = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const { data: logData, loading } = useInfiniteScrollWithToken(
    (params) =>
      ListKnowledgeBaseLogs({
        KbId: kbId || undefined,
        MaxResults: params.MaxResults,
        NextToken: params.NextToken,
      }),
    {
      refreshDeps: [kbId, recentUpdatesRefresh, logsRefresh],
      target: ref,
    },
  );

  // 有中间态的数据
  const hasIntermediateData = logData?.some((item) => item.Status === KnowledgeBaseLogStatus.Processing);

  // 有中间态的数据时，每30秒刷新一次
  useEffect(() => {
    if (hasIntermediateData) {
      intervalRef.current = setInterval(() => {
        setLogsRefresh((prev) => prev + 1);
      }, 30000);
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [hasIntermediateData]);

  return (
    <div className={`${styles.container} ${className || ''}`}>
      {showTitle && (
        <div className={styles.header}>
          <div className={styles.titleWrapper}>
            <span className={styles.title}>最近更新</span>
          </div>
        </div>
      )}
      <div className={styles.contentContainer} ref={ref}>
        {loading ? (
          <div className={styles.loadingContainer}>
            <Spin />
          </div>
        ) : logData?.length > 0 ? (
          logData?.map((item) => (
            <UpdateItem
              key={`${item.KbName}-${item.Title}-${item.TargetType}-${item.GmtModified}-${item.GmtCreated}`}
              title={item.Title}
              status={item.Status}
              targetType={item.TargetType}
              type={item.Type}
              gmtModified={item.GmtModified}
              kbName={item.KbName}
            />
          ))
        ) : (
          <Empty />
        )}
      </div>
    </div>
  );
};

export default RecentUpdates;
