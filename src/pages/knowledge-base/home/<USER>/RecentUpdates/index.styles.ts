import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  container: css`
    background: #ffffff;
    display: flex;
    flex-direction: column;
    overflow: auto;
    width: 100%;
    height: 100%;
    /* height: 624px; */
  `,
  contentContainer: css`
    overflow: auto;
    width: 100%;
    height: 100%;
    min-height: 200px;
  `,
  header: css`
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 16px 20px;
    justify-content: center;
    width: 100%;
    height: 68px;
  `,
  titleWrapper: css`
    display: flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;
    height: 20px;
  `,
  title: css`
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #1f2024;
  `,
  loadingContainer: css`
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  `,
}));
