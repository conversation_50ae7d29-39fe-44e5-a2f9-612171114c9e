import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  container: css`
    display: flex;
    flex-direction: row;
    gap: 16px;
    padding: 20px;
    background: #ffffff;
    flex: 1;
    height: 100%;
    border-radius: 16px;
    box-sizing: border-box;
  `,
  leftPanel: css`
    width: 400px;
    flex-shrink: 0;
  `,
  rightPanel: css`
    flex: 1;
    min-width: 0;
  `,
}));
