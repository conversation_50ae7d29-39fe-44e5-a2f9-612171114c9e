import React from 'react';
import RecentUpdates from './components/RecentUpdates';
import MyKnowledgeBase from './components/MyKnowledgeBase';
import { useStyles } from './index.styles';

const Home = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.leftPanel}>
        <RecentUpdates key="knowledge-base" />
      </div>
      <div className={styles.rightPanel}>
        <MyKnowledgeBase />
      </div>
    </div>
  );
};

export default Home;
