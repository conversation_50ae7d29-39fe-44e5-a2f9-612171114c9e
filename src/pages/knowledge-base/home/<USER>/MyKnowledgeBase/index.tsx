import React, { useState } from 'react';
import { ListKnowledgeBases } from '@/server/WuyingAI/ListKnowledgeBases';
import { useRequest } from 'ahooks';
import { Iconfont } from '@/components/icon';
import { But<PERSON>, App, Spin, Popover, Empty } from 'antd';
import { useStyles } from './index.styles';
import CreateKnowledgeBaseModal from '../CreateKnowledgeBaseModal';
import { CreateKnowledgeBase, ERROR_CODE_CREATE_KNOWLEDGE_BASE } from '@/server/WuyingAI/CreateKnowledgeBase';
import { KNOWLEDGE_BASE_TABS } from '@/constant/tabs';
import { useTabContext } from '@/contexts/TabContext';
import { ListKnowledgeBaseData } from '@/server/WuyingAI/ListKnowledgeBases';
import knowledgeBaseState from '@/model/knowledgeBaseModel';
import { useSnapshot } from 'valtio';
import { PlusOutlined } from '@ant-design/icons';

const MyKnowledgeBase: React.FC = () => {
  const { message } = App.useApp();
  const { addTab } = useTabContext();
  const { styles } = useStyles();
  const [open, setOpen] = useState(false);
  const { knowledgeBaseRefresh } = useSnapshot(knowledgeBaseState);
  const { setKnowledgeBaseParams } = knowledgeBaseState.actions;

  const { data: knowledgeBases, loading } = useRequest(() => ListKnowledgeBases({}), {
    refreshDeps: [knowledgeBaseRefresh],
  });

  const knowledgeBaseCount = knowledgeBases?.Data?.length || 0;
  const createKnowledgeBaseDisabled = knowledgeBaseCount >= 5;

  const handleCreateClick = () => {
    setOpen(true);
  };

  const handleKnowledgeBaseCardClick = (item: ListKnowledgeBaseData) => {
    setKnowledgeBaseParams({
      kbId: item.KbId,
      detailTab: 'recentUpdates',
    });
    addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseDetail);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const handleCreate = async (data: { name: string; description: string }) => {
    const res = await CreateKnowledgeBase({
      Name: data.name,
      Description: data.description,
    });
    if (res.Code === '200') {
      setOpen(false);
      knowledgeBaseState.actions.setKnowledgeBaseRefresh();
      message.success('新建知识库成功');
    } else {
      message.error(ERROR_CODE_CREATE_KNOWLEDGE_BASE[res.Code as unknown as keyof typeof ERROR_CODE_CREATE_KNOWLEDGE_BASE] || res.Message);
    }
  };

  const handleDocumentClick = (
    e: React.MouseEvent<HTMLDivElement>,
    item: ListKnowledgeBaseData,
  ) => {
    e.stopPropagation();
    setKnowledgeBaseParams({
      kbId: item.KbId,
      detailTab: 'fileList',
    });
    addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseDetail);
  };

  const handleSessionClick = (e: React.MouseEvent<HTMLDivElement>, item: ListKnowledgeBaseData) => {
    e.stopPropagation();
    setKnowledgeBaseParams({
      kbId: item.KbId,
      detailTab: 'sessionList',
    });
    addTab(KNOWLEDGE_BASE_TABS.knowledgeBaseDetail);
  };

  return (
    <>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.headerContent}>
            <span className={styles.title}>我的知识库</span>
            {createKnowledgeBaseDisabled ? (
              <Popover trigger="hover" placement="top" content={<span>最多创建5个知识库</span>}>
                <Button
                  type="primary"
                  onClick={handleCreateClick}
                  disabled={createKnowledgeBaseDisabled}
                >
                  <PlusOutlined />
                  <span>新建</span>
                </Button>
              </Popover>
            ) : (
              <Button
                type="primary"
                onClick={handleCreateClick}
                disabled={createKnowledgeBaseDisabled}
              >
                <PlusOutlined />
                <span>新建</span>
              </Button>
            )}
          </div>
        </div>
        {loading ? (
          <div className={styles.loadingContainer}>
            <Spin />
          </div>
        ) : knowledgeBases?.Data?.length && knowledgeBases?.Data?.length > 0 ? (
          <div className={styles.cardsContainer}>
            {knowledgeBases?.Data?.map((item, index) => (
              <div
                key={item.KbId || index}
                className={styles.card}
                onClick={() => handleKnowledgeBaseCardClick(item)}
              >
                <div className={styles.cardHeader}>
                  <span className={styles.cardTitle}>{item.Name}</span>
                </div>
                <div className={styles.cardContent}>
                  <span className={styles.cardDescription}>{item.Description}</span>
                </div>
                <div className={styles.statsContainer}>
                  <div className={styles.statItem} onClick={(e) => handleDocumentClick(e, item)}>
                    <Iconfont
                      type="document--outline"
                      style={{ width: 16, height: 16, color: '#8c909c' }}
                    />
                    <span className={styles.statValue}>{item.DocumentCount}</span>
                  </div>
                  <div className={styles.statItem} onClick={(e) => handleSessionClick(e, item)}>
                    <Iconfont
                      type="chat--outline"
                      style={{ width: 16, height: 16, color: '#8c909c' }}
                    />
                    <span className={styles.statValue}>{item.SessionCount}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Empty />
        )}
      </div>
      <CreateKnowledgeBaseModal open={open} onCancel={handleCancel} onCreate={handleCreate} />
    </>
  );
};

export default React.memo(MyKnowledgeBase);
