import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  container: css`
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: auto;
  `,
  header: css`
    display: flex;
    flex-direction: row;
    box-sizing: border-box;
    padding: 16px 20px;
    align-items: center;
    width: 100%;
    height: 68px;
  `,
  headerContent: css`
    display: flex;
    flex-direction: row;
    gap: 4px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 36px;
  `,
  title: css`
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #1f2024;
  `,
  createIcon: css`
    width: 16px;
    height: 16px;
  `,
  createText: css`
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #ffffff;
  `,
  cardsContainer: css`
    box-sizing: border-box;
    padding: 2px 20px 12px 20px;
    width: 100%;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    overflow: auto;
  `,
  loadingContainer: css`
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  `,
  card: css`
    border-radius: 12px;
    background: #ffffff;
    box-shadow:
      inset 0 0 0 1px #d4d6db,
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 16px;
    overflow: hidden;
    width: 382px;
    height: 162px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow:
        inset 0 0 0 1px #d4d6db,
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }
  `,
  cardHeader: css`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  `,
  cardTitle: css`
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #1f2024;
    margin-bottom: 12px;
  `,
  cardIcon: css`
    width: 20px;
    height: 20px;
  `,
  statsContainer: css`
    display: flex;
    flex-direction: row;
    gap: 24px;
    width: 100%;
    margin-top: auto;
  `,
  statItem: css`
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
  `,
  statLabel: css`
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #8c909c;
  `,
  statValue: css`
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #474a52;
  `,
  cardContent: css`
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow: auto;
  `,
  cardDescription: css`
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #474a52;
  `,
}));
