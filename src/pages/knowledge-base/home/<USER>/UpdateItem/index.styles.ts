import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css, token }) => ({
  itemContainer: css`
    display: flex;
    flex-direction: row;
    gap: 8px;
    box-sizing: border-box;
    padding: 12px 20px;
    align-items: center;
    width: 100%;
    height: 64px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }
  `,
  processingIcon: css`
    width: 36px;
    height: 36px;
    border-radius: 100px;
    background: #eaf3ff;
    box-sizing: border-box;
    padding: 8px;
    fill: ${token.colorPrimary};
  `,
  successIcon: css`
    width: 36px;
    height: 36px;
    border-radius: 100px;
    background: #e3fff0;
    box-sizing: border-box;
    padding: 8px;
    fill: ${token.colorSuccess};
  `,
  deleteIcon: css`
    width: 36px;
    height: 36px;
    border-radius: 100px;
    background: rgba(42, 45, 49, 0.1); // TODO 颜色待确定
    box-sizing: border-box;
    padding: 8px;
    fill: #474a52;
  `,
  processingText: css`
    color: ${token.colorPrimary};
  `,
  successText: css`
    color: #8c909c;
  `,
  contentWrapper: css`
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 0;
    height: 40px;
  `,
  titleRow: css`
    display: flex;
    flex-direction: row;
    gap: 4px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 20px;
  `,
  itemTitle: css`
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #474a52;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
  `,
  progressStatus: css`
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #0075ff;
    white-space: nowrap;
    flex-shrink: 0;
  `,
  normalStatus: css`
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #8c909c;
    white-space: nowrap;
    flex-shrink: 0;
  `,
  pathRow: css`
    display: flex;
    flex-direction: row;
    gap: 4px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 16px;
  `,
  pathText: css`
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #8c909c;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
  `,
}));
