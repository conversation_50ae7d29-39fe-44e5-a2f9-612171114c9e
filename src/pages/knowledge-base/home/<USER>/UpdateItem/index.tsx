import React from 'react';
import { useStyles } from './index.styles';
import { Iconfont } from '@/components/icon';
import {
  KnowledgeBaseLogStatus,
  KnowledgeBaseTargetType,
  KnowledgeBaseLogType,
} from '@/server/WuyingAI/ListKnowledgeBaseLogs';
import { getRelativeTime } from '@/utils/date';

interface UpdateItemProps {
  title: string;
  status: KnowledgeBaseLogStatus;
  targetType: KnowledgeBaseTargetType;
  onClick?: () => void;
  type: KnowledgeBaseLogType;
  gmtModified: string;
  kbName?: string | null;
}

// {
//   "Status": "success",
//   "Type": "delete",
//   "KbName": "弋秋的知识库",
//   "GmtModified": "2025-08-04T15:04Z",
//   "TargetType": "session",
//   "Title": "打招呼",
//   "GmtCreated": "2025-08-04T15:04Z"
// },
// {
//   "Status": "processing",
//   "Type": "create",
//   "KbName": "弋秋的知识库",
//   "GmtModified": "2025-08-04T11:13Z",
//   "TargetType": "session",
//   "Title": "打招呼",
//   "GmtCreated": "2025-08-04T11:13Z"
// }

export const typeMap = {
  [KnowledgeBaseLogType.delete]: '移除',
  [KnowledgeBaseLogType.create]: '添加',
};

export const getIconText = (type: KnowledgeBaseLogType, status: KnowledgeBaseLogStatus) => {
  if (type === KnowledgeBaseLogType.delete) {
    return status === KnowledgeBaseLogStatus.Processing ? '移除中' : '移除';
  }
  return status === KnowledgeBaseLogStatus.Processing ? '添加中' : '添加';
};

const UpdateItem: React.FC<UpdateItemProps> = ({
  title,
  status,
  targetType,
  onClick,
  type,
  gmtModified,
  kbName,
}) => {
  const { styles } = useStyles();

  const getIcon = (status: KnowledgeBaseLogStatus, type: KnowledgeBaseLogType) => {
    let icon = '';
    let text = '';
    let iconClassName = styles.processingIcon;
    let textClassName = styles.progressStatus;
    if (type === KnowledgeBaseLogType.create) {
      icon = 'document--add';
      if (status === KnowledgeBaseLogStatus.Processing) {
        text = '添加中';
        iconClassName = styles.processingIcon;
        textClassName = styles.processingText;
      } else {
        text = `${getRelativeTime(gmtModified)} 添加`;
        iconClassName = styles.successIcon;
        textClassName = styles.successText;
      }
    } else if (type === KnowledgeBaseLogType.delete) {
      icon = 'document-move';
      text = `${getRelativeTime(gmtModified)} 移除`;
      iconClassName = styles.deleteIcon;
      textClassName = styles.successText;
    }
    return { icon, text, iconClassName, textClassName };
  };

  const { icon, text, iconClassName, textClassName } = getIcon(status, type);

  return (
    <div className={styles.itemContainer} onClick={onClick}>
      <Iconfont type={icon} className={iconClassName} />
      <div className={styles.contentWrapper}>
        <div className={styles.titleRow}>
          <span className={styles.itemTitle} title={title}>
            {title}
          </span>
          <span className={textClassName}>{text}</span>
        </div>
        <div className={styles.pathRow}>
          <span className={styles.pathText} title={targetType}>
            {targetType === KnowledgeBaseTargetType.document ? `${kbName}/文件` : `${kbName}/会话`}
          </span>
        </div>
      </div>
    </div>
  );
};

export default UpdateItem;
