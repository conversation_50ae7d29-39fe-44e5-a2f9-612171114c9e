import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  wrapper: {
    // width: '1000px',
    boxSizing: 'border-box',
    height: 'calc(100vh - 48px)',
    flexDirection: 'column',
    overflow: 'auto',
    padding: '28px 20px',
    background: '#fff',
    borderRadius: '12px',
    position: 'relative',
  },
  endTaskButton: {
    position: 'sticky',
    bottom: '50px',
    left: '20px',
    zIndex: 1000,
    borderRadius: token.borderRadius,
    backgroundColor: '#1F2024',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '8px 12px',
    color: '#fff',
    fontSize: '14px',
    lineHeight: '20px',
    gap: '4px',
    boxSizing: 'border-box',
    cursor: 'pointer',
    width: '116px',
    transition: 'all 0.3s',
    '&:hover': {
      backgroundColor: '#2F3136',
    },

    // .terminateButton {
    //       border-radius: 12px;
    //       background: #1F2024;
    //       display: flex;
    //       flex-direction: row;
    //       flex-wrap: nowrap;
    //       gap: 4px;
    //       box-sizing: border-box;
    //       padding: 8px 12px;
    //       justify-content: center;
    //       align-items: center;
    //       width: 104px;
    //       height: 36px;
    //       overflow: hidden;
    //       cursor: pointer;
    //     }

  },
  icon: {
    marginRight: '4px',
    width: '20px',
    height: '20px',
  },
  text: {
    fontSize: '14px',
    color: '#fff',
    fontWeight: 500,
    lineHeight: '20px',
    textAlign: 'center',
  },
}));

export const useHomePageStyle = createStyles(({ token, css }) => {
  return {
    layout: css`
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding: 28px 20px;
      background: ${token.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
    `,
    layoutChatting: css`
      height: 100%;
    `,
    layoutRequiresResearch: css`
      max-width: 450px !important;
    `,
    // sider 样式
    sider: css`
      background: ${token.colorBgLayout}80;
      width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 0 12px;
      box-sizing: border-box;
    `,
    logo: css`
      display: flex;
      align-items: center;
      justify-content: start;
      padding: 0 24px;
      box-sizing: border-box;
      margin: 24px 0;
      gap: 8px;
      span {
        font-weight: bold;
        color: ${token.colorText};
        font-size: 16px;
      }
    `,
    addBtn: css`
      background: #1677ff0f;
      border: 1px solid #1677ff34;
      height: 40px;
    `,
    conversations: css`
      flex: 1;
      overflow-y: auto;
      margin-top: 12px;
      padding: 0;

      .ant-conversations-list {
        padding-inline-start: 0;
      }
    `,
    siderFooter: css`
      border-top: 1px solid ${token.colorBorderSecondary};
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    `,
    // chat list 样式
    chat: css`
      width: 100%;
      max-width: 1000px;
      margin: auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding-block: ${token.paddingLG}px;
      gap: 16px;
    `,
    chatPrompt: css`
      .ant-prompts-label {
        color: #000000e0 !important;
      }
      .ant-prompts-desc {
        color: #000000a6 !important;
        width: 100%;
      }
      .ant-prompts-icon {
        color: #000000a6 !important;
      }
    `,
    loadingMessage: css`
      background-image: linear-gradient(90deg, #ff6b23 0%, #af3cb8 31%, #53b6ff 89%);
      background-size: 100% 2px;
      background-repeat: no-repeat;
      background-position: bottom;
    `,
    placeholder: css`
      padding-top: 32px;
    `,
    // sender 样式
    sender: css`
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
    `,
    speechButton: css`
      font-size: 18px;
      color: ${token.colorText} !important;
    `,
    senderPrompt: css`
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
      color: ${token.colorText};
    `,
  };
});
