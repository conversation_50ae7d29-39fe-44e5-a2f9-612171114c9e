import React from 'react';
import SearchActivityDisplay from './display-search';
import ReportTitle from '@/components/report-title';
import { useStyles } from './index.styles';
import researchState from '@/model/researchModel';
import { useSnapshot } from 'valtio';
import { Iconfont } from '@/components/icon';
import { CancelSession } from '@/server/WuyingAI/CancelSession';
import { Modal, message } from 'antd';

// icon-stop--outline

export default function SearchPlan() {
  const { styles } = useStyles();
  const { researchInfo } = useSnapshot(researchState);
  const { report_title, research_plan, report_outline, loading, run_error, sessionId, stopChat, run_success } = researchInfo;

  const stopDeepChat = () => {
    Modal.confirm({
      title: '确认终止任务吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        stopChat?.();
        message.success('任务已终止');
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  return (
    <div className={styles.wrapper}>
      <ReportTitle
        title={report_title}
        desc={research_plan}
        loading={loading}
        run_error={run_error}
        run_success={run_success}
      />
      <SearchActivityDisplay
        data={report_outline as Array<{ title: string; key_question: string; iterative_research_id?: string; content?: string }>}
        loading={loading}
        run_error={run_error}
        run_success={run_success}
      />
      {loading && <div className={styles.endTaskButton} onClick={stopDeepChat}>
        <Iconfont type="stop--outline" className={styles.icon} fill="#fff" />
        <span className={styles.text}>终止任务</span>
      </div>}
    </div>
  );
}
