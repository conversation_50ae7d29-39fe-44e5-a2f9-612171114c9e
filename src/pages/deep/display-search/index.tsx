import React from 'react';
import { List, Timeline, Badge, Spin } from 'antd';
import ActivityItem from '@/components/activity-item';
import { useStyles } from './index.styles';
interface SearchActivityDisplayProps {
  data?: Array<{
    title: string;
    key_question: string;
    iterative_research_id?: string;
    content?: string;
  }>;
  loading?: boolean;
  run_error?: boolean;
  run_success?: boolean;
}

const SearchActivityDisplay: React.FC<SearchActivityDisplayProps> = ({ data, loading, run_error, run_success }) => {
  const { styles } = useStyles();
  return (
    <div id="1" className={styles.container}>
      {/* {data?.length && (
        <Timeline
          items={[
            {
              dot: <Badge color="black" />,
              children: (
                <>
                  <div className={styles.timelineTitle}>研究计划</div>
                  <List
                    className={styles.list}
                    dataSource={data}
                    split={false}
                    renderItem={(activity, index) => (
                      <List.Item className={styles.listItem} key={index}>
                        <ActivityItem question={activity.key_question} title={activity.title} />
                      </List.Item>
                    )}
                  />
                </>
              ),
            },
            {
              children: (
                <>
                  {run_error && <div className={styles.error}>任务失败</div>}
                  {run_success && <div className={styles.done}>任务已完成</div>}
                  {loading && (<>
                    <Spin style={{ fontSize: 14, marginRight: 8 }} />
                    <span>任务进行中...</span>
                  </>)}
                  {!run_success && !run_error && !loading && <div className={styles.error}>任务停止</div>}
                </>
              ),
              dot: <Badge color="black" />,
            },
          ]}
        />
      )} */}
      {data?.length && (<><div className={styles.timelineTitle}>研究计划</div>
        <List
          className={styles.list}
          dataSource={data}
          split={false}
          renderItem={(activity, index) => (
            <List.Item className={styles.listItem} key={activity?.key_question}>
              <ActivityItem question={activity.key_question} title={activity.title} content={activity.content} />
            </List.Item>
          )}
        />
      </>)
      }
    </div>
  );
};

export default SearchActivityDisplay;
