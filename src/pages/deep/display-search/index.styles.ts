import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    // width: '100%',
    margin: '20px 14px',
    boxSizing: 'border-box',
  },
  card: {
    borderRadius: token.borderRadius,
    boxShadow: token.boxShadowTertiary,
    '& .ant-card-head': {
      borderBottom: `1px solid ${token.colorBorderSecondary}`,
      fontSize: token.fontSizeLG,
      fontWeight: token.fontWeightStrong,
    },
    '& .ant-card-body': {
      padding: 0,
    },
  },
  timelineTitle: {
    fontSize: token.fontSizeLG,
    fontWeight: token.fontWeightStrong,
    marginBottom: token.marginSM,
  },
  list: {
    '& .ant-list-item': {
      // padding: '16px 24px',
      borderBottom: 'none',
      '&:last-child': {
        borderBottom: 'none',
      },
    },
  },
  listItem: {
    width: '100%',
  },
  done: {
    color: '#009244',
    fontSize: '16px',
    fontWeight: 500,
    lineHeight: '24px',
  },
  error: {
    color: '#D93026',
    fontSize: '16px',
    fontWeight: 500,
    lineHeight: '24px',
  },
}));
