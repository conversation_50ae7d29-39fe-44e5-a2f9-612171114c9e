import React, { useEffect, useState, useMemo } from 'react';
import cx from 'classnames';
import ChatSender from '@/components/chat-sender';
import Welcome from '@/components/welcome';
import ChatHeader from '@/components/chat-header';
import ChatList from '@/components/chat-list';
import useChat from '@/hooks/useChat';
import { useHomePageStyle } from './index.styles';
import { AgentIdEnum } from '@/constant/enum';
import { ResourceItem } from '@/server/WuyingAI/SendMessage';
import { getLoginInfo } from '@/utils';
import { useTabContext } from '@/contexts/TabContext';
import SearchPlan from './SearchPlan';
import researchState from '@/model/researchModel';
import { notification } from 'antd';
import FileManager from '@/components/file-manager';
import { useRequest } from 'ahooks';

const times = [
  { label: '早上好！', start: 7, end: 11 },
  { label: '中午好！', start: 11, end: 13 },
  { label: '下午好！', start: 13, end: 18 },
  { label: '晚上好！', start: 18, end: 31 },
];

const getCurrentGreeting = () => {
  const hours = new Date().getHours();
  let currentTime = times[0];
  for (let i = 0; i < times.length; i++) {
    if (hours >= times[i].start % 24 && hours < times[i].end) {
      currentTime = times[i];
      break;
    }
  }
  return currentTime.label;
};

const Deeper: React.FC = () => {
  const { styles } = useHomePageStyle();

  const {
    sendMessage,
    loading,
    resetChat,
    deepMessages,
    customMessages,
    setSessionInfo,
    sessionInfo,
    setDeepMessages,
    stopChat,
  } = useChat({
    agentId: AgentIdEnum.deep,
  });

  const { data: userInfo } = useRequest(getLoginInfo);
  const username = userInfo?.username;


  // 当前执行环境
  const [selectedEnvironment, setSelectedEnvironment] = useState<{
    Name: string;
    DesktopId: string;
  } | null>(null);

  const { addTab, closeTab, tabVisible, hideTabs, showTabs } = useTabContext();

  // 提取文件信息，避免重复的可选链操作
  const file: { ArtifactId?: string; FileType?: string; FileName?: string } = useMemo(() => ({
    ArtifactId: customMessages?.ArtifactId,
    FileType: customMessages?.FileType,
    FileName: customMessages?.FileName,
  }), [customMessages?.ArtifactId, customMessages?.FileType, customMessages?.FileName]);

  // ==================== Event ====================
  const onSubmit = (val: string, resources: ResourceItem[]) => {
    if (!val) return;

    if (loading) {
      notification.error({
        message: '请求正在处理中，请等待请求完成。',
      });
      return;
    }
    closeTab('research');
    sendMessage(val, resources, selectedEnvironment?.DesktopId);
  };

  useEffect(() => {
    if (customMessages.requires_research) {
      researchState.actions.setResearchInfo({
        report_title: customMessages?.report_title,
        report_outline: customMessages?.report_outline,
        research_plan: customMessages?.research_plan,
        run_error: customMessages?.run_error,
        sessionId: sessionInfo?.SessionId,
        run_success: customMessages?.run_success,
        loading,
        stopChat,
      });
    }
  }, [
    customMessages.requires_research,
    customMessages.report_outline,
    customMessages.report_title,
    customMessages.research_plan,
    customMessages.run_error,
    customMessages.run_success,
    sessionInfo?.SessionId,
    loading,
    stopChat,
  ]);

  useEffect(() => {
    if (customMessages.requires_research) {
      addTab({
        key: 'research',
        label: '当前会话',
        closable: true,
        children: SearchPlan,
      });
    } else {
      closeTab('research');
    }
  }, [customMessages.requires_research, addTab, closeTab]);

  // ==================== Nodes ====================
  const chatList = deepMessages?.length ? (
    <ChatList
      messages={deepMessages}
      agentId={AgentIdEnum.deep}
      sessionId={sessionInfo.SessionId}
      updateMessages={setDeepMessages}
      research={customMessages.requires_research}
      loading={loading}
    />
  ) : (
    <Welcome text={'Deep Research'} tip="请问您正在研究什么？" />
  );

  const handleOpenTab = () => {
    if (tabVisible) {
      hideTabs();
    } else {
      showTabs();
    }
  };
  const onNewChat = () => {
    resetChat();
  };

  // ==================== Render =================
  return (
    <div className={styles.layout}>
      <ChatHeader
        agentId={AgentIdEnum.deep}
        selectedEnvironment={selectedEnvironment}
        onSelectEnvironment={setSelectedEnvironment}
        onNewChat={onNewChat}
        handleOpenTab={handleOpenTab}
      />
      <div
        className={cx(styles.chat, {
          [styles.layoutChatting]: deepMessages.length > 0,
          [styles.layoutRequiresResearch]: tabVisible,
        })}
      >
        {chatList}
        <ChatSender
          onSubmit={onSubmit}
          loading={loading}
          agentId={AgentIdEnum.deep}
          sessionInfo={sessionInfo}
          setSessionInfo={setSessionInfo}
          research={customMessages.requires_research}
          file={file}
          stopChat={stopChat}
          placeholder={'请告诉我您正在研究的课题，越详细越好。'}
        />
      </div>
    </div>
  );
};

export default Deeper;
