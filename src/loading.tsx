import { useEffect, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';

export default () => {
  // 延迟展示loading
  const [showLoading, setShowLoading] = useState(false);
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLoading(true);
    }, 500);
    return () => {
      clearTimeout(timer);
    };
  }, []);
  return showLoading ? <LoadingOutlined /> : null;
};
