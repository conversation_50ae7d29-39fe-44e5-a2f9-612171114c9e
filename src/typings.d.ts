import 'umi/typings';
import { AipptIframeInstance } from '@/types/ppt';
import type { HTMLAttributes, DetailedHTMLProps } from 'react';

// 全局类型声明
declare global {
  const AipptIframe: AipptIframeInstance | undefined;
}

declare module 'react/jsx-runtime' {
  namespace JSX {
    interface IntrinsicElements {
      'wuying-web-sdk': { class: string } & DetailedHTMLProps<HTMLAttributes<any>, any>;
    }
  }
}

