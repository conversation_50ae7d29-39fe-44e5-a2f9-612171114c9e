// ai ppt 类型定义
export enum AiPPTEventTypeEnum {
  PPT_DOWNLOAD = 'PPT_DOWNLOAD', // 下载
  PPT_SAVE = 'PPT_SAVE', // 保存
  GENERATE_PPT_SUCCESS = 'GENERATE_PPT_SUCCESS', // 生成ppt成功
}

export interface AiPPTEventDownloadData {
  edit: boolean;
  filename: string;
  id: number;
  status: boolean;
  taskId: number;
  thumbnail: string;
  type: string;
  url: string;
}

export interface AiPPTSaveData {
  id: number;
  filename: string;
  thumbnail?: string; // 缩略图, 目前是只有在定位在第一张ppt，编辑的时候才有
}

export interface AiPPTGeneratePPTSuccessData {
  id: number;
  taskId: number;
  thumbnail: string;
  title: string;
  type: string;
}

export interface AiPPTEventMap {
  [AiPPTEventTypeEnum.PPT_DOWNLOAD]: AiPPTEventDownloadData;
  [AiPPTEventTypeEnum.PPT_SAVE]: AiPPTSaveData;
  [AiPPTEventTypeEnum.GENERATE_PPT_SUCCESS]: AiPPTGeneratePPTSuccessData;
}

export type AiPPTOnMessage = <T extends AiPPTEventTypeEnum>(
  eventType: T,
  data: AiPPTEventMap[T],
) => void;

export interface AipptIframeConfig {
  appkey: string;
  channel: string;
  code: string;
  container: HTMLElement;
  editorModel?: boolean;
  options?: {
    fc_plate?: number[];
    custom_generate?: {
      type: number;
      step: number;
      content: string;
    };
    download_mode?: number;
  };
  routerOptions?: {
    list: string[];
    editor?: {
      id: number;
      showLogo: number;
    };
  };
  onMessage?: AiPPTOnMessage;
}

export interface AipptIframeInstance {
  show: (config: AipptIframeConfig) => Promise<void>;
  deleteIframe: () => void;
}
