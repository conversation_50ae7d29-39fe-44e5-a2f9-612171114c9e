import { Flex } from 'antd';
import { useWelcomeStyles } from './index.styles';
const ChatWelcome = ({ text, tip }: { text: string; tip: string }) => {
  const { styles } = useWelcomeStyles();
  return (
    <Flex vertical align="center" justify="space-around" className={styles.wrapper}>
      <div className={styles.mainText}>{text}</div>
      <div className={styles.tip}>{tip}</div>
    </Flex>
  );
};

export default ChatWelcome;
