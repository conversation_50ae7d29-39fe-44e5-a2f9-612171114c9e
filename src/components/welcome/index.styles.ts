import { createStyles } from 'antd-style';

export const useWelcomeStyles = createStyles(({ token, css }) => {
  return {
    wrapper: css`
      width: 100%;
      display: flex;
      background: ${token.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
    `,
    mainText: css`
      font-size: 32px;
      font-weight: 500;
      line-height: 44px;
      letter-spacing: normal;
      color: #1f2024;
    `,
    tip: css`
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
      text-align: center;
      letter-spacing: normal;
      color: #474a52;
    `,
  };
});
