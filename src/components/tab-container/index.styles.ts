import { createStyles } from 'antd-style';

export const useTabContainerStyles = createStyles(({ css, token }) => ({
  header: css`
    padding: 16px 20px;
    border-bottom: 1px solid ${token.colorBorder};
    background: ${token.colorBgContainer};
    font-weight: 500;
    font-size: 16px;
  `,

  tabContainer: css`
    height: 100%;
    width: 100%;
    .ant-tabs-tab-btn {
      width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
    }

    .ant-tabs-tabpane {
      height: 100%;
      overflow: auto;
    }
    .ant-tabs-content {
      height: 100%;
    }
    .ant-tabs-nav::before {
      border-bottom: none;
    }

    .ant-tabs-content-holder {
      background: #fff;
      border-radius: 12px;
    }
  `,

  emptyState: css`
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${token.colorBgContainer};
  `,

  emptyContent: css`
    text-align: center;
    color: ${token.colorTextSecondary};

    p {
      margin: 8px 0;

      &:first-child {
        font-size: 16px;
        color: ${token.colorText};
      }
    }
  `,

  emptyHint: css`
    font-size: 14px;
    opacity: 0.8;
  `,

  titleWrapper: css`
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 20px;
  `,

  titleIcon: css`
    width: 24px;
    height: 24px;
  `,

  titleText: css`
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
    letter-spacing: normal;
    color: #1f2024;
  `,

  titleUnfoldIcon: css`
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: #e6eaf0;
    padding: 8px;
    cursor: pointer;
  `,

  // TODO: 需要优化
  tabBar: css`
    width: 272px;
    height: 16px;
    background: #fff;
    overflow: hidden;
    position: relative;
    ::before {
      content: '';
      position: absolute;
      left: -16px;
      bottom: 0;
      width: 32px;
      height: 32px;
      background: #f2f5fa;
      border-radius: 32px;
    }
    ::after {
      content: '';
      position: absolute;
      right: -16px;
      bottom: 0;
      width: 32px;
      height: 32px;
      background: #f2f5fa;
      border-radius: 32px;
    }
  `,
}));
