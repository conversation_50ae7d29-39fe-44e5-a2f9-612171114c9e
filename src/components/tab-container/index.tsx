import React, { useMemo } from 'react';
import { Tabs } from 'antd';
import { TabItem } from '@/contexts/TabContext';
import { useTabContainerStyles } from './index.styles';
import { menuIconTitleMap } from '@/constant/icon';
import { MenuIconType } from '@/constant/icon';
import { useLocation } from 'umi';
import type { TabsProps } from 'antd';


interface TabContainerProps {
  tabs: TabItem[];
  activeKey: string;
  onTabChange?: (activeKey: string) => void;
  onTabEdit?: (targetKey: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => void;
  className?: string;
  hideConversation?: boolean;
}

const TabContainer: React.FC<TabContainerProps> = ({
  tabs: tabsContext,
  activeKey,
  onTabChange,
  onTabEdit,
  className,
}) => {
  const { styles, cx } = useTabContainerStyles();
  const location = useLocation();

  const handleTabEdit = (targetKey: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => {
    onTabEdit?.(targetKey, action);
  };

  const handleTabChange = (key: string) => {
    onTabChange?.(key);
  };

  const tabs = useMemo(() => {
    return tabsContext.map((tab) => {
      const { children: originalChildren, props, ...rest } = tab;
      const children = React.createElement(
        originalChildren as unknown as React.ComponentType<any>,
        props,
      );
      return {
        ...rest,
        children,
      };
    }) as TabsProps['items'];
  }, [tabsContext]);

  const title = useMemo(() => {
    const key = location.pathname.slice(1) as MenuIconType;
    const text = menuIconTitleMap[key];
    return (
      <div className={styles.titleWrapper}>
        {/* {!hideConversation && (
          <Iconfont type="Unfold" className={styles.titleUnfoldIcon} onClick={hideTabs} />
        )} */}
        <div className={styles.titleText}>{text}</div>
      </div>
    );
  }, [location.pathname, styles.titleWrapper, styles.titleText]);

  if (tabsContext.length === 0) {
    return (
      <div className={className}>
        <div className={styles.emptyState}>
          <div className={styles.emptyContent}>
            <p>暂无打开的功能页面</p>
            <p className={styles.emptyHint}>通过对话或其他方式打开功能页面</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Tabs
      type="editable-card"
      hideAdd
      activeKey={activeKey}
      onChange={handleTabChange}
      onEdit={handleTabEdit}
      items={tabs}
      // destroyOnHidden
      className={cx(styles.tabContainer)}
      tabBarGutter={8}
      size="large"
      tabBarExtraContent={{
        left: title,
      }}
      tabBarStyle={{
        margin: 0,
      }}
    />
  );
};

export default TabContainer;
export type { TabItem };
