import { createStyles } from 'antd-style';

export const useLiveComponentStyles = createStyles(({ css, token }) => {
  return {
    wrapper: css`
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      height: 100%;
      padding: 20px;
    `,
    header: css`
      display: flex;
      justify-content: flex-end;
      box-sizing: border-box;
      width: 100%;
      align-items: center;
      margin-bottom: 20px;
    `,
    connectItem: css`
      flex: 1;
    `,
    errorWrapper: css`
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #8C909C;
    `,
  };
});
