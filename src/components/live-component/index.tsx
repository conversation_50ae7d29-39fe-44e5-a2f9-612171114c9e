import { useEffect, useRef, useState } from 'react';
import { Button } from 'antd';
import { Iconfont } from '../icon';
import { useLiveComponentStyles } from './index.styles';

const LiveComponent = ({ connectInfo }: {
  connectInfo: {
    connection_properties?: string;
    resource_id?: string;
    resource_type?: string;
    ticket?: string;
    auth_code?: string;
    app_id?: string;
    session_id?: string;
  };
}) => {
  const { styles } = useLiveComponentStyles();
  const sessionRef = useRef<any>(null);

  const isConnectedRef = useRef(false);

  const [isManual, setIsManual] = useState(false);

  // 断连code
  const [isDisconnected, setIsDisconnected] = useState<number | null>(null);

  useEffect(() => {
    if (!connectInfo.ticket || isConnectedRef.current) return;
    const sessionParam: any = {
      openType: 'inline',
      containerId: 'sessionIframe',
      resourceType: 'local',
      connectType: 'app',
      uiConfig: {
        toolbar: {
          visible: false,
        },
        exitCheck: false,
      },
      userInfo: {
        ticket: connectInfo.ticket,
      },
      appInfo: {
        appId: connectInfo.app_id,
        appInstanceId: connectInfo.resource_id,
        loginRegionId: 'cn-shanghai',
        resourceId: connectInfo.resource_id,
        bizRegionId: 'cn-shanghai',
        connectionProperties: connectInfo.connection_properties,
      },
    };
    const wuyingSdk = (window as any).Wuying.WebSDK;

    const session = wuyingSdk.createSession('appstream', sessionParam);
    session.addHandle('onConnected', () => {
      session.enableInput(false);
      setIsManual(false);
      isConnectedRef.current = true;
    });
    session.addHandle('onDisConnected', (data: any) => {
      session.stop();
      isConnectedRef.current = false;
      setIsDisconnected(data);
    });

    sessionRef.current = session;

    session.start();
    setIsDisconnected(null);
  }, [connectInfo]);

  const handleManual = () => {
    if (sessionRef.current) {
      sessionRef.current.enableInput(!isManual);
      setIsManual(!isManual);
    }
  };


  return (<div className={styles.wrapper}>
    {isDisconnected ? <div className={styles.errorWrapper}>
      <img src="https://gw.alicdn.com/imgextra/i2/O1CN01YBq63r1KAqGP4eyvD_!!6000000001124-55-tps-53-44.svg" alt="" />
      <div style={{ margin: '20px 0' }}>云电脑断开连接</div>
      <div>Code: {isDisconnected}</div>
    </div> : <>
      <div className={styles.header}><Button
        onClick={() => {
          handleManual();
        }}
        type="primary"
        icon={<Iconfont
          type="user--admin--outline"
          style={{
            width: '20px',
            height: '20px',
          }}
          fill="#fff"
        />}
      >{!isManual ? '手动接管' : '退出接管'}</Button></div>
      <wuying-web-sdk id="sessionIframe" class={styles.connectItem} />
    </>}

  </div>);
};
export default LiveComponent;
