import React from 'react';
import { menuIconMap, type MenuIconType } from '@/constant';
import '@/global.css';
import { useStyles } from './index.styles';

export const Icon = ({ type, ...args }: { type: MenuIconType }) => {
  return <img src={menuIconMap[type]} {...args} />;
};

export const Iconfont: React.FC<
  {
    type: string;
    className?: string;
    style?: React.CSSProperties;
    fill?: string;
    disabled?: boolean;
  } & React.HTMLAttributes<HTMLOrSVGElement>
> = (props) => {
  const { type, className, style, disabled, onClick, ...restProps } = props;
  const { styles, cx } = useStyles();
  const handleClick = (e: React.MouseEvent<SVGSVGElement>) => {
    if (disabled) {
      e.preventDefault();
      return;
    }
    onClick?.(e);
  };

  return (
    <svg
      className={cx('ai-icon-font', className, { [styles.disabled]: disabled })}
      style={style}
      aria-hidden="true"
      onClick={handleClick}
      {...restProps}
    >
      <use xlinkHref={`#icon-${type}`} />
    </svg>
  );
};
