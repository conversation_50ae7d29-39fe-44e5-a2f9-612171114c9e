import React from 'react';
import { Card, Typography } from 'antd';
import { useStyles } from './index.styles';
import { Iconfont } from '@/components/icon';
import { GetArtifactPreview } from '@/server/WuyingAI/GetArtifactPreview';
import usePreviewUrl from '@/hooks/usePreviewUrl';
import { getFileIconByFileName } from '@/utils/file';

const { Text } = Typography;

interface FileCardProps {
  id?: string;
  fileName?: string;
  fileSize?: string;
  modifyTime?: string;
  content?: string;
}

export const FileCard: React.FC<FileCardProps> = ({
  id,
  fileName = '文件预览',
}) => {
  const { styles } = useStyles();
  const { previewUrl } = usePreviewUrl();

  const onClick = async () => {
    if (!id) return;
    const res = await GetArtifactPreview({
      ArtifactId: id,
    });
    previewUrl(res?.Data?.Url || '', {
      label: fileName,
      key: id,
    });
  };

  return (
    <Card
      id={id}
      className={styles.card}
      bodyStyle={{ padding: '8px', height: '100%' }}
      hoverable
      onClick={onClick}
      bordered
    >
      <Card.Meta
        avatar={
          <Iconfont type={getFileIconByFileName(fileName)} className={styles.icon} />
        }
        title={
          <Text
            className={styles.fileName}
            title={fileName}
            ellipsis={{ tooltip: fileName }}
          >
            {fileName}
          </Text>
        }
        // description={
        //   <Space size={100}>
        //     <Text className={styles.fileSize}>{fileSize}</Text>
        //     <Text className={styles.modifyTime}>{modifyTime}</Text>
        //   </Space>
        // }
      />
    </Card>
  );
};

