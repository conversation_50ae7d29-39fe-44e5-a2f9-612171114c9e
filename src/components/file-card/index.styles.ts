import { createStyles } from 'antd-style';

export const useStyles = createStyles(() => ({
  card: {
    width: 340,
    height: 66,
    borderRadius: 8,
    border: '1px solid #c1d0e7',
    cursor: 'pointer',
    '&:hover': {
      borderColor: '#1890ff',
      boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',
    },
  },
  fileName: {
    fontSize: 14,
    fontWeight: 500,
    color: '#1f2024',
    marginTop: 12,
  },
  icon: {
    marginTop: 6,
    width: 40,
    height: 40,
  },
  // fileSize: {
  //   fontSize: 13,
  //   color: '#8f91a8',
  // },
  // modifyTime: {
  //   fontSize: 12,
  //   color: 'rgba(39, 38, 77, 0.65)',
  // },
}));
