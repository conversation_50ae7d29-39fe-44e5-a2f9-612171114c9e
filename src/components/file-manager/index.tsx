import React, { useState, useEffect, useRef } from 'react';
import { message, Modal, Tabs, Table, Button } from 'antd';
import { ExclamationCircleOutlined, PlusOutlined, DeleteOutlined, DownloadOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { useStyles } from './index.styles';
import { useColumns } from './columns';
import { ListUserSessionArtifacts } from '@/server/WuyingAI/ListUserSessionArtifacts';
import { ListUserArtifactDownloadUrls } from '@/server/WuyingAI/ListUserArtifactDownloadUrls';
import { DeleteUserArtifacts } from '@/server/WuyingAI/DeleteUserArtifacts';
import useAddToKBModal from '@/hooks/useAddToKBModal';
import commonState from '@/model/commonModel';
import { AgentIdEnum } from '@/constant/enum';
import { useTabContext } from '@/contexts/TabContext';
import { downloadFiles } from '@/utils';
export interface FileItem {
  ArtifactId: string;
  ArtifactType: string;
  FileName: string;
  FileType: string;
  FileSize: string;
  GmtModified: string;
  icon: string;
  FileFormat?: string;
}

interface FileManagerProps {
  sessionId?: string;
  agentId?: AgentIdEnum;
}

interface ListUserSessionArtifactsParams {
  ArtifactTypes: string[];
  MaxResults?: number;
  SessionId?: string;
}

interface ListUserArtifactDownloadUrlsParams {
  ArtifactIds: string[];
  FileFormat?: string;
  SessionId?: string;
}


interface DeleteUserArtifactsParams {
  ArtifactIds: string[];
  SessionId?: string;
}


const FileManager: React.FC<FileManagerProps> = (props) => {
  const { styles } = useStyles();
  const [activeTab, setActiveTab] = useState<string>('artifactFile');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const { showAddKbModal } = useAddToKBModal();
  const { sessionId } = commonState;
  const { agentId } = props;
  const tabsCount = useRef<{ [key: string]: any }>({
    artifactFile: null,
    sessionFile: null,
  });
  const { activeKey } = useTabContext();

  // 获取文件列表
  const { data: fileListResponse, loading, run: fetchFiles, refresh } = useRequest(
    async (params: ListUserSessionArtifactsParams) => {
      if (!params.SessionId) {
        return {
          Data: [],
          TotalCount: 0,
          NextToken: '',
        };
      }
      const res = await ListUserSessionArtifacts(params);
      return res;
    },
    {
      manual: true,
      onSuccess: (result) => {
        tabsCount.current[activeTab] = result?.TotalCount || 0;
      },
      onError: (error) => {
        message.error('获取文件列表失败');
      },
    },
  );
  // 下载文件
  const { loading: downloadLoading, run: runDownload } = useRequest(
    async (params: ListUserArtifactDownloadUrlsParams) => {
      message.success('正在下载文件，请稍等...');
      const res = await ListUserArtifactDownloadUrls({
        ...params,
        ArtifactIds: params.ArtifactIds,
        SessionId: sessionId,
      });
      return res?.Data || [];
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.FailedFiles.length > 0) {
          Modal.error({
            title: '以下制品下载失败',
            content: (
              <div>
                {result.FailedFiles.map((item) => (
                  <div key={item.ArtifactId}>
                    {item.FileName}
                  </div>
                ))}
              </div>
            ),
          });
        }
        if (result?.DownloadLinks?.length > 0) {
          downloadFiles(result?.DownloadLinks);
          setSelectedRowKeys([]);
        }
      },
      onError: (error) => {
        message.error('下载失败');
      },
    },
  );

  // 删除文件
  const { loading: deleteLoading, run: runDelete } = useRequest(
    async (params: DeleteUserArtifactsParams) => {
      const res = await DeleteUserArtifacts({
        ...params,
        ArtifactIds: params.ArtifactIds,
        SessionId: sessionId,
      });
      return res;
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result?.Data?.FailedFiles?.length > 0) {
          const failedFiles = result?.Data?.FailedFiles;
          message.error(`以下文件删除失败: ${failedFiles.map((item) => item.FileName)}`);
        } else {
          message.success(result?.Message || '文件删除成功');
        }
        setSelectedRowKeys([]);
        refresh();
      },
      onError: (error) => {
        message.error('删除失败');
      },
    },
  );

  // 显示删除确认对话框
  const showDeleteConfirm = (artifactIds: string[], fileNames: string[], onConfirm: () => void) => {
    const isMultiple = artifactIds.length > 1;
    const title = isMultiple ? `确认删除 ${artifactIds.length} 个文件？` : '确认删除文件？';
    const content = isMultiple
      ? `您即将删除 ${artifactIds.length} 个文件，此操作不可撤销。`
      : `您即将删除文件"${fileNames[0]}"，此操作不可撤销。`;

    Modal.confirm({
      title,
      content,
      icon: <ExclamationCircleOutlined />,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: onConfirm,
    });
  };

  // 当tab切换时获取数据
  useEffect(() => {
    fetchFiles({
      ArtifactTypes: [activeTab],
      MaxResults: 100,
      SessionId: sessionId,
    });
  }, [activeTab, fetchFiles, sessionId, activeKey]);

  const handleMenuClick = (key: string, record: FileItem) => {
    switch (key) {
      case 'addToKnowledge':
        handleAddToKnowledge([record.ArtifactId]);
        break;
      case 'delete':
        handleDelete([record.ArtifactId]);
        break;
      case 'downloadPPT-ppt':
      case 'downloadPPT-pdf':
      case 'downloadPPT-jpeg':
      case 'downloadPPT-png':
        handleDownload({ ...record, FileFormat: key.split('-')[1] });
        break;
      default:
        break;
    }
  };

  const handleDelete = (artifactIds: string[]) => {
    const deleteFileNames = (fileListResponse?.Data || [])?.filter((file) =>
      artifactIds?.includes(file?.ArtifactId)).map((file) => file?.FileName);
    showDeleteConfirm(selectedRowKeys, deleteFileNames, () => {
      runDelete({ ArtifactIds: artifactIds });
    });
  };

  const handleAddToKnowledge = (artifactIds: string[]) => {
    console.log('lxy handleAddToKnowledge ', sessionId);
    showAddKbModal({
      sessionId: sessionId!,
      messageIgnore: true,
      fileIdList: artifactIds,
    });
  };

  const handleBatchAction = (action: string) => {
    if (selectedRowKeys?.length === 0) {
      message.warning('请先选择文件');
      return;
    }
    switch (action) {
      case 'download':
        runDownload({ ArtifactIds: selectedRowKeys });
        break;
      case 'addToKnowledge':
        handleAddToKnowledge(selectedRowKeys);
        break;
      case 'delete':
        handleDelete(selectedRowKeys);
        break;
      default:
        break;
    }
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSelectedRowKeys([]);
  };

  const handleSelectionChange = (newSelectedRowKeys: string[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const getFileCount = (tabKey: string) => {
    return tabKey === activeTab ? (fileListResponse?.Data || []).length : 0;
  };

  const handleDownload = (record: FileItem & { FileFormat?: string }) => {
    runDownload({ ArtifactIds: [record.ArtifactId], FileFormat: record.FileFormat });
  };

  const columns: any = useColumns({
    onMenuClick: handleMenuClick,
    onDownload: handleDownload,
    agentId,
    activeTab,
    sessionId,
  });

  const rowSelection: any = {
    selectedRowKeys,
    onChange: handleSelectionChange,
  };

  const tabItems = [
    {
      key: 'artifactFile',
      label: `结果文件${tabsCount.current.artifactFile ? `（${tabsCount.current.artifactFile}）` : ''}`,
    },
    {
      key: 'sessionFile',
      label: `上传文件${tabsCount.current.sessionFile ? `（${tabsCount.current.sessionFile}）` : ''}`,
    },
  ];

  return (
    <div className={styles.container}>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        className={styles.tabs}
      />

      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={fileListResponse?.Data || []}
        pagination={false}
        className={styles.fileTable}
        size="middle"
        showHeader={false}
        rowKey="ArtifactId"
        loading={loading}
      />

      <div className={styles.bottomBar}>
        <span className={styles.selectedInfo}>已选中 {selectedRowKeys.length} 项</span>
        <Button
          icon={<DownloadOutlined />}
          disabled={selectedRowKeys.length === 0}
          loading={downloadLoading}
          onClick={() => handleBatchAction('download')}
          className={styles.actionButton}
        >
          下载
        </Button>
        <Button
          icon={<PlusOutlined />}
          disabled={selectedRowKeys.length === 0}
          onClick={() => handleBatchAction('addToKnowledge')}
          className={styles.actionButton}
        >
          添加到知识库
        </Button>
        <Button
          icon={<DeleteOutlined />}
          disabled={selectedRowKeys.length === 0}
          loading={deleteLoading}
          onClick={() => handleBatchAction('delete')}
          className={styles.actionButton}
        >
          删除
        </Button>
      </div>
    </div>
  );
};

export default FileManager;
