import React from 'react';
import { Button, Dropdown, Space, message } from 'antd';
import { useRequest } from 'ahooks';
import { MoreOutlined, PlusOutlined, DeleteOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FileItem } from './index';
import { useStyles } from './index.styles';
import { AgentIdEnum } from '@/constant/enum';
import { Iconfont } from '@/components/icon';
import { PPT_DOWNLOAD_ITEMS } from '@/components/ppt-download';
import { getFileIconByFileName, formatFileSize } from '@/utils/file';
import { GetArtifactPreview } from '@/server/WuyingAI/GetArtifactPreview';
import useAiPPT from '@/hooks/useAiPPT';
import usePreviewUrl from '@/hooks/usePreviewUrl';

interface ColumnsProps {

  onMenuClick: (key: string, record: FileItem) => void;
  onDownload: (record: FileItem & { FileFormat?: string }) => void;
  agentId?: AgentIdEnum;
  activeTab: string;
  sessionId?: string;
}

interface OperationsProps extends ColumnsProps {
  record: FileItem;
}

const Operations = (props: OperationsProps) => {
  const { record, onMenuClick, onDownload, agentId, activeTab, sessionId } = props;
  const { styles } = useStyles();
  const { openEditTab } = useAiPPT();
  const { previewUrl } = usePreviewUrl();
  const { runAsync: runAsyncPreview } = useRequest(GetArtifactPreview, { manual: true });
  const isAiPPTAndArtifactFile = agentId === AgentIdEnum.ppt && activeTab === 'artifactFile';
  const getDropdownItems = () => [
    {
      key: 'addToKnowledge',
      label: (
        <Space>
          <PlusOutlined />
          添加到知识库
        </Space>
      ),
    },
    ...(
      isAiPPTAndArtifactFile
        ? [
          {
            key: 'ppt-download',
            label: (
              <Space>
                <DownloadOutlined />
                下载
              </Space>
            ),
            children: PPT_DOWNLOAD_ITEMS,
          },
        ]
        : []
    ),
    {
      key: 'delete',
      label: (
        <Space>
          <DeleteOutlined />
          删除
        </Space>
      ),
    },
  ];

  // 文件预览
  const handlePreview = async () => {
    if (!record?.ArtifactId || !sessionId) return;
    const res = await runAsyncPreview({
      ArtifactId: record.ArtifactId,
      SessionId: sessionId,
    });

    if (!res?.Data?.Url) {
      message.error(res?.Message || '文件预览地址不存在');
      return;
    }
    previewUrl(res?.Data?.Url || '', {
      label: record.FileName,
      key: record.ArtifactId,
    });
  };

  // ppt编辑
  const handlePPTEdit = async () => {
    if (!record?.ArtifactId || !sessionId) return;
    const res = await runAsyncPreview({
      ArtifactId: record.ArtifactId,
      SessionId: sessionId,
    });
    openEditTab({ pptId: res?.Data?.Url } as any);
  };

  return (
    <Space size={4}>
      {
        !isAiPPTAndArtifactFile && (
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => onDownload(record)}
            className={styles.actionIcon}
          />
        )
      }
      {
        isAiPPTAndArtifactFile
          ? (
            <Button
              type={'text'}
              onClick={() => handlePPTEdit()}
              icon={<Iconfont type={'edit--outline'} className={styles.editIcon} />}
            />
          ) : (
            <Button
              type="text"
              disabled={agentId === AgentIdEnum.alpha} // 主agent不支持预览
              icon={<EyeOutlined />}
              onClick={() => handlePreview()}
              className={styles.actionIcon}
            />
          )
      }
      <Dropdown
        menu={{
          items: getDropdownItems(),
          onClick: ({ key }) => onMenuClick(key, record),
        }}
        trigger={['click']}
      >
        <Button type="text" icon={<MoreOutlined />} className={styles.actionIcon} />
      </Dropdown>
    </Space>
  );
};

export const useColumns = ({ onMenuClick, onDownload, agentId, activeTab, sessionId }: ColumnsProps): ColumnsType<FileItem> => {
  const { styles } = useStyles();
  const isAiPPTAndArtifactFile = agentId === AgentIdEnum.ppt && activeTab === 'artifactFile';
  return [
    {
      title: '文件名',
      dataIndex: 'FileName',
      key: 'FileName',
      width: 280,
      minWidth: 200,
      ellipsis: true,
      render: (text: string, record: FileItem) => (
        <div className={styles.fileNameCell}>
          <Iconfont type={getFileIconByFileName(text)} className={styles.fileIcon} />
          <span className={styles.fileName} title={text}>{text}</span>
        </div>
      ),
    },
    {
      title: '修改时间',
      dataIndex: 'GmtModified',
      key: 'GmtModified',
      width: 140,
      minWidth: 120,
      ellipsis: true,
      render: (text: string) => <span className={styles.dateText} title={text}>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      responsive: ['md'],
    },
    ...(
      isAiPPTAndArtifactFile
        ? []
        : [
          {
            title: '大小',
            dataIndex: 'FileSize',
            key: 'FileSize',
            width: 80,
            minWidth: 60,
            ellipsis: true,
            render: (text: number) => <span className={styles.metaText}>{formatFileSize(text)}</span>,
          },
        ]
    ),
    {
      title: '操作',
      key: 'action',
      width: 120,
      minWidth: 100,
      render: (_, record) => (
        <Operations
          record={record}
          agentId={agentId}
          onDownload={onDownload}
          onMenuClick={onMenuClick}
          activeTab={activeTab}
          sessionId={sessionId}
        />
      ),
    },
  ];
};
