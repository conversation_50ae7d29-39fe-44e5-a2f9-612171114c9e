import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token, css }) => ({
  container: css`
    background: #ffffff;
    display: flex;
    flex-direction: column;
    width: 100%;
    min-width: 400px;
    height: 100%;
    /* height: 100vh; */
    overflow: scroll;
  `,

  tabs: css`
    padding: 0 20px;
    height: 52px;
    flex-shrink: 0;
    
    .ant-tabs-tab {
      padding: 16px 0;
      margin: 0 24px 0 0;
    }

    .ant-tabs-tab-btn {
      font-size: 14px;
      font-weight: 400;
      color: #1f2024;
    }

    .ant-tabs-tab-active .ant-tabs-tab-btn {
      font-weight: 500;
      color: #0075ff;
    }

    .ant-tabs-ink-bar {
      background: #0075ff;
      height: 3px;
    }
  `,

  fileTable: css`
    flex: 1;
    overflow-y: auto;
    overflow-x: auto;
    min-height: 0;

    .ant-table {
      background: transparent;
      border: none;
      width: 100%;
      min-width: 600px;
      table-layout: fixed;
    }

    .ant-table-container {
      border: none;
    }

    .ant-table-content {
      border: none;
    }

    .ant-table-tbody > tr > td {
      padding: 16px 20px;
      border: none;
      border-bottom: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-table-tbody > tr:hover > td {
      background: #f8f9fa;
    }

    .ant-table-thead > tr > th {
      border: none;
      background: transparent;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    @media (max-width: 768px) {
      .ant-table {
        min-width: 500px;
      }
      
      .ant-table-tbody > tr > td {
        padding: 12px 16px;
      }
    }

    @media (max-width: 480px) {
      .ant-table {
        min-width: 400px;
      }
      
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
      }
    }
  `,

  fileNameCell: css`
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    width: 100%;
  `,

  fileIcon: css`
    width: 28px;
    height: 28px;
    flex-shrink: 0;

    @media (max-width: 768px) {
      width: 24px;
      height: 24px;
    }
  `,


  fileName: css`
    font-size: 14px;
    font-weight: 400;
    color: #474a52;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;

    @media (max-width: 768px) {
      font-size: 13px;
    }

    @media (max-width: 480px) {
      font-size: 12px;
    }
  `,

  dateText: css`
    font-size: 14px;
    font-weight: 400;
    color: #8c909c;
  `,

  metaText: css`
  font-size: 14px;
  font-weight: 400;
  color: #8c909c;
  line-height: 20px;
`,

  sizeText: css`
    font-size: 14px;
    font-weight: 400;
    color: #8c909c;
  `,

  bottomBar: css`
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 24px;
    flex-shrink: 0;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      padding: 12px 16px;
      gap: 16px;
    }

    @media (max-width: 480px) {
      padding: 8px 12px;
      gap: 12px;
      flex-direction: column;
      align-items: flex-start;
    }
  `,

  selectedInfo: css`
    font-size: 14px;
    font-weight: 400;
    color: #474a52;
  `,

  actionButton: css`
    border-radius: 8px;
    background: #f2f5fa;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #b8bbc2;
    height: 36px;
    padding: 8px 12px;
    white-space: nowrap;

    &:not(:disabled) {
      color: #1f2024;
      background: #f2f5fa;
    }

    &:not(:disabled):hover {
      background: #e8ecf2;
      color: #0075ff;
    }

    &:disabled {
      background: #f2f5fa;
      color: #b8bbc2;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      font-size: 13px;
      height: 32px;
      padding: 6px 10px;
    }

    @media (max-width: 480px) {
      font-size: 12px;
      height: 30px;
      padding: 5px 8px;
    }
  `,

  actionIcon: css`
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c909c;
    
    &:hover {
      color: #0075ff;
      background-color: #f0f7ff;
    }

    .anticon {
      font-size: 14px;
    }

    @media (max-width: 768px) {
      width: 24px;
      height: 24px;
      
      .anticon {
        font-size: 12px;
      }
    }
  `,
  editIcon: css`
    width: 18px;
    height: 18px;
    fill: #474A52;
  `,
}));
