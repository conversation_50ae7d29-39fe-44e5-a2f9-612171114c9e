import React from 'react';
import { Space, Typography, Flex, Spin } from 'antd';
import { useStyles } from './index.styles';
import { Iconfont } from '@/components/icon';
import { deepIconMap, DeepIconType } from '@/constant/icon';
import MarkDownPreview from '@/components/markdown';


interface ActivityItemProps {
  iconKey?: DeepIconType;
  question?: string;
  title?: string;
  content?: string;
}
const { Text, Paragraph } = Typography;
const ActivityItem: React.FC<ActivityItemProps> = ({ iconKey = 'web_search', title, question, content = '' }) => {
  const { styles } = useStyles();
  return (
    <div className={styles.activityItem}>
      <Flex justify="space-between" align="flex-start" className={styles.header}>
        <Paragraph className={styles.description} ellipsis={{ rows: 2, expandable: true }}>
          {title}
        </Paragraph>
      </Flex>
      <div className={styles.searchBox}>
        <Space className={styles.searchContent} size={10}>
          <Iconfont type={deepIconMap[iconKey]} style={{ width: 20, height: 20, flexShrink: 0 }} />
          <Text className={styles.searchText}>{question}</Text>
        </Space>
        { content && <Text className={styles.searchText}>{<MarkDownPreview context={content} />}</Text>}
      </div>
    </div>
  );
};

export default ActivityItem;
