import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  activityItem: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    gap: token.marginSM,
  },
  header: {
    width: '100%',
    // marginBottom: token.marginXS,
  },
  description: {
    fontSize: token.fontSizeSM,
    color: token.colorTextSecondary,
    marginBottom: 0,
    flex: 1,
    marginRight: token.marginSM,
    '&.ant-typography': {
      marginBottom: 0,
    },
  },
  activityTag: {
    flexShrink: 0,
    lineHeight: '16px',
    height: 'auto',
    padding: '2px 6px',
  },
  searchBox: {
    borderRadius: token.borderRadius,
    background: token.colorFillTertiary,
    padding: `${token.paddingXS}px ${token.paddingSM}px`,
    // border: `1px solid ${token.colorBorderSecondary}`,
    transition: `all ${token.motionDurationSlow}`,
    // '&:hover': {
    //   background: token.colorFillSecondary,
    //   borderColor: token.colorPrimary,
    // },
  },
  searchContent: {
    width: '100%',
    '& .ant-space-item': {
      display: 'flex',
      alignItems: 'center',
    },
  },
  searchText: {
    width: '100%',
    fontSize: token.fontSize,
    color: token.colorText,
    lineHeight: token.lineHeight,
    // fontWeight: token.fontWeightStrong,
    // whiteSpace: 'nowrap',
    overflow: 'auto',
    // textOverflow: 'ellipsis',
    flex: 1,
  },
}));
