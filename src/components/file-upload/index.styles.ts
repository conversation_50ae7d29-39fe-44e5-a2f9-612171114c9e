import { createStyles } from 'antd-style';

export const useFileUploaderStyles = createStyles(({ token }) => ({
  container: {
    maxWidth: '800px',
    margin: '0 auto',
    padding: '24px',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '24px',
  },
  uploadButton: {
    height: '40px',
    fontSize: '14px',
    fontWeight: 500,
  },
  uploadList: {
    marginBottom: '16px',
  },
  emptyState: {
    textAlign: 'center',
    padding: '40px',
    color: '#999',
    backgroundColor: '#fafafa',
    borderRadius: '8px',
  },
  fileGrid: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '12px',
  },
  statistics: {
    marginTop: '24px',
    padding: '16px',
    backgroundColor: '#f6f8fa',
    borderRadius: '8px',
  },
}));

export const useFileUploadItemStyles = createStyles(({ token }) => ({
  container: {
    position: 'relative',
    maxWidth: '216px',
    height: '56px',
    backgroundColor: '#F2F5FA',
    borderRadius: '8px',
    padding: '8px 20px 8px 8px',
    display: 'flex',
    alignItems: 'center',
    margin: '0 8px 0 0',
  },
  imgContainer: {
    width: '56px',
  },
  fileIcon: {
    width: '40px',
    height: '40px',
    marginRight: '8px',
    flexShrink: 0,
  },
  iconWrapper: {
    width: '40px',
    height: '40px',
    backgroundColor: '#FF5A5F',
    borderRadius: '6px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '16px',
    fontWeight: 'bold',
  },
  fileInfo: {
    flex: 1,
    minWidth: 0,
  },
  fileName: {
    fontSize: '14px',
    fontWeight: 500,
    color: '#1F2024',
    lineHeight: '20px',
    marginBottom: '2px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  fileStatus: {
    fontSize: '12px',
    lineHeight: '16px',
  },
  closeButton: {
    position: 'absolute',
    top: '2px',
    right: '4px',
    width: '12px',
    height: '12px',
    backgroundColor: '#8C909C',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'opacity 0.2s',
    '&:hover': {
      opacity: 0.7,
    },
  },
  closeIcon: {
    fontSize: '8px',
    color: 'white',
    lineHeight: 1,
  },
  avatarStyle: {
    backgroundColor: 'transparent',
    width: 56,
    height: 56,
  },
  loadingIconStyle: {
    fontSize: '24px',
    color: '#0075FF',
  },
  errorIconStyle: {
    fontSize: '24px',
    color: '#E51A1A',
  },
}));
