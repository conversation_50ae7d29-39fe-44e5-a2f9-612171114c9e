import { FileItem } from '@/server/upLoad';
import { fileIconsMap } from '@/constant/icon';
export const getStatusText = (
  status: FileItem['status'],
  progress: number,
  size?: number,
): string => {
  switch (status) {
    case 'done':
      return '上传完成';
    case 'error':
      return '上传失败';
    case 'uploading':
      return `上传中  ${progress}%`;
    case 'parsing':
      return '解析中';
    case 'ready':
      return '准备上传';
    case 'failed':
      return '解析失败';
    case 'completed':
      return getFileSizeText(size || 0);
    default:
      return '等待上传';
  }
};

export const getStatusColor = (status: FileItem['status']): string => {
  switch (status) {
    case 'error':
    case 'failed':
      return '#E51A1A';
    default:
      return '#8C909C';
  }
};

export const getExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || '';
};

export const isImageFile = (fileName: string): boolean => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
  const extension = getExtension(fileName);
  return imageExtensions.includes(extension);
};

export const getFilePreviewUrl = (file: FileItem): string | null => {
  if (file.file && isImageFile(file.name)) {
    return URL.createObjectURL(file.file);
  }
  return null;
};

export const getFileSizeText = (size: number) => {
  if (isNaN(size)) {
    return '0KB';
  }
  return size / 1024 > 1024
    ? `${(size / 1024 / 1024).toFixed(2)}MB`
    : `${(size / 1024).toFixed(2)}KB`;
};
