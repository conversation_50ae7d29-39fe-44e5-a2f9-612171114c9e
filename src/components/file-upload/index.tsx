import React, { useRef } from 'react';
import { Typography, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useOSSUpload } from '@/hooks/useOSSUpload';
import FileUploadItem from './FileUploadItem';
import { useFileUploaderStyles } from './index.styles';

const { Title, Text } = Typography;

export const ChatFileUpload = FileUploadItem;

// const FileUploader: React.FC = () => {
//   const { styles } = useFileUploaderStyles();
//   const { uploadList, startUpload, removeFile } = useOSSUpload();
//   const fileInputRef = useRef<HTMLInputElement>(null);
//   // 处理文件选择
//   const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const { files } = event.target;
//     if (files) {
//       Array.from(files).forEach((file) => {
//         startUpload(file);
//       });
//     }
//     // 清空 input 值，允许重复选择同一文件
//     if (fileInputRef.current) {
//       fileInputRef.current.value = '';
//     }
//   };

//   // 触发文件选择
//   const handleUploadClick = () => {
//     fileInputRef.current?.click();
//   };

//   return (
//     <div className={styles.container}>
//       <div className={styles.header}>
//         <Title level={2}>文件列表</Title>
//         <Button
//           type="primary"
//           icon={<UploadOutlined />}
//           onClick={handleUploadClick}
//           className={styles.uploadButton}
//         >
//           上传文件
//         </Button>
//       </div>
//       {/* 隐藏的文件输入框 */}
//       <input
//         ref={fileInputRef}
//         type="file"
//         multiple
//         style={{ display: 'none' }}
//         onChange={handleFileSelect}
//         accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.mkv,.avi,.mov,.wmv,.mp3,.wav,.aac,.epub,.mobi,.html,.htm,.rtf,.md,.xlsm"
//       />

//       {/* 文件列表 */}
//       <div>
//         <Title level={4} className={styles.uploadList}>
//           文件列表 ({uploadList.length})
//         </Title>

//         {uploadList.length === 0 ? (
//           <div className={styles.emptyState}>
//             <Text type="secondary">暂无文件</Text>
//           </div>
//         ) : (
//           <div className={styles.fileGrid}>
//             {uploadList.map((file) => (
//               <FileUploadItem key={file.fileId} file={file} onRemove={removeFile} />
//             ))}
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default FileUploader;
