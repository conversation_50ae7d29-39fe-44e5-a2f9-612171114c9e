import React from 'react';
import { CloseOutlined, LoadingOutlined, InfoCircleFilled } from '@ant-design/icons';
import { FileItem } from '@/server/upLoad';
import { Image, Avatar } from 'antd';
import { Iconfont } from '@/components/icon';
import { useFileUploadItemStyles } from './index.styles';
import { getStatusText, getStatusColor, getFilePreviewUrl } from './utils';
import { getFileIconByFileName } from '@/utils/file';

// const VALID_EXTENSIONS = {
//   documents: ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.xlsm'],
//   text: ['.md', '.html', '.htm', '.epub', '.mobi', '.rtf', '.txt'],
//   images: ['.jpg', '.jpeg', '.png', '.bmp', '.gif'],
//   media: ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.mp3', '.wav', '.aac']
// };

interface FileUploadItemProps {
  file: FileItem;
  onRemove: (uid: string) => void;
}

const FileUploadItem: React.FC<FileUploadItemProps> = ({ file, onRemove }) => {
  const { styles, cx } = useFileUploadItemStyles();

  // 加载中的 Avatar
  const renderLoadingAvatar = () => (
    <Avatar shape="square" className={styles.avatarStyle}>
      <LoadingOutlined className={styles.loadingIconStyle} />
    </Avatar>
  );

  // 错误状态的 Avatar
  const renderErrorAvatar = () => (
    <Avatar shape="square" className={styles.avatarStyle}>
      <InfoCircleFilled className={styles.loadingIconStyle} />
    </Avatar>
  );

  // 成功状态的 Image
  const renderImage = (previewUrl: string) => (
    <Image
      src={previewUrl}
      alt={file.name}
      style={{ width: '56px', height: '56px', objectFit: 'cover' }}
    />
  );
  const renderPreview = () => {
    const previewUrl = getFilePreviewUrl(file);

    if (!previewUrl) {
      return (
        <div className={styles.fileIcon}>
          <Iconfont type={getFileIconByFileName(file.name)} style={{ width: 40, height: 40 }} />
        </div>
      );
    }
    if (file.status === 'completed' && previewUrl) {
      return renderImage(previewUrl);
    }

    if (file.status === 'error' || file.status === 'failed') {
      return renderErrorAvatar();
    }

    // 默认加载状态
    return renderLoadingAvatar();
  };

  return (
    <div className={cx(styles.container, { [styles.imgContainer]: !!getFilePreviewUrl(file) })}>
      {/* 文件图标 */}
      {renderPreview()}
      {/* 文件信息 */}
      {getFilePreviewUrl(file) ? null : (
        <div className={styles.fileInfo}>
          <div className={styles.fileName}>{file.name}</div>
          <div className={styles.fileStatus} style={{ color: getStatusColor(file.status) }}>
            {file.status === 'parsing' && (
              <LoadingOutlined style={{ marginRight: '4px', fontSize: '12px' }} />
            )}
            {getStatusText(file.status, file.progress, file.size)}
          </div>
        </div>
      )}
      {/* 关闭按钮 */}
      <div className={styles.closeButton} onClick={() => onRemove(file.fileId)}>
        <CloseOutlined className={styles.closeIcon} />
      </div>
    </div>
  );
};

export default FileUploadItem;
