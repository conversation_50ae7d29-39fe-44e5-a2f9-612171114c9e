import React from 'react';
import { Dropdown } from 'antd';

interface PptDownloadProps {
  children: React.ReactNode;
  handleMenuClick: ({ FileFormat }: { FileFormat: string }) => void;
}

export const PPT_DOWNLOAD_ITEMS = [
  {
    key: 'downloadPPT-ppt',
    label: 'PPT下载',
  },
  {
    key: 'downloadPPT-pdf',
    label: 'PDF下载',
  },
  {
    key: 'downloadPPT-jpeg',
    label: 'JPG下载',
  },
  {
    key: 'downloadPPT-png',
    label: 'PNG下载',
  },
];

const PptDownload: React.FC<PptDownloadProps> = ({ children, handleMenuClick }) => {
  const onMenuClick = ({ key }: { key: string }) => {
    const format = key.split('-')[1];
    handleMenuClick({ FileFormat: format as string });
  };
  return (
    <Dropdown
      trigger={['click']}
      menu={{
        items: PPT_DOWNLOAD_ITEMS,
        onClick: onMenuClick,
      }}
    >
      { children }
    </Dropdown>
  );
};

export default PptDownload;
