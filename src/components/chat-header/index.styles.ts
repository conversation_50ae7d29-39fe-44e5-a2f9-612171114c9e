import { createStyles } from 'antd-style';

export const useChatHeaderStyles = createStyles(({ css }) => {
  return {
    wrapper: css`
      display: flex;
      width: 100%;
      justify-content: flex-end;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      line-height: 24px;
      color: #1f2024;
    `,
    normalIcon: css`
      width: 24px;
      height: 24px;
      cursor: pointer;
      margin: 0 8px;
    `,
    icon: css`
      width: 24px;
      height: 24px;
      cursor: pointer;
      margin: 0 8px;
      transform: rotate(180deg);
      transition: transform 0.3s ease-in-out;
    `,
    iconRotate: css`
      transform: rotate(0);
    `,
    left: css`
      display: flex;
      align-items: center;
    `,
    title: css`
      margin-left: 8px;
    `,
    desktopSelectorWrapper: css`
      display: flex;
      flex-direction: column;
      align-items: center;
    `,
    desktopItem: css`
      width: 100%;
      border-radius: 12px;
      box-sizing: border-box;
      border: 1px solid #e6e8eb;
      display: flex;
      align-items: center;
      padding: 12px 4px;
      margin: 4px 0;
      cursor: pointer;
    `,
    desktopItemDisabled: css`
    cursor: not-allowed;
    pointer-events: none;
    color: #b8bbc2;
    & svg {
      fill: #b8bbc2;
    }
    `,
    desktopTitle: css`
      flex: 1;
      display: flex;
      align-items: center;
    `,
    desktopCheck: css`
      width: 40px;
      display: flex;
      align-items: center;
    `,
  };
});
