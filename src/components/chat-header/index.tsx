import { AgentIdEnum, DesktopStatusEnum, DesktopStatusTextMap } from '@/constant/enum';
import { Iconfont } from '../icon';
import { useChatHeaderStyles } from './index.styles';
import { useRequest } from 'ahooks';
import { Tooltip, Popover } from 'antd';
import { useEffect } from 'react';
import { ListUserDesktop } from '@/server/WuyingAI/ListUserDesktop';
import { DescribeUserSetting } from '@/server/WuyingAI/DescribeUserSetting';
import { useTabContext } from '@/contexts/TabContext';

// 兜底环境
const agentbayEnvironment = {
  DesktopId: 'Agentbay',
  Name: 'Agentbay',
};

const ChatHeader = ({
  agentId,
  selectedEnvironment,
  onSelectEnvironment,
  onNewChat,
  handleOpenTab = () => {},
}: {
  agentId?: AgentIdEnum;
  selectedEnvironment?: {
    Name: string;
    DesktopId: string;
  } | null;
  onSelectEnvironment?: (val: {
    Name: string;
    DesktopId: string;
  } | null) => void;
  onNewChat: () => void;
  handleOpenTab?: () => void;
}) => {
  const { styles, cx } = useChatHeaderStyles();
  const { tabVisible } = useTabContext();

  const { data: desktopList, runAsync: getDesktopList } = useRequest(() => ListUserDesktop({}));

  // 从设置中获取默认选择的桌面
  useRequest(() => DescribeUserSetting({}).then((res) => {
    // 偏好设置不可用则默认选中agentbay
    const defaultEnvironment = res?.Data?.AvailableEnvironments?.find?.((it) => it.DesktopId === res?.Data?.CurrentSettings?.DesktopId && it.DesktopStatus === DesktopStatusEnum.Running) || agentbayEnvironment;
    onSelectEnvironment?.(defaultEnvironment);
  }));

  useEffect(() => {
    const selected = desktopList?.Data?.Environments?.find?.((it) => it.DesktopId === selectedEnvironment?.DesktopId);
    if (selected?.DesktopStatus !== DesktopStatusEnum.Running && selectedEnvironment?.DesktopId !== agentbayEnvironment?.DesktopId) {
      // 非运行状态改选默认环境
      onSelectEnvironment?.(agentbayEnvironment);
    }
  }, [desktopList, selectedEnvironment, onSelectEnvironment]);

  return (
    <div className={styles.wrapper}>
      {/* <div className={styles.left}>
        <span className={styles.title}>{titleMap[agentId]}</span>
      </div> */}
      <div>
        <Tooltip title="新对话" trigger="hover">
          <Iconfont type="add" className={styles.icon} onClick={onNewChat} />
        </Tooltip>
        <Popover
          title="执行环境"
          content={
            <div className={styles.desktopSelectorWrapper}>
              {desktopList?.Data?.Environments?.map((it) => (
                <div
                  key={it.DesktopId}
                  className={cx(styles.desktopItem, {
                    [styles.desktopItemDisabled]: it.DesktopStatus !== DesktopStatusEnum.Running,
                  })}
                  onClick={() =>
                    onSelectEnvironment?.(it.DesktopId === selectedEnvironment?.DesktopId ? null : it)
                  }
                >
                  <div className={styles.desktopTitle}>
                    <Iconfont type="window-logo" className={styles.icon} fill="#0075FF" />
                    <span>{it.Name}{it.DesktopStatus !== DesktopStatusEnum.Running ? `(${DesktopStatusTextMap[it.DesktopStatus as DesktopStatusEnum]})` : ''}</span>
                  </div>
                  <div className={styles.desktopCheck}>
                    {it.DesktopId === selectedEnvironment?.DesktopId ? (
                      <Iconfont type="check-small" className={styles.normalIcon} fill="#0075FF" />
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
          }
          placement="bottomRight"
          trigger="click"
          onOpenChange={(open) => {
            if (open) {
              // 刷新桌面状态
              getDesktopList();
            }
          }}
        >
          <Tooltip title="执行环境" trigger="hover">
            <Iconfont type="window-logo" className={cx(styles.icon)} />
          </Tooltip>
        </Popover>
        <Iconfont type="Unfold" className={cx(styles.icon, { [styles.iconRotate]: tabVisible })} onClick={handleOpenTab} />
      </div>
    </div>
  );
};

export default ChatHeader;
