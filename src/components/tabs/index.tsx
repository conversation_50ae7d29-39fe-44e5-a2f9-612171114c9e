import React from 'react';
import { Tabs as AntdTabs, TabsProps as AntdTabsProps } from 'antd';
import { useStyles } from './index.styles';

interface TabsProps extends AntdTabsProps {}

const Tabs: React.FC<TabsProps> = (props) => {
  const { styles, cx } = useStyles();
  const { rootClassName, ...restProps } = props;
  return (
    <AntdTabs type="editable-card" rootClassName={cx(styles.tabs, rootClassName)} {...restProps} />
  );
};

export default Tabs;
