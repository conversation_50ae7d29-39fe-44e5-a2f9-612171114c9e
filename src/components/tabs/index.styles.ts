import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  tabs: css`
    width: 100%;
    height: 100%;
    .ant-tabs-nav {
      margin: 0;

      .ant-tabs-extra-content {
        margin-right: 40px;
      }

      .ant-tabs-tab,
      .ant-tabs-tab-active,
      .ant-tabs-tab:hover,
      .ant-tabs-tab-btn {
        color: #1f2024 !important;
        border: none !important;
      }
      .ant-tabs-tab {
        width: 240px;
        justify-content: space-between;
        padding: 10px 14px !important;
        margin-bottom: 8px !important;
        border-radius: 12px !important;
        text-align: left;
      }
      .ant-tabs-tab-active {
        padding: 10px 14px 18px 14px !important;
        margin-bottom: 0 !important;
        border-radius: 12px 12px 0 0 !important;
      }
      .ant-tabs-tab + .ant-tabs-tab {
        margin-left: 8px !important;
      }
    }

    .ant-tabs-content-holder {
      border-radius: 12px;
      background: #fff;
    }
  `,
}));
