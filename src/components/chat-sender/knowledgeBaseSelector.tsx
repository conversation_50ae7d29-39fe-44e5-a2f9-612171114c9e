import { Drawer, Divider } from 'antd';
import { Iconfont } from '../icon';
import { useSelectorStyles } from './index.styles';
import { useState } from 'react';
import {
  ListKnowledgeBases,
  ListKnowledgeBasesResponse,
  ListKnowledgeBaseData,
} from '@/server/WuyingAI/ListKnowledgeBases';
import { useRequest } from 'ahooks';

const KnowledgeBaseSelector = ({
  selectedItems = [],
  onSelectChange,
  loading,
}: {
  loading: boolean;
  selectedItems: ListKnowledgeBaseData[];
  onSelectChange: (items: ListKnowledgeBaseData[]) => void;
}) => {
  const { styles, cx } = useSelectorStyles();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const { data: knowledgeBases } = useRequest(
    () => {
      if (drawerOpen) {
        return ListKnowledgeBases({});
      }
      return Promise.resolve({} as ListKnowledgeBasesResponse);
    },
    { refreshDeps: [drawerOpen] },
  );

  return (
    <>
      <div className={cx(styles.wrapper, { [styles.disabled]: loading })}>
        <Iconfont type="zhishiku" className={cx(styles.icon)} onClick={() => setDrawerOpen(true)} />
        {selectedItems.length ? <span className={styles.num}>{selectedItems.length}</span> : null}
      </div>
      <Drawer
        height="85%"
        title={null}
        placement="bottom"
        closable={false}
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
      >
        <div className={styles.drawerWrapper}>
          <div className={styles.drawerHeader}>
            <div className={styles.drawerTitle}>
              <span>知识库</span>
              <Iconfont type="add" className={styles.drawerIcon} />
            </div>
            <Iconfont
              onClick={() => setDrawerOpen(false)}
              type="close"
              className={styles.drawerIcon}
            />
          </div>
          <div className={styles.listItemWrapper}>
            {knowledgeBases?.Data?.map?.((it) => {
              const isSelected = selectedItems.find((selected) => selected.KbId === it.KbId);
              return (
                <div className={styles.listItem} key={it.KbId}>
                  <Iconfont type="ILL-FOLDER" className={styles.drawerIcon} />
                  <div className={styles.listItemDetail}>
                    <div>{it.Name}</div>
                    <div className={styles.listItemTip}>
                      共{it.DocumentCount}个文件
                      <Divider type="vertical" />
                      共{it.SessionCount}个会话
                    </div>
                  </div>
                  <div
                    className={styles.listItemButton}
                    onClick={() => {
                      if (isSelected) {
                        onSelectChange(
                          selectedItems.filter((selected) => selected.KbId !== it.KbId),
                        );
                      } else {
                        onSelectChange([...selectedItems, it]);
                      }
                    }}
                  >
                    {isSelected ? (
                      <Iconfont
                        type="checkmark"
                        style={{
                          width: 16,
                          height: 16,
                          fill: '#0075FF',
                        }}
                      />
                    ) : (
                      '启用'
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default KnowledgeBaseSelector;
