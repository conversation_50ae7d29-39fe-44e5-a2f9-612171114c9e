import { Drawer } from 'antd';
import { Iconfont } from '../icon';
import { useSelectorStyles } from './index.styles';
import { useState } from 'react';
// mock
const mockList = [
  {
    id: '1',
    title: '阿里云无影个人版产品V1.3文档汇总',
    tip: '666',
  },
];

const ExtensionSelector = ({
  selectedItems = [],
  onSelectChange,
}: {
  selectedItems: string[];
  onSelectChange: (items: string[]) => void;
}) => {
  const { styles } = useSelectorStyles();
  const [drawerOpen, setDrawerOpen] = useState(false);
  return (
    <>
      <div className={styles.wrapper} onClick={() => setDrawerOpen(true)}>
        <Iconfont type="zhishiku" className={styles.icon} />
        {selectedItems.length ? <span className={styles.num}>{selectedItems.length}</span> : null}
      </div>
      <Drawer
        height="85%"
        title={null}
        placement="bottom"
        closable={false}
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
      >
        <div className={styles.drawerWrapper}>
          <div className={styles.drawerHeader}>
            <div className={styles.drawerTitle}>
              <span>MCP扩展</span>
              <Iconfont type="add" className={styles.drawerIcon} />
            </div>
            <Iconfont
              onClick={() => setDrawerOpen(false)}
              type="close"
              className={styles.drawerIcon}
            />
          </div>
          <div className={styles.listItemWrapper}>
            {mockList.map((it) => (
              <div className={styles.listItem} key={it.id}>
                <Iconfont type="add" className={styles.drawerIcon} />
                <div className={styles.listItemDetail}>
                  <div>{it.title}</div>
                  <div>{it.tip}</div>
                </div>
                <div className={styles.listItemButton}>启用</div>
              </div>
            ))}
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default ExtensionSelector;
