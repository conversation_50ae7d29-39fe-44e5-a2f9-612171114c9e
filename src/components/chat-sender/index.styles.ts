import { createStyles } from 'antd-style';

export const useSenderStyles = createStyles(({ css }) => {
  return {
    wrapper: css`
      margin: 20px 0;

      border: 1px solid #d9d9d9;
      border-radius: 16px;
      transition: all 0.3s;
      &::after {
        display: none;
      }
      &:focus-within {
        border-color: transparent;
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;

        background-image:
          linear-gradient(to right, #fff, #fff),
          linear-gradient(
            to bottom,
            #79aeff 0%,
            #76d4ff 11.1%,
            rgba(119, 132, 255, 0.6498) 25.3%,
            rgba(105, 120, 255, 0.7) 40%,
            #ffa5d6 55.8%,
            #a579ff 78.6%,
            #79aeff 100%
          );
        box-shadow: 0px 4px 4px 0px rgba(0, 117, 255, 0.2);
      }
    `,
    fileUploader: css`
          display: inline-flex;
        .ant-upload {
          display: inline-flex;
        }
    `,
    sendButton: css`
      width: 36px !important;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px;
      border-radius: 12px !important;
      border: none;
      background: linear-gradient(135deg, #355dff 0%, #52bdff 100%) !important;
      color: #fff !important;
    `,
    loadingButton: css`
      background: #000;
      color: #b8bbc2 !important;
      opacity: 1 !important;
      background: #000 !important;
      pointer-events: all !important;
    `,
    loadingTip: css`
      display: flex;
      align-items: center;
    `,
    fileUploadList: css`
      padding: 12px 20px;
      display: flex;
      width: 100%;
      box-sizing: border-box;
      overflow-x: auto;
    `,
  };
});

export const useSelectorStyles = createStyles(({ css }) => {
  return {
    wrapper: css`
      background: #eaf3ff;
      border-radius: 8px;
      padding: 4px 4px;
      display: flex;
      align-items: center;
      color: #0075ff;
      cursor: pointer;
    `,
    disabled: css`
      color: #b8bbc2;
      pointer-events: none;
      cursor: not-allowed;

      & svg {
        fill: #b8bbc2;
      }
    `,
    icon: css`
      width: 20px;
      height: 20px;
      fill: #0075ff;
    `,
    num: css`
      margin: 0 4px;
    `,
    drawerWrapper: css`
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;
    `,
    drawerHeader: css`
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 20px;
    `,
    drawerTitle: css`
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 18px;
      font-weight: bold;
      line-height: 24px;
      color: #1f2024;
    `,
    drawerIcon: css`
      width: 20px;
      height: 20px;
      fill: #1f2024;
      margin: 0 8px;
      cursor: pointer;
    `,
    listItemWrapper: css`
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      overflow-y: auto;
      padding: 12px 8px;
      justify-content: flex-start;
      width: 100%;
    `,
    listItem: css`
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid #e6e8eb;
      border-radius: 12px;
      box-sizing: border-box;
      width: 100%;
      margin-bottom: 8px;
    `,
    listItemTip: css`
      font-size: 12px;
      color: #8f91a8;
    `,
    listItemDetail: css`
      flex: 1;
    `,
    listItemButton: css`
      padding: 8px 12px;
      border-radius: 8px;
      background: #f2f5fa;
      cursor: pointer;
      width: 32px;
      text-align: center;
    `,
  };
});
