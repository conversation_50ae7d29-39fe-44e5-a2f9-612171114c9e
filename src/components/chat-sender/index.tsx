import { Sender } from '@ant-design/x';
import { Divider, Flex, Spin, Upload, Tooltip } from 'antd';
import cx from 'classnames';
import { useState } from 'react';
import { AgentIdEnum } from '@/constant/enum';
import { useOSSUpload } from '@/hooks/useOSSUpload';
import { ChatFileUpload } from '../file-upload';
import ExtensionSelector from './extensionSelector';
import KnowledgeBaseSelector from './knowledgeBaseSelector';
import { ResourceItem } from '@/server/WuyingAI/SendMessage';
import { ListKnowledgeBaseData } from '@/server/WuyingAI/ListKnowledgeBases';
import { useSenderStyles } from './index.styles';
import { Iconfont } from '../icon';
import { ReportLoading } from '@/components/report-loading';
import { FileCard } from '@/components/file-card';

const defaultPlaceholder = '问我任何问题';

const ChatSender = ({
  onSubmit,
  placeholder,
  loading = false,
  research = false,
  agentId,
  sessionInfo,
  setSessionInfo,
  file = {},
  extraHeader,
  stopChat,
}: {
  onSubmit: (value: string, resources: ResourceItem[]) => void;
  placeholder?: string;
  loading?: boolean;
  research?: boolean;
  file?: { ArtifactId?: string; FileType?: string; FileName?: string };
  agentId: AgentIdEnum;
  sessionInfo: {
    SessionId?: string;
  };
  extraHeader?: React.ReactNode;
  setSessionInfo: (sessionInfo: { SessionId: string }) => void;
  stopChat?: () => void;
}) => {
  const { styles } = useSenderStyles();
  const [value, setValue] = useState<string>('');
  const { uploadList, startUpload, removeFile, clearUploadList, uploading } = useOSSUpload({
    agentId,
    sessionInfo,
    setSessionInfo,
  });
  const [selectedKb, setSelectedKb] = useState<ListKnowledgeBaseData[]>([]);

  const handleStop = () => {
    // 主agent不支持中止
    if (agentId === AgentIdEnum.alpha) return;
    stopChat?.();
  };


  return (
    <>
      {/* {
         loading && !research ? (
           <div className={styles.loadingTip}>
             <Spin style={{ marginRight: 8 }} size="small" />
             <span>正在生成中...</span>
           </div>
         ) : null
      } */}
      {extraHeader}
      <Sender
        header={
          uploadList.length > 0 ? (
            <div className={styles.fileUploadList}>
              {uploadList.map((file) => (
                <ChatFileUpload key={file.fileId} file={file} onRemove={removeFile} />
              ))}
            </div>
          ) : null
        }
        className={styles.wrapper}
        value={value}
        onChange={setValue}
        autoSize={{ minRows: 2, maxRows: 6 }}
        placeholder={placeholder ?? defaultPlaceholder}
        allowSpeech={false}
        footer={({ components }) => {
          const { SendButton, LoadingButton } = components;
          return (
            <Flex justify="space-between" align="center">
              <Flex gap="small" align="center">
                {/* deep暂不支持知识库 */}
                {agentId === AgentIdEnum.deep ? <div /> :
                <KnowledgeBaseSelector
                  loading={loading}
                  selectedItems={selectedKb}
                  onSelectChange={setSelectedKb}
                />}
                {/* 本期暂不支持MCP扩展 */}
                {/* <ExtensionSelector selectedItems={[]} onSelectChange={() => {}} /> */}
              </Flex>
              <Flex align="center">
                <Upload
                  className={styles.fileUploader}
                  disabled={loading}
                  showUploadList={false}
                  beforeUpload={startUpload}
                  // todo， 后面再补上传校验
                  // accept=".pdf,.word,.ppt,.pptx,.xls,.xlsx,.xlsm,.jpg,.jpeg,.png,.bmp,.gif,.markdown,.html,.epub,.mobi,.rtf,.txt"
                >
                  <Tooltip title="支持上传pdf、word、ppt、pptx、xls、xlsx、xlsm、jpg、jpeg、png、bmp、gif、markdown、html、epub、mobi、rtf、txt格式文件">
                    <Iconfont type="attachment" style={{ width: 20, height: 20, cursor: 'pointer' }} fill={loading ? '#b8bbc2' : ''} />
                  </Tooltip>
                </Upload>
                <Divider type="vertical" />
                {loading ? (
                  <LoadingButton
                    className={cx(styles.loadingButton, styles.sendButton)}
                    disabled={agentId === AgentIdEnum.alpha}
                    type="default"
                    onMouseDown={handleStop}
                    onTouchStart={handleStop}
                  />
                ) : (
                  <SendButton className={styles.sendButton} type="primary" disabled={uploading || !value} />
                )}
              </Flex>
            </Flex>
          );
        }}
        onSubmit={() => {
          if (loading || uploading) return;
          setValue('');
          clearUploadList();
          // TODO: 合并选择知识库文件上传
          onSubmit(value, [
            ...uploadList.map((file) => ({ ResourceId: file.fileId, Type: 'file' })),
            ...selectedKb.map((kb) => ({ ResourceId: kb.KbId, Type: 'knowledge_base' })),
          ]);
        }}
        onCancel={() => {}}
        actions={false}
      />
    </>
  );
};

export default ChatSender;
