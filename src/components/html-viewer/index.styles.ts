import { createStyles } from 'antd-style';

export const useHtmlViewerStyles = createStyles(({ css }) => ({
  iframe: css`
    width: 100%;
    height: 100%;
    border: none;
    background: #fff;
    display: block;
  `,

  loading: css`
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    z-index: 10;
  `,

  spinner: css`
    width: 24px;
    height: 24px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `,
}));
