import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useHtmlViewerStyles } from './index.styles';
import { useTabContext } from '@/contexts/TabContext';

export type HtmlViewerSource =
  | { type: 'url'; value: string }
  | { type: 'html'; value: string };

export interface HtmlViewerProps {
  source?: HtmlViewerSource;
  title?: string;
  sandbox?: string;
  className?: string;
  style?: React.CSSProperties;
}

export const HtmlViewer = forwardRef<HTMLIFrameElement, HtmlViewerProps>(
  (
    {
      source,
      title = 'HTML预览',
      sandbox = 'allow-same-origin allow-scripts allow-forms allow-popups allow-modals',
      className,
      style,
    },
    ref,
  ) => {
    const { styles, cx } = useHtmlViewerStyles();
    const iframeRef = useRef<HTMLIFrameElement | null>(null);
    const [loading, setLoading] = useState<boolean>(true);

    useImperativeHandle(ref, () => iframeRef.current as HTMLIFrameElement);


    const handleLoad = () => {
      setLoading(false);
      // iframe加载完成后自动获取焦点
      try {
        iframeRef.current?.contentWindow?.focus();
        iframeRef.current?.focus();
      } catch (e) {
        // 忽略跨域等错误
        console.warn('Failed to focus iframe:', e);
      }
    };


    useEffect(() => {
      const iframe = iframeRef.current;
      if (!iframe || !source || !source.value) return;

      // 每次有新内容时重置loading状态
      setLoading(true);

      try {
        if (source.type === 'url') {
          iframe.src = source.value;
          iframe.onload = handleLoad;
        } else {
          const doc = iframe.contentDocument || iframe.contentWindow?.document;
          if (!doc) return;
          doc.open();
          doc.write(source.value);
          doc.close();
          // 对于内联HTML，设置一个较小的延迟后隐藏loading并获取焦点
          setTimeout(() => {
            handleLoad();
          }, 100);
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('HTML内容解析失败:', err);
        setLoading(false);
      }
    }, [source]);

    if (!source || !source.value) return null;

    return (
      <div style={{ width: '100%', height: '100%', position: 'relative' }}>
        {loading && (
          <div className={styles.loading}>
            <div className={styles.spinner} />
            <span>加载中...</span>
          </div>
        )}
        <iframe
          ref={iframeRef}
          className={cx(styles.iframe, className)}
          // style={style}
          style={{ ...style, visibility: loading ? 'hidden' : 'visible' }}
          title={title}
          sandbox={sandbox}
        />
      </div>
    );
  },
);

HtmlViewer.displayName = 'HtmlViewer';
