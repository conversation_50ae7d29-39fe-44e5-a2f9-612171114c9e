import cx from 'classnames';
import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'umi';
import { useMenuStyles } from './index.styles';
// import { Icon } from '@/components/icon';
import { Iconfont } from '@/components/icon';
import { activeMenuIconMap, menuIconMap, type MenuIconType } from '@/constant/icon';

const agentMenuItems: MenuIconType[] = ['chat', 'design', 'ppt', 'deep'];
const generalMenuItems: MenuIconType[] = ['knowledge-base', 'history', 'setting'];

const MenuItem = ({
  isHovered,
  routeKey,
  menuKey,
  onMenuItemClick,
  setIsHovered,
}: {
  isHovered: MenuIconType | false;
  routeKey?: MenuIconType;
  menuKey: MenuIconType;
  onMenuItemClick: (key: MenuIconType) => void;
  setIsHovered: (key: MenuIconType | false) => void;
}) => {
  const { styles } = useMenuStyles();

  return (<div
    className={cx(styles.menuItem, {
      [styles.activeMenuItem]: routeKey === menuKey,
    })}
    onClick={() => onMenuItemClick(menuKey)}
    onMouseEnter={() => setIsHovered(menuKey)}
    onMouseLeave={() => setIsHovered(false)}
  >
    <Iconfont type={isHovered === menuKey || routeKey === menuKey ? activeMenuIconMap[menuKey] : menuIconMap[menuKey]} className={styles.icon} fill={isHovered === menuKey || routeKey === menuKey ? '#0075FF' : ''} />
  </div>);
};

const Menu = () => {
  const { styles } = useMenuStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const [routeKey, setRouteKey] = useState<MenuIconType>();

  const [isHovered, setIsHovered] = useState<MenuIconType | false>(false);

  useEffect(() => {
    setRouteKey(location.pathname.slice(1) as any);
  }, [location.pathname]);

  const onMenuItemClick = (key: MenuIconType) => {
    setRouteKey(key);
    navigate(key, { replace: true });
  };

  return (
    <div className={styles.wrapper} >
      <div>
        {agentMenuItems.map((it) => (
          <MenuItem
            key={it}
            isHovered={isHovered}
            routeKey={routeKey}
            menuKey={it}
            onMenuItemClick={onMenuItemClick}
            setIsHovered={(key) => setIsHovered(key)}
          />
        ))}
      </div>
      <div>
        {generalMenuItems.map((it) => (
          <MenuItem
            key={it}
            isHovered={isHovered}
            routeKey={routeKey}
            menuKey={it}
            onMenuItemClick={onMenuItemClick}
            setIsHovered={(key) => setIsHovered(key)}
          />
        ))}
      </div>
    </div>
  );
};

export default Menu;
