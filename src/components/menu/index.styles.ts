import { createStyles } from 'antd-style';

export const useMenuStyles = createStyles(({ css, token }) => {
  return {
    wrapper: css`
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
      z-index: 99;
      background: ${token.colorBgContainer};
    `,
    menuItem: css`
      cursor: pointer;
      padding: 0 10px;
      margin: 20px 0;

      &:hover {
              border-right: 3px solid #3391ff;
         padding-right: 7px;
      }
    `,
    activeMenuItem: css`
      border-right: 3px solid #3391ff;
      padding-right: 7px;
    `,
    icon: css`
      width: 40px;
      height: 40px;
    `,
  };
});
