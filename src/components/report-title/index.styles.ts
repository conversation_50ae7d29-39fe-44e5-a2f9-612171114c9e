// src/components/mark-down/index.styles.ts
import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token }) => ({
  container: {
    margin: token.margin,
    border: `${token.lineWidth}px solid ${token.colorSplit},`,
    paddingTop: 0,
    padding: '0 16px',
    borderRadius: token.borderRadiusLG,
    position: 'relative',
  },
  titleContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  iconStyle: {
    marginRight: token.marginXS,
    height: token.sizeLG,
    width: token.sizeLG,
  },
  titleFontStyle: {
    fontSize: 18,
  },
  paragraphFontStyle: {
    color: token.colorTextDescription,
    fontSize: token.fontSizeSM,
  },
}));
