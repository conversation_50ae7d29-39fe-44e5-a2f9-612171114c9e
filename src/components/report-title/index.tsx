import React from 'react';
import { Typography, Flex, Tag } from 'antd';
import { Iconfont } from '@/components/icon';
import { useStyles } from './index.styles';

const ReportTitle = ({ title, desc, loading = false, run_error = false, run_success = false }: { title?: string; desc?: string; loading?: boolean; run_error?: boolean; run_success?: boolean }) => {
  const { styles } = useStyles();

  return (
    <div className={styles.container}>
      <Typography.Title>
        <Flex align="center" className={styles.titleContainer}>
          <Iconfont type="wikis--outline" className={styles.iconStyle} />
          <div className={styles.titleFontStyle}>{title ?? '报告生成中...'}</div>
          <div style={{ flex: 1 }} />
          {loading && <Tag color="blue">进行中</Tag>}
          {run_error && <Tag color="red">失败</Tag>}
          {run_success && <Tag color="green">成功</Tag>}
          {!loading && !run_error && !run_success && <Tag color="gray">停止</Tag>}
        </Flex>
      </Typography.Title>
      <Typography.Paragraph className={styles.paragraphFontStyle}>{desc}</Typography.Paragraph>
    </div>
  );
};

export default ReportTitle;
