import { createStyles, keyframes } from 'antd-style';

export const useReportLoadingStyles = createStyles(({ css }) => {
  const gradientAnimation = keyframes`
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  `;

  const spin = keyframes`
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  `;

  return {
    container: css`
      display: flex;
      justify-content: center;
      align-items: center;
      /* width: 380px; */
    `,

    card: css`
      width: 350px;
      border-radius: 16px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      background: #fff;
    `,

    content: css`
      display: flex;
      align-items: center;
      padding: 12px;
      gap: 20px;
    `,

    leftSection: css`
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0px;
      margin-top: -14px;
    `,

    title: css`
      font-size: 14px;
      font-weight: 500;
      color: #1a1a1a;
      font-family: 'AlibabaSans102-Medium', 'PingFang SC', sans-serif;
      line-height: 1.5;
      margin-bottom: 4px;
    `,

    progress: css`
      width: 100%;

      .ant-progress-outer {
        .ant-progress-inner {
          background: #f5f5f5;
          border-radius: 100px;

          .ant-progress-bg {
            background: linear-gradient(90deg, #1677ff 0%, #52c41a 50%, #faad14 100%);
            background-size: 200% 100%;
            animation: ${gradientAnimation} 2.5s ease-in-out infinite;
            border-radius: 100px;
          }
        }
      }
    `,

    loading: css`
      width: 20px;
      height: 20px;
      border: 2px solid #f0f0f0;
      border-top: 2px solid #1677ff;
      border-radius: 50%;
      animation: ${spin} 1s linear infinite;
    `,
  };
});
