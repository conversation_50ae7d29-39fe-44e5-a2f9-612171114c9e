import React from 'react';
import { Card, Progress, Typography } from 'antd';
import { useReportLoadingStyles } from './index.styles';

const { Text } = Typography;

export const ReportLoading: React.FC = () => {
  const { styles } = useReportLoadingStyles();

  return (
    <div className={styles.container}>
      <Card className={styles.card} bordered={false}>
        <div className={styles.content}>
          <div className={styles.leftSection}>
            <Text className={styles.title}>报告生成中..</Text>
            <Progress
              percent={48}
              showInfo={false}
              strokeWidth={4}
              strokeLinecap="round"
              size="small"
              className={styles.progress}
            />
          </div>
          <div className={styles.loading} />
        </div>
      </Card>
    </div>
  );
};
