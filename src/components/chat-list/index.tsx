import { Bubble } from '@ant-design/x';
import { notification, Tooltip, Checkbox, Button, CheckboxChangeEvent } from 'antd';
import { cloneDeep, debounce } from 'lodash-es';
import { useState, useMemo } from 'react';
import type { BubbleDataType } from '@/hooks/useChat';
import { Iconfont } from '@/components/icon';
import MarkDownPreview from '@/components/markdown';
import { useChatListStyles } from './index.styles';
import { UpdateMessageFeedback } from '@/server/WuyingAI/UpdateMessageFeedback';
import { AgentIdEnum } from '@/constant/enum';
import useAddToKBModal from '@/hooks/useAddToKBModal';
import PptCard from './ppt-card';
import { ReportLoading } from '@/components/report-loading';
import { FileCard } from '@/components/file-card';
import { safeParseJSON } from '@/utils';

export const CopyButton = ({ text }: { text: string }) => {
  const { styles } = useChatListStyles();
  const [active, setActive] = useState(false);
  return (<Tooltip title="复制" trigger="hover" placement="bottom" ><Iconfont
    type={active ? 'copy--filled' : 'copy--outline'}
    className={styles.assistantFooterBtn}
    onClick={debounce(async () => {
      setActive(true);
      setTimeout(() => {
        setActive(false);
      }, 1000);
      await navigator.clipboard.writeText(text);
      notification.info({
        message: '复制成功',
      });
    }, 300)}
    fill={active ? '#0075FF' : ''}
  /></Tooltip>);
};

const ChatList = ({
  agentId,
  messages,
  loading = true,
  updateMessages,
  sessionId,
  research = false,
}: {
  messages: BubbleDataType[];
  updateMessages?: React.Dispatch<React.SetStateAction<BubbleDataType[]>>;
  loading?: boolean;
  sessionId?: string;
  agentId?: AgentIdEnum;
  research?: boolean;
}) => {
  const { styles } = useChatListStyles();
  // 选择添加知识库开关
  const [isAddToKB, setIsAddToKB] = useState(false);
  // 已选择的会话id
  const [selectedMessageIds, setSelectedMessageIds] = useState<string[]>([]);
  // 已选择的文件id，ai ppt里会用到，后又不要了
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);

  // ai ppt在二级页面里有会话制品（ppt卡片）
  // 过滤出有制品信息的消息，后面讨论的时候，又不需要了
  // const artifactMessages = useMemo(() => {
  //   if (agentId !== AgentIdEnum.ppt) {
  //     // 不是ai ppt, 不需要处理
  //     return [];
  //   }
  //   return messages.reduce((acc: Array<BubbleDataType & { extInfoObj: Record<string, any> }>, val) => {
  //     if (typeof val.extInfo === 'string' && /artifact_id/.test(val.extInfo)) {
  //       const extInfo = safeParseJSON(val.extInfo);
  //       acc.push({ ...val, extInfoObj: extInfo });
  //     }
  //     return acc;
  //   }, []);
  // }, [messages, agentId]);

  const selectedAllCheckboxProps = useMemo(() => {
    const isMessagesIndeterminate = (selectedMessageIds.length > 0 && selectedMessageIds.length < messages.filter((it) => it.messageId).length);
    const isSelectedAllMessages = messages.length === selectedMessageIds.length;
    return {
      indeterminate: isMessagesIndeterminate,
      checked: isSelectedAllMessages,
    };
  }, [selectedMessageIds, messages]);

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    setSelectedMessageIds(e.target.checked ? messages.reduce((memo: string[], it) => {
      if (it.messageId) {
        memo.push(it.messageId);
      }
      return memo;
    }, []) : []);
    // if (agentId === AgentIdEnum.ppt) {
    //   setSelectedFileIds(e.target.checked ? artifactMessages.reduce((memo: string[], it) => {
    //     memo.push(it.extInfoObj.artifact_id);
    //     return memo;
    //   }, []) : []);
    // }
  };

  const { showAddKbModal } = useAddToKBModal();

  const handleCancelAddToKB = () => {
    setIsAddToKB(false);
    setSelectedMessageIds([]);
  };


  const handleFeedBack = (message: BubbleDataType, feedback: 'like' | 'dislike' | null) => {
    if (message.messageId && sessionId) {
      updateMessages?.((prev) => {
        const newMessages = cloneDeep(prev);
        const currentMessage = newMessages.find((it) => it.messageId === message.messageId);
        if (currentMessage) {
          currentMessage.feedback = feedback;
        }
        return newMessages;
      });
      UpdateMessageFeedback({
        SessionId: sessionId,
        MessageId: message.messageId,
        Feedback: feedback,
      }).then((res) => {
        console.log(res);
      });
    }
  };

  const renderAssistantMessage = (it: BubbleDataType, message: string) => {
    if (it.file?.ArtifactId) {
      return (
        <div className={styles.fileCardWrapper}>
          <div>任务已完成。以下为生成的文件结果</div>
          <FileCard
            id={it.file.ArtifactId}
            fileName={it.file.FileName}
          />
        </div>
      );
    }
    if (it.requires_research && agentId === AgentIdEnum.deep && loading) {
      return <ReportLoading />;
    }
    if (message) {
      return <MarkDownPreview context={message} />;
    }
    return null;
  };

  const renderUserMessage = (message: string) => {
    return <div>{message}</div>;
  };
  const renderMessages = useMemo(() => {
    if (loading) {
      return [...messages, {
        role: 'assistant',
        loading: true,
        messageId: '',
        content: '',
        resources: [],
        feedback: null,
      }];
    }
    return messages;
  }, [loading, messages]);

  return (
    <>
      <div className={styles.chatList}>
        {
        /* 🌟 消息列表 */
          <Bubble.List
            autoScroll
            className={styles.bubbleList}
            items={renderMessages.map((it) => ({
              ...it,
              avatar: isAddToKB && it.messageId ? <Checkbox
                className={styles.addToKBCheckbox}
                checked={selectedMessageIds.includes(it.messageId)}
                onChange={() => setSelectedMessageIds((prev) => {
                  if (selectedMessageIds.includes(it.messageId as string)) {
                    return prev.filter((id) => id !== it.messageId);
                  }
                  return [...prev, it.messageId] as string[];
                })}
              /> : <div />,
              variant: it.role === 'assistant' ? 'borderless' : 'filled',
              messageRender: (message: any) => {
                const quoteFileNums = it?.resources?.filter?.((r) => r.Type === 'file')?.length ?? 0;
                return (
                  <div className={styles.messageItem}>
                    {it.role === 'assistant' ? renderAssistantMessage(it, message) : renderUserMessage(message)}
                    {quoteFileNums > 0 ? (
                      <div className={styles.resourcesTip}>
                        <Iconfont type="undo" className={styles.resourcesTipIcon} />
                        引用了{quoteFileNums}个文件
                      </div>
                    ) : null}
                  </div>
                );
              },
              loadingRender: () => !research && <div className={styles.loadingWrapper}><img className={styles.loadingIcon} src="https://gw.alicdn.com/imgextra/i1/O1CN01NiUuXj22u2R0WSWMB_!!6000000007179-55-tps-23-23.svg" alt="" /><span>正在生成中...</span></div>,
              footer: () =>
                (it.role === 'assistant' && !loading ? (
                  <div className={styles.assistantFooterWrapper}>
                    <div className={styles.assistantFooterOperations}>
                      {it?.messageId && <Tooltip title="点赞" trigger="hover" placement="bottom">
                        <Iconfont
                          type={it.feedback === 'like' ? 'thumbs-up--filled' : 'thumbs-up--outline'}
                          className={styles.assistantFooterBtn}
                          onClick={() => handleFeedBack(it, it.feedback === 'like' ? null : 'like')}
                          fill={it.feedback === 'like' ? '#0075FF' : ''}
                        />
                      </Tooltip>}

                      {it?.messageId && <Tooltip title="点踩" trigger="hover" placement="bottom">
                        <Iconfont
                          type={
                        it.feedback === 'dislike' ? 'thumbs-down--filled' : 'thumbs-down--outline'
                      }
                          className={styles.assistantFooterBtn}
                          onClick={() =>
                            handleFeedBack(it, it.feedback === 'dislike' ? null : 'dislike')
                      }
                          fill={it.feedback === 'dislike' ? '#0075FF' : ''}
                        />
                      </Tooltip>
                    }
                      {it.content && <CopyButton text={it.content} />}
                      {it.messageId && <Tooltip title="添加到知识库" trigger="hover" placement="bottom">
                        <Iconfont
                          type="folder--add--outline"
                          className={styles.assistantFooterBtn}
                          onClick={() => {
                            if (isAddToKB) {
                              handleCancelAddToKB();
                            } else {
                              setIsAddToKB((prev) => !prev);
                            }
                          }}
                        />
                      </Tooltip>}
                    </div>
                    {it.content && <span className={styles.assistantFooterTip}>以上内容由AI生成</span>}
                    {
                    agentId === AgentIdEnum.ppt && sessionId && (
                      <PptCard
                        message={it}
                        sessionId={sessionId}
                        messages={messages}
                        updateMessages={updateMessages}
                        isAddToKB={isAddToKB}
                        setSelectedFileIds={setSelectedFileIds}
                        selectedFileIds={selectedFileIds}
                      />
                    )
                  }
                  </div>
                ) : null),
            }))}
          />
      }
      </div>
      {isAddToKB ? <div className={styles.addToKBWrapper}>
        <div>
          <Checkbox
            className={styles.addToKBCheckbox}
            indeterminate={selectedAllCheckboxProps.indeterminate}
            checked={selectedAllCheckboxProps.checked}
            onChange={handleSelectAll}
          />
          <span style={{ marginLeft: 8 }}>
            全选
          </span>
        </div>

        <div>
          <Button onClick={handleCancelAddToKB} style={{ marginRight: 8 }}>取消</Button>
          <Button
            type="primary"
            disabled={selectedMessageIds.length === 0}
            onClick={() => {
              showAddKbModal({
                sessionId,
                messageIds: selectedMessageIds,
                onSuccess: handleCancelAddToKB,
                fileIdList: selectedFileIds,
                messageIgnore: messages.length === 0,
                fileIgnore: selectedFileIds.length === 0,
              });
            }}
          >添加到知识库</Button>
        </div>
      </div> : null}
    </>
  );
};

export default ChatList;
