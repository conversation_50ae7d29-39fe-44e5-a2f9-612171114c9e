import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  createBtn: css`
    margin: 32px 0;
    width: 137px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 圆角/12px */
    /* 样式描述：12px */
    border-radius: 12px;
    background: #FFFFFF;
    box-sizing: border-box;
    position: relative;
    border: 1px solid #d9d9d9;
    border-color: transparent;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    background-image:
      linear-gradient(to right, #fff, #fff),
      linear-gradient(
        to bottom,
        #79aeff 0%,
        #76d4ff 11.1%,
        rgba(119, 132, 255, 0.6498) 25.3%,
        rgba(105, 120, 255, 0.7) 40%,
        #ffa5d6 55.8%,
        #a579ff 78.6%,
        #79aeff 100%
      );
    box-shadow: 0px 4px 4px 0px rgba(0, 117, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
  
    &:hover {
      box-shadow: 
        0px 4px 4px 0px rgba(0, 117, 255, 0.2),
        0px 8px 16px 0px rgba(121, 174, 255, 0.15),
        0px 0px 0px 4px rgba(121, 174, 255, 0.1);
    }
  `,
  pptIcon: css`
    width: 20px;
    height: 20px;
    margin-right: 4px;
    fill: #0075FF;
  `,
  createText: css`
    font-size: 15px;
    font-weight: normal;
    line-height: 24px;
    text-align: center;
    letter-spacing: normal;
    background: linear-gradient(95deg, #0075FF 14%, #4E60FF 50%, #A579FF 94%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  `,
  thumbnail: css`
    width: 360px;
    border-radius: 12px;
  `,
  optsWrapper: css`
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
  `,
  arrow: css`
    width: 20px;
    height: 20px;
    fill: #B8BBC2;
  `,
  thumbnailWrapper: css`
    display: flex;
    gap: 12px;
    align-items: flex-start;  
    margin: 20px 0 8px;
  `,
}));
