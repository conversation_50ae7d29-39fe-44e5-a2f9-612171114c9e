import { Button, Dropdown, message, Modal, Checkbox } from 'antd';
import { useRequest } from 'ahooks';
import { useMemo, useRef, useState } from 'react';
import { safeParseJSON } from '@/utils/common';
import { Iconfont } from '@/components/icon';
import useAiPPT from '@/hooks/useAiPPT';
import useAddToKBModal from '@/hooks/useAddToKBModal';
import type { BubbleDataType } from '@/hooks/useChat';
import { BindPPTToSession } from '@/server/WuyingAI/BindPPTToSession';
import { GetPPTThumbnail } from '@/server/WuyingAI/GetPPTThumbnail';
import { ListUserArtifactDownloadUrls } from '@/server/WuyingAI/ListUserArtifactDownloadUrls';
import { IsArtifactExisted } from '@/server/WuyingAI/IsArtifactExisted';
import { EventType, useGlobalEventEmitter } from '@/hooks/useGlobalEventEmitter';
import { AiPPTGeneratePPTSuccessData } from '@/types/ppt';
import { downloadFiles } from '@/utils/download';
import PptDownload from '@/components/ppt-download';
import InvalidPpt from './invalid-ppt';
import { useStyles } from './index.styles';

const extractMarkdownFromCodeBlock = (text: string) => {
  if (typeof text !== 'string' || text.trim() === '') {
    return '';
  }
  // 匹配 ```markdown 开头的代码块（支持可选空格和大小写）
  // const markdownRegex = /```markdown\s*([\s\S]*?)(?:\n```|$)/;
  // const match = text.match(markdownRegex);

  return text.replaceAll(/```markdown|```/g, '');
};

const Thumbnail = (props: {
  pptId: string;
  artifactId: string;
  sessionId: string;
  isAddToKB: boolean;
  setSelectedFileIds: (fileIdList: string[]) => void;
  selectedFileIds: string[];
}) => {
  const { pptId, artifactId, sessionId, isAddToKB, setSelectedFileIds, selectedFileIds } =
    props;
  const { styles } = useStyles();
  const { openEditTab } = useAiPPT();
  const { showAddKbModal } = useAddToKBModal();
  // useRequest(async (params) => {
  //   const ret = await IsArtifactExisted(params);
  //   return ret.Data;
  // }, {
  //   ready: !!artifactId,
  //   defaultParams: [
  //     {
  //       ArtifactId: artifactId,
  //     },
  //   ],
  // });
  const { data: thumbnail } = useRequest(
    async (params) => {
      const ret = await GetPPTThumbnail(params);
      return ret.Data;
    },
    {
      ready: !!pptId,
      defaultParams: [
        {
          PptId: pptId,
        },
      ],
    },
  );
  const { run: downloadPPT } = useRequest(
    async (params) => {
      message.success('正在下载文件，请稍等...');
      const res = await ListUserArtifactDownloadUrls({
        ArtifactIds: params.ArtifactIds,
        FileFormat: params.FileFormat,
        SessionId: sessionId,
      });
      return res?.Data || [];
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.FailedFiles.length > 0) {
          Modal.error({
            title: '以下制品下载失败',
            content: (
              <div>
                {result.FailedFiles.map((item) => (
                  <div key={item.ArtifactId}>{item.FileName}</div>
                ))}
              </div>
            ),
          });
        }
        downloadFiles(result.DownloadLinks);
      },
      onError: (error) => {
        message.error('下载失败');
      },
    },
  );
  const handleMenuClick = async ({ FileFormat }: { FileFormat: string }) => {
    downloadPPT({ ArtifactIds: [artifactId], SessionId: sessionId, FileFormat });
  };

  const handleAddToKnowledge = () => {
    showAddKbModal({ sessionId, messageIgnore: true, fileIdList: [artifactId] });
  };

  if (!thumbnail) {
    return null;
  }

  return (
    <div className={styles.thumbnailWrapper}>
      {/* { */}
      {/*  isAddToKB && ( */}
      {/*    <Checkbox */}
      {/*      checked={selectedFileIds.includes(artifactId)} */}
      {/*      onChange={(e) => { */}
      {/*        if (e.target.checked) { */}
      {/*          setSelectedFileIds([...selectedFileIds, artifactId]); */}
      {/*        } else { */}
      {/*          setSelectedFileIds(selectedFileIds.filter((item) => item !== artifactId)); */}
      {/*        } */}
      {/*      }} */}
      {/*    /> */}
      {/*  ) */}
      {/* } */}
      <div>
        <img className={styles.thumbnail} src={thumbnail} alt="thumbnail" />
        <div className={styles.optsWrapper}>
          <PptDownload handleMenuClick={handleMenuClick}>
            <Button>
              下载
              <Iconfont type="chevron--down" className={styles.arrow} />
            </Button>
          </PptDownload>
          <Button onClick={() => openEditTab({ pptId })}>去编辑</Button>
          <Button onClick={handleAddToKnowledge}>添加到知识库</Button>
        </div>
      </div>
    </div>
  );
};

const CreatePPTCard = (props: {
  message: BubbleDataType;
  sessionId: string;
  messages: BubbleDataType[];
  updateMessages?: (messages: BubbleDataType[]) => void;
}) => {
  const { message, sessionId, messages, updateMessages } = props;
  const { styles } = useStyles();
  const { openCreateTab } = useAiPPT();
  const md = extractMarkdownFromCodeBlock(message.content) || message.content;
  const event$ = useGlobalEventEmitter();
  const pptInfoRef = useRef<AiPPTGeneratePPTSuccessData | { id: string } | null>(null);
  const { runAsync: bindPPTToSession } = useRequest(BindPPTToSession, {
    manual: true,
    onSuccess: (data) => {
      // 需要手动改一下message里的数据结构，追加ppt_id，生成阶段的数据和历史数据里的结构保持一致
      const newMessages = messages.map((item) => {
        if (item.eventId === message.eventId) {
          return {
            ...item,
            extInfo: JSON.stringify({
              ...safeParseJSON(item.extInfo),
              ppt_id: pptInfoRef.current?.id?.toString() || '',
              artifact_id: data.Data,
            }),
          };
        }
        return item;
      });
      updateMessages?.(newMessages);
    },
  });

  event$.useSubscription((data) => {
    if (data?.type === EventType.PPT_CREATE_SUCCESS && data?.payload?.eventId === message.eventId) {
      const pptData = data.payload as AiPPTGeneratePPTSuccessData;
      pptInfoRef.current = {
        ...pptData,
        id: pptData?.id?.toString() || '',
      };
      bindPPTToSession({
        SessionId: sessionId,
        EventId: message.eventId!,
        PptId: pptData?.id?.toString() || '',
      });
    }
  });

  return (
    <div
      className={styles.createBtn}
      onClick={() => openCreateTab({ content: md, sessionId, eventId: message.eventId })}
    >
      <Iconfont type="file--ppt" className={styles.pptIcon} />
      <span className={styles.createText}>一键生成PPT</span>
    </div>
  );
};

const PptCard = (props: { message: BubbleDataType; sessionId: string; messages: BubbleDataType[]; updateMessages?: (messages: BubbleDataType[]) => void; isAddToKB: boolean; setSelectedFileIds: (fileIdList: string[]) => void; selectedFileIds: string[] }) => {
  const { message, sessionId, messages, updateMessages, isAddToKB, setSelectedFileIds, selectedFileIds } = props;
  const extInfo = useMemo(() => {
    return safeParseJSON(message?.extInfo);
  }, [message.extInfo]);
  const pptId = extInfo?.ppt_id;
  const artifactId = extInfo?.artifact_id;

  return (
    <>
      {
        pptId
          ? <Thumbnail
              pptId={pptId}
              artifactId={artifactId}
              sessionId={sessionId}
              isAddToKB={isAddToKB}
              setSelectedFileIds={setSelectedFileIds}
              selectedFileIds={selectedFileIds}
          />
          : <CreatePPTCard message={message} sessionId={sessionId} messages={messages} updateMessages={updateMessages} />
      }
    </>
  );
};

export default PptCard;
