import { createStyles } from 'antd-style';

export const useChatListStyles = createStyles(({ css }) => {
  return {
    chatList: css`
      flex: 1;
      overflow: auto;

        .ant-bubble[role='user'] {
          justify-content: space-between;

          .ant-bubble-content-wrapper {
            flex: initial;
          }
        }
    `,
    bubbleList: css`
      height: 100%;
      overflow: auto;
    `,
    addToKBCheckbox: css`
    margin-top: 16px;
    `,
    assistantFooterWrapper: css`
      display: flex;
      flex-direction: column;
    `,
    assistantFooterOperations: css`
      display: flex;
    `,
    assistantFooterBtn: css`
      width: 20px;
      height: 20px;
      margin-right: 12px;
      cursor: pointer;
    `,
    assistantFooterTip: css`
      margin-top: 8px;
      font-size: 13px;
      line-height: 20px;
      color: #b8bbc2;
    `,
    messageItem: css`
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    `,
    resourcesTip: css`
      align-self: flex-end;
      color: #8c909c;
      text-align: right;
      display: flex;
      align-items: center;
    `,
    resourcesTipIcon: css`
      transform: rotate(180deg);
      fill: #8c909c;
      width: 20px;
      height: 20px;
    `,
    addToKBWrapper: css`
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;
    `,
    loadingWrapper: css`
      display: flex;
      align-items: center;
    color: #474A52;
    `,
    loadingIcon: css`
      margin-right: 8px;
      animation: spin 2s ease-in-out infinite;

      @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
      }
    `,
    fileCardWrapper: css`
      padding: 16px 0; `,
  };
});
