import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkEmoji from 'remark-emoji';
import remarkMath from 'remark-math';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';

const MarkDownPreview = ({ context }: { context: string }) => {
  const renderers: any = {
    a: ({ href, children }: { href: string; children: any }) => (
      <a href={href} target="_blank" rel="noopener noreferrer">
        {children}
      </a>
    ),
    code: ({
      children,
      className,
      node,
      ...rest
    }: {
      children: any;
      className: string;
      node: any;
    }) => {
      const match = /language-(\w+)/.exec(className || '');
      return match ? (
        <SyntaxHighlighter
          key={node.key}
          className="custom-scrollbar"
          {...rest}
          PreTag="div"
          language={match[1]}
          style={atomDark}
          codeTagProps={{
            style: {
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap',
            },
          }}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code {...rest} className={className}>
          {children}
        </code>
      );
    },
    // 自定义有序列表渲染
    ol: ({ node, children, ...props }: { node: any; children: any; [key: string]: any }) => {
      return (
        <ol
          {...props}
          start={node.start || 1}
          style={{
            marginBottom: '0.5rem',
          }}
        >
          {children}
        </ol>
      );
    },
    li: ({ node, children, ...props }: { node: any; children: any; [key: string]: any }) => {
      // 获取原始编号
      const originalStart = node.originalStart || props.value;
      if (node.checked !== null && node.checked !== undefined) {
        return (
          <li className="flex items-center space-x-2">
            <input type="checkbox" checked={node.checked} readOnly />
            <span>{props.children}</span>
          </li>
        );
      }
      return (
        <li
          {...props}
          value={originalStart}
          style={{
            marginBottom: '0.5rem',
            lineHeight: '1.5',
          }}
        >
          {children}
        </li>
      );
    },
    // 自定义表格样式
    table: ({ children }: { children: any }) => <table>{children}</table>,
    // 删除线样式
    del: ({ children }: { children: any }) => <del className="text-gray-500">{children}</del>,
  };

  return (
    <Markdown
      remarkPlugins={[remarkGfm, remarkEmoji, remarkMath]}
      rehypePlugins={[rehypeRaw, rehypeKatex]}
      components={renderers}
    >
      {context}
    </Markdown>
  );
};

export default MarkDownPreview;
