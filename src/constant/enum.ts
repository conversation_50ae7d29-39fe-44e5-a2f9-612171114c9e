export enum AgentIdEnum {
  /**
   * @description 主agnet
   */
  alpha = 'alpha',
  ppt = 'ai_ppt',
  deep = 'deeper',
}

export const agentIdRouterMap = {
  [AgentIdEnum.alpha]: '/chat',
  [AgentIdEnum.ppt]: '/ppt',
  [AgentIdEnum.deep]: '/deep',
};

export enum DesktopStatusEnum {
  /**
   * @description 运行中
   */
  Running = 'Running',
  /**
   * @description 已关机
   */
  Stopped = 'Stopped',
  /**
   * @description 开机中
   */
  Starting = 'Starting',
  /**
   * @description 重建中
   */
  Rebuilding = 'Rebuilding',
  /**
   * @description 关机中
   */
  Stopping = 'Stopping',
  /**
   * @description 已过期
   */
  Expired = 'Expired',
  /**
   * @description 已删除
   */
  Deleted = 'Deleted',
  /**
   * @description 等待中
   */
  Pending = 'Pending',
}

export const DesktopStatusTextMap = {
  [DesktopStatusEnum.Running]: '运行中',
  [DesktopStatusEnum.Stopped]: '已关机',
  [DesktopStatusEnum.Starting]: '开机中',
  [DesktopStatusEnum.Rebuilding]: '重建中',
  [DesktopStatusEnum.Stopping]: '关机中',
  [DesktopStatusEnum.Expired]: '已过期',
  [DesktopStatusEnum.Deleted]: '已删除',
  [DesktopStatusEnum.Pending]: '等待中',
};
