import KnowledgeBase from '@/pages/knowledge-base/home';
import KnowledgeBaseDetail from '@/pages/knowledge-base/knowledge-base-detail';
import KnowledgeBaseAddSession from '@/pages/knowledge-base/knowledge-base-add-sessions';
import KnowledgeBaseSessionDetail from '@/pages/knowledge-base/knowledge-base-session-detail';
import KnowledgeBaseAddFiles from '@/pages/knowledge-base/knowledge-base-add-files';
import FileManager from '@/components/file-manager';
import { AgentIdEnum } from '@/constant/enum';
import PPTDetail from '@/pages/ppt/ppt-detail';
import pptState from '@/model/pptModel';

export const KNOWLEDGE_BASE_TABS = {
  knowledgeBase: {
    key: 'knowledge-base',
    label: '知识库',
    closable: false,
    children: KnowledgeBase,
  },
  knowledgeBaseDetail: {
    key: 'knowledge-base-detail',
    label: '知识库详情',
    children: KnowledgeBaseDetail,
  },
  knowledgeBaseAddSession: {
    key: 'knowledge-base-add-session',
    label: '添加会话',
    children: KnowledgeBaseAddSession,
  },
  knowledgeBaseAddFiles: {
    key: 'knowledge-base-add-files',
    label: '添加文件',
    children: KnowledgeBaseAddFiles,
  },
  knowledgeBaseSessionDetail: {
    key: 'knowledge-base-session-detail',
    label: '会话详情',
    children: KnowledgeBaseSessionDetail,
  },
};

export const CHAT_TABS = {
  fileList: {
    key: 'chat-file-list',
    label: '文件列表',
    children: FileManager,
    props: {
      agentId: AgentIdEnum.alpha,
    },
    destroyOnHidden: true,
  },
};

export const PPT_TABS = {
  pptList: {
    key: 'ppt-file-list',
    label: 'PPT列表',
    children: FileManager,
    destroyOnHidden: true,
    props: {
      agentId: AgentIdEnum.ppt,
    },
  },
  pptCreate: {
    key: 'ppt-detail',
    label: 'PPT创建',
    children: PPTDetail,
    onClose: () => {
      pptState.actions.resetPptInfo();
    },
  },
  pptEdit: {
    key: 'ppt-detail',
    label: 'PPT编辑',
    children: PPTDetail,
    onClose: () => {
      pptState.actions.resetPptInfo();
    },
  },
};

export const DEEP_SEARCH_TABS = {
  fileList: {
    key: 'deep-search-file-list',
    label: '任务文件',
    children: FileManager,
    destroyOnHidden: true,

  },
};

export const DEFAULT_ROUTE_TABS = {
  '/chat': [
    CHAT_TABS.fileList,
  ],
  '/ppt': [
    PPT_TABS.pptList,
  ],
  '/deep': [
    DEEP_SEARCH_TABS.fileList,
  ],
  '/knowledge-base': [
    KNOWLEDGE_BASE_TABS.knowledgeBase,
  ],
};
