import { AgentIdEnum } from './enum';

export const menuIconMap = {
  chat: 'a-tongyong555',
  ppt: 'PPT',
  folder: 'a-wenjianjia1',
  'knowledge-base': 'zhishiku',
  deep: 'deep555',
  history: 'lishi',
  setting: 'shezhi',
  design: 'Design',
};
export const activeMenuIconMap = {
  chat: 'tongyong-C',
  ppt: 'PPT-C',
  folder: 'wenjianjia-C',
  'knowledge-base': 'zhishiku-C',
  deep: 'deep5-6',
  history: 'lishi-C',
  setting: 'shezhi-C',
  design: 'Design-C',
};

export const menuIconTitleMap = {
  chat: 'AI Chat',
  ppt: 'PPT Agent',
  folder: '文件夹',
  'knowledge-base': '知识库',
  deep: 'Deep Research',
  history: '历史对话',
  setting: '设置',
  design: 'Design Agent',
};

export type MenuIconType = keyof typeof menuIconMap;

export const deepIconMap = {
  web_search: 'search',
  write_file: 'edit--outline',
};

export type DeepIconType = keyof typeof deepIconMap;

export const fileIconsMap: Record<string, string> = {
  pdf: 'PDF',
  doc: 'WORD',
  docx: 'WORD',
  ppt: 'PPT',
  pptx: 'PPT',
  xls: 'EXCEL',
  xlsx: 'EXCEL',
  xlsm: 'EXCEL',
  md: 'MD',
  html: 'HTML',
  htm: 'HTML',
  epub: 'EPUB',
  mobi: 'MOBI',
  txt: 'TXT',
};

export const historyIconMap = {
  [AgentIdEnum.alpha]: 'a-tongyong555',
  [AgentIdEnum.ppt]: 'PPT',
  [AgentIdEnum.deep]: 'deep555',
};

export type HistoryIconType = keyof typeof historyIconMap;
