import { useEffect, useRef, useState } from 'react';
import StreamRequest from '@/server/streamRequest';
import { SendMessage } from '@/server/WuyingAI/SendMessage';
import { AgentIdEnum } from '@/constant/enum';
import { safeParseJSON, getLoginInfo, getUrlParams } from '@/utils';
import { ResourceItem } from '@/server/WuyingAI/SendMessage';
import { ListSessionHistoryMessage, EventItem } from '@/server/WuyingAI/ListSessionHistoryMessage';
import { CancelSession } from '@/server/WuyingAI/CancelSession';
import { useRequest } from 'ahooks';
import { Modal, message, notification } from 'antd';
import commonState from '@/model/commonModel';
import { useTabContext } from '@/contexts/TabContext';

export interface StreamData {
  Content: string;
  EventId: string;
  RunId: string;
  SessionId: string;
  Timestamp: number;
  Type: string;
  MessageId: string;
  ToolCallId?: string;
  ToolName?: string;
  Role?: 'assistant' | 'user' | 'tool';
  Name?: string;
  ArtifactId?: string;
  FileType?: string;
  FileName?: string;
}

const baseURL = 'https://wuyingai-pre.cn-hangzhou.aliyuncs.com/GetStreamMessage';

const baseOption = {
  Version: '2025-07-17',
  Product: 'WuyingAI',
};

export interface BubbleDataType {
  role: string;
  content: string;
  messageId?: string;
  loading?: boolean;
  resources?: ResourceItem[];
  feedback?: 'like' | 'dislike' | null;
  eventId?: string;
  extInfo?: string;
  requires_research?: boolean;
  file?: {
    ArtifactId?: string;
    FileType?: string;
    FileName?: string;
  };
}
export interface CustomType {
  requires_research?: boolean;
  research_plan?: string;
  report_outline?: Array<{ title: string; key_question: string; iterative_research_id?: string; content?: string }>;
  report_title?: string;
  ArtifactId?: string;
  FileType?: string;
  FileName?: string;
  run_error?: boolean;
  run_success?: boolean;
}

const request = StreamRequest({
  baseURL,
});

const doneStatus = ['RUN_FINISHED', 'RUN_ERROR', 'RUN_STOPPED'];
const useChat = ({ agentId }: { agentId: AgentIdEnum }) => {
  const chatAbortController = useRef<AbortController | null>(null);
  const eventIdRef = useRef<string>('');
  const [sessionInfo, setSessionInfo] = useState<{ SessionId?: string }>({});

  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<BubbleDataType[]>([]);
  // 工具调用消息
  const [toolCallMsgs, setToolCallMsgs] = useState<Array<{
    toolName: string;
    toolCallId: string;
    toolCallContent: string;
    isFinished: boolean;
  }>>([]);

  const rapRef = useRef<number>(0);

  const [deepMessages, setDeepMessages] = useState<BubbleDataType[]>([]);
  const [customMessages, setCustomMessages] = useState<CustomType>({});

  const { closeAllTabs } = useTabContext();

  // 当前会话唤起云电脑的连接信息
  const [connectInfo, setConnectInfo] = useState<{
    connection_properties?: string;
    resource_id?: string;
    resource_type?: string;
    ticket?: string;
    auth_code?: string;
    app_id?: string;
    session_id?: string;
  } | null>(null);

  useEffect(() => {
    // 从url中获取sessionId恢复对话
    const { SessionId } = getUrlParams();
    if (!SessionId) {
      commonState.actions.setSessionId('');
      return;
    }
    const getAllMessages = async () => {
      const fetchPage = async (token?: string): Promise<EventItem[]> => {
        const response = await ListSessionHistoryMessage({
          SessionId,
          NextToken: token,
          MaxResults: 100,
        });
        const { Events = [] } = response.Data || {};
        // 取第一条消息的EventId， 倒序输出
        eventIdRef.current = Events[0]?.EventId;
        const { NextToken: newNextToken } = response;

        if (newNextToken) {
          const nextEvents = await fetchPage(newNextToken);
          return [...Events, ...nextEvents];
        }
        return Events;
      };

      const data = await fetchPage();

      setMessages(data.reduce((memo: BubbleDataType[], event) => {
        const temp: BubbleDataType[] = [];
        // 用户输入
        // const userMessage = event?.find((it) => it.Role === 'user' && it.Type === 'TEXT_MESSAGE_CONTENT');
        if (event?.Role === 'user' && event?.Type === 'TEXT_MESSAGE_CONTENT') {
          temp.push({
            role: 'user',
            content: event?.Content || '',
            messageId: event?.MessageId || '',
          });
        }
        // ai回复
        // const assistantMessage = event.find((it) => it.Role === 'assistant' && it.Type === 'TEXT_MESSAGE_CONTENT');
        if (event?.Role === 'assistant' && event?.Type === 'TEXT_MESSAGE_CONTENT') {
          temp.push({
            role: 'assistant',
            content: event?.Content || '',
            messageId: event?.MessageId || '',
            feedback: event?.Feedback,
            eventId: event?.EventId || '',
            extInfo: event?.ExtInfo || '',
          });
        }
        // return [...memo, ...temp];
        return [...temp.reverse(), ...memo];
      }, []));
      setDeepMessages(data.reduce((memo: any[], event) => {
        const temp: BubbleDataType[] = [];
        // 用户输入
        if (event?.Role === 'user' && event?.Type === 'TEXT_MESSAGE_CONTENT') {
          temp.push({
            role: 'user',
            content: event?.Content || '',
            messageId: event?.MessageId || '',
          });
        }
        if (event.Type === 'CUSTOM' && event.Name === 'understanding_summary') {
          temp.push({
            role: 'assistant',
            content: safeParseJSON(event?.Content)?.user_reply || '',
            messageId: event?.MessageId || '',
          });
        }
        if (event?.Type === 'ARTIFACT' && event.FileType === 'html') {
          temp.push({
            role: 'assistant',
            content: '',
            messageId: event?.MessageId || '',
            file: {
              ArtifactId: event?.ArtifactId,
              FileType: event?.FileType,
              FileName: event?.FileName,
            },
          });
        }
        // return [...memo, ...temp];
        return [...temp.reverse(), ...memo];
      }, []));
      // 历史消息不展示当前会话
      // TODO: 未返回消息或最后一条消息不是结束，继续建立长连接
      if (!data[0]?.Type || !doneStatus.includes(data[0].Type)) {
        setTimeout(() => {
          setLoading(true);
          requestStream(SessionId);
        });
      }
    };

    setSessionInfo({ SessionId });
    commonState.actions.setSessionId(SessionId);
    getAllMessages();
  }, []);

  const handleReConnect = (sessionId: string) => {
    if (rapRef.current < 6 && rapRef.current >= 0) {
      rapRef.current += 1;
      setTimeout(() => {
        setLoading(true);
        console.log('重试', rapRef.current, { sessionId });
        requestStream(sessionId);
      }, 10000);
    } else {
      setLoading(false);
    }
  };

  const { run: requestStream } = useRequest(
    async (sessionId: string) => {
      if (sessionId && messages.length > 0) {
        // 获取sessionId后建立长连接
        const loginInfo = await getLoginInfo();
        rapRef.current = 0;
        request.create(
          {
            ...baseOption,
            LoginToken: loginInfo.loginToken,
            LoginSessionId: loginInfo.loginSessionId,
            RegionId: loginInfo.loginRegionId,
            LastEventId: eventIdRef.current, // 从上个消息结果获取，用于长连接失败重试
            SessionId: sessionId,
            Action: 'GetStreamMessage',
          },
          {
            onSuccess: (messages) => {
              // 长连接成功这个eventId不再需要
              console.log(
                'onSuccess',
                messages.map(({ data }) => safeParseJSON(data)),
              );
              const lastMessage: any = safeParseJSON(messages[messages.length - 1]?.data);
              // 判断是否结束
              // const lastMessage = messages[messages.length - 1];
              if (!doneStatus.includes(lastMessage?.Type)) {
                handleReConnect(sessionId);
                return;
              }
              setLoading(false);
              rapRef.current = 0;
              // setMessages((prev) => [...prev, { role: 'assistant', content: 'lorem ipsum' }]);
            },
            onError: (error) => {
              // 长连接失败重试
              console.log('onError', error);
              handleReConnect(sessionId);
              setLoading(false);
            },
            onUpdate: ({ data }) => {
              const parsedData: StreamData = safeParseJSON(data);
              console.log('onUpdate', parsedData);
              eventIdRef.current = parsedData.EventId || eventIdRef.current;
              if (parsedData.Role === 'user') {
                // 补充用户输入的messageId
                if (parsedData.Type === 'TEXT_MESSAGE_CONTENT') {
                  setMessages((prev) => {
                    const currentUserMsg = prev.findLast(
                      (it) => it.content === parsedData.Content,
                    );
                    if (currentUserMsg) {
                      currentUserMsg.messageId = parsedData.MessageId;
                    }
                    return [
                      ...prev,
                    ];
                  });
                }
              }
              if (parsedData.Role === 'assistant') {
                if (parsedData.Type === 'TEXT_MESSAGE_DELTA_CONTENT') {
                  setMessages((prev) => {
                    const currentAssistantMsg = prev.find(
                      (it) => it.messageId === parsedData.MessageId,
                    );
                    if (currentAssistantMsg) {
                      prev.pop();
                    }
                    return [
                      ...prev,
                      {
                        role: 'assistant',
                        content: `${currentAssistantMsg?.content ?? ''}${parsedData?.Content ?? ''}`,
                        messageId: parsedData.MessageId,
                      },
                    ];
                  });
                }
                if (parsedData.Type === 'TEXT_MESSAGE_CONTENT') {
                  // 获取大模型返回消息
                  setMessages((prev) => {
                    // 退掉之前的delta消息，
                    const currentAssistantMsg = prev.find(
                      (it) => it.messageId === parsedData.MessageId,
                    );
                    if (currentAssistantMsg) {
                      prev.pop();
                    }
                    return [
                      ...prev,
                      {
                        role: 'assistant',
                        content: parsedData.Content || '',
                        messageId: parsedData.MessageId,
                        eventId: parsedData.EventId,
                      },
                    ];
                  });
                }
              }
              // 工具调用消息
              if (parsedData.Type === 'TOOL_CALL_ARGS') {
                setToolCallMsgs(
                  (prev) => [
                    ...prev,
                    {
                      toolName: parsedData.ToolName || '',
                      toolCallId: parsedData.ToolCallId || '',
                      toolCallContent: parsedData.Content || '',
                      isFinished: false,
                    },
                  ],
                );
              }
              // 工具调用结果
              if (parsedData.Type === 'TOOL_CALL_RESULT') {
                setToolCallMsgs(
                  (prev) => {
                    const currentToolCall = prev.find(
                      (item) => item.toolCallId === parsedData.ToolCallId,
                    );
                    if (currentToolCall) {
                      currentToolCall.isFinished = true;
                      // currentToolCall.toolCallContent = parsedData.Content;
                    }
                    return [
                      ...prev,
                    ];
                  },
                );
              }
              // 拉起云电脑信息
              if (parsedData.Type === 'CUSTOM' && parsedData.Name === 'pc_operator_info') {
                const pcOperatorInfo = parsedData.Content;
                setConnectInfo(safeParseJSON(pcOperatorInfo));
              }
              // 处理deepResearch需要展示的信息
              handleDeepResearchMessage(parsedData);
            },
            onStream: (controller) => {
              chatAbortController.current = controller;
            },
          },
        );
      } else {
        setLoading(false);
      }

      return Promise.resolve();
    },
    {
      manual: true,
    },
  );
  const sendMessage = async (val: string, resources: ResourceItem[] = [], desktopId?: string) => {
    setLoading(true);
    setMessages((prev) => [...prev, { role: 'user', content: val, resources }]);
    setDeepMessages((prev: any) => [...prev, { role: 'user', content: val, resources }]);
    setCustomMessages({}); // 清空自定义消息
    // 先发起一次SendMessage才能建立长链接
    // 联调长连接
    try {
      const res = await SendMessage({
        Prompt: val,
        AgentId: agentId, // 区分是否为主agent
        Resources: resources,
        SessionId: sessionInfo?.SessionId, // 传入上次的sessionId, 用于多轮对话
        DesktopId: desktopId,
      });
      if (res?.Code !== '200') {
        await Promise.reject(res);
      }
      const { SessionId } = res.Data;
      // 缓存sessionId，需要和文件传输共用
      setSessionInfo({
        SessionId,
      });
      commonState.actions.setSessionId(SessionId);
      // 请求长连接消息， 这里拿到的sessionId一定是当前最新的
      requestStream(SessionId);
    } catch (e: any) {
      // TODO: 对话错误统一处理
      console.log(e);
      notification.error({
        message: e?.Message || '对话失败',
      });
      setLoading(false);
    }
  };
  // 清空会话、中断长连接
  const resetChat = () => {
    setMessages([]);
    setDeepMessages([]);
    setCustomMessages({});
    setToolCallMsgs([]);
    setLoading(false);
    setSessionInfo({});
    setConnectInfo(null);
    stopChat();
    eventIdRef.current = '';
    // 重置会话需要重置所有的tab
    closeAllTabs();
  };

  const stopChat = () => {
    if (!sessionInfo?.SessionId) return;
    // 手动终止长连接时不再重试
    rapRef.current = -1;
    chatAbortController?.current?.abort?.();
    CancelSession({ SessionId: sessionInfo?.SessionId });
  };

  // 抽取处理不同类型消息的函数
  const handleDeepResearchMessage = (
    parsedData: StreamData,
  ) => {
    if (parsedData.Role === 'user') {
      // 补充用户输入的messageId
      if (parsedData.Type === 'TEXT_MESSAGE_CONTENT') {
        setDeepMessages((prev) => {
          const currentUserMsg = prev.findLast(
            (it) => it.content === parsedData.Content,
          );
          if (currentUserMsg) {
            currentUserMsg.messageId = parsedData.MessageId;
          }
          return [
            ...prev,
          ];
        });
      }
    }
    if (parsedData.Type === 'CUSTOM') {
      if (parsedData.Name === 'understanding_summary') {
        setDeepMessages((prev) => {
          return [
            ...prev,
            {
              role: 'assistant',
              content: safeParseJSON(parsedData.Content).user_reply || '',
              messageId: parsedData.MessageId,
            },
            {
              role: 'assistant',
              content: '',
              requires_research: safeParseJSON(parsedData.Content).requires_research || false,
              messageId: '',
            },
          ];
        });
        setCustomMessages((prev) => ({
          ...prev,
          requires_research: safeParseJSON(parsedData.Content).requires_research,
          research_plan: safeParseJSON(parsedData.Content).research_plan || '',
        }));
      } else if (parsedData.Name === 'report_plan') {
        setCustomMessages((prev) => ({
          ...prev,
          report_outline: safeParseJSON(parsedData.Content).report_outline || '',
          report_title: safeParseJSON(parsedData.Content).report_title || '',
        }));
      } else if (parsedData.Name === 'iterative_research_report') {
        const content = safeParseJSON(parsedData?.Content);
        if (content?.iterative_research_id) {
          setCustomMessages((prev) => {
            const { report_outline: originalOutline = [] } = prev;
            const { iterative_research_id: targetId, content: newContent } = content || {};

            // 生成新数组：找到匹配项并替换 content，其他保持不变
            const updatedOutline = originalOutline?.map((item) => {
              if (item.iterative_research_id === targetId) {
                return {
                  ...item, // 浅拷贝原对象
                  content: newContent, // 更新 content
                };
              }
              return item; // 其他项原样返回
            });

            return {
              ...prev,
              report_outline: updatedOutline, // ✅ 新数组，且被修改的项也是新对象
            };
          });
        }
      }
    }
    if (parsedData.Type === 'ARTIFACT' && parsedData?.FileType === 'html') {
      setDeepMessages((prev) => {
        // 删除所有 requires_research 为 true 的项
        const newPre = prev?.filter((item) => !item?.requires_research);
        return [
          ...newPre,
          {
            role: 'assistant',
            content: '',
            file: {
              ArtifactId: parsedData?.ArtifactId,
              FileType: parsedData?.FileType,
              FileName: parsedData?.FileName,
            },
            messageId: parsedData?.MessageId,
          },
        ];
      });
    }
    if (parsedData.Type === 'RUN_ERROR') {
      setLoading(false);
      setCustomMessages((prev) => ({
        ...prev,
        run_error: true,
      }));
    }
    if (parsedData.Type === 'RUN_FINISHED') {
      setLoading(false);
      setCustomMessages((prev) => ({
        ...prev,
        run_success: true,
      }));
    }
  };

  return {
    connectInfo,
    sendMessage,
    resetChat,
    stopChat,
    loading,
    messages,
    setMessages,
    toolCallMsgs,
    sessionInfo,
    setSessionInfo,
    deepMessages,
    setDeepMessages,
    customMessages,
  };
};

export default useChat;
