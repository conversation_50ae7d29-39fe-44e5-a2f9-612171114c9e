import { useState, useCallback } from 'react';
import { useSnapshot } from 'umi';
import { scriptManager } from '@/utils/script';
import pptState from '@/model/pptModel';
import { useRequest } from 'ahooks';
import {
  AiPPTOnMessage,
  AiPPTEventTypeEnum,
  AiPPTEventDownloadData,
  AiPPTSaveData,
  AiPPTGeneratePPTSuccessData,
} from '@/types/ppt';
import { useTabContext } from '@/contexts/TabContext';
import { GetPPTAuthCode, AuthCodeData } from '@/server/WuyingAI/GetPPTAuthCode';
import PPTDetail from '@/pages/ppt/ppt-detail';
import { showErrorModal } from '@/server/request';
import { EventType, useGlobalEventEmitter } from '@/hooks/useGlobalEventEmitter';
import { PPT_TABS } from '@/constant/tabs';

interface UseAiPPTProps {
  containerRef?: React.RefObject<HTMLDivElement>;
  // onMessage?: AiPPTOnMessage;
  onDownload?: (data: AiPPTEventDownloadData) => void;
  onSave?: (data: AiPPTSaveData) => void;
  onGeneratePPTSuccess?: (data: AiPPTGeneratePPTSuccessData) => void;
}

const sdkUrl = 'https://api-static.aippt.cn/aippt-iframe-sdk.js';

const useAiPPT = (props: UseAiPPTProps = {}) => {
  const { onDownload, onSave, onGeneratePPTSuccess } = props;
  const event$ = useGlobalEventEmitter();
  const [initStatus, setInitStatus] = useState<'loading' | 'success' | 'error'>('loading'); // 前置条件初始化是否完成
  const { runAsync: getPPTAuthCode, data: pptAuthCode } = useRequest(GetPPTAuthCode, {
    manual: true,
    cacheKey: 'ppt-GetPPTAuthCode',
    staleTime: 24 * 60 * 60 * 1000,
  });
  const { addTab } = useTabContext();

  // 前置条件的初始化
  const initPPT = useCallback(async () => {
    setInitStatus('loading');
    let ret = {};
    try {
      const [, codeInfo] = await Promise.all([
        scriptManager.preloadScript(sdkUrl),
        getPPTAuthCode(),
      ]);
      ret = codeInfo.Data ?? {};
      setInitStatus('success');
    } catch (error) {
      setInitStatus('error');
    }
    return ret as AuthCodeData;
  }, []);

  const unloadPPT = useCallback(() => {
    AipptIframe?.deleteIframe();
  }, []);

  const getCreateConfig = useCallback(({ content }: { content: string }) => {
    return {
      options: {
        /**
         * 2014 - 合成页
         * 2025 - ppt编辑器：支持大纲编辑
         * 2026 - ppt编辑器：支持模板替换
         * 2029 - ppt编辑器：支持下载
         * 2037 - ppt编辑器：支持AI创作助手
         */
        fc_plate: [2014, 2025, 2026, 2029, 2037],
        custom_generate: {
          type: 7,
          step: 2,
          content,
        },
        // download_mode: 2, // 只发送消息通知，但不会进行自动下载，包含可下载地址
      },
    };
  }, []);

  const getEditConfig = useCallback(({ pptId }: { pptId: string }) => {
    return {
      options: {
        /**
         * 2014 - 合成页
         * 2025 - ppt编辑器：支持大纲编辑
         * 2026 - ppt编辑器：支持模板替换
         * 2029 - ppt编辑器：支持下载
         * 2037 - ppt编辑器：支持AI创作助手
         */
        fc_plate: [2014, 2025, 2026, 2029, 2037],
      },
      routerOptions: {
        list: ['workspace', 'editor'],
        // // 存在editor 并且存在id的情况下 则直接跳转至编辑器页面
        editor: {
          id: Number(pptId),
          showLogo: 3, // 不展示aippt logo
        },
      },
    };
  }, []);

  // 下载
  const handleDownload = useCallback((data: AiPPTEventDownloadData) => {
    onDownload?.(data);
  }, []);
  // 保存
  const handleSave = useCallback((data: AiPPTSaveData) => {
    onSave?.(data);
  }, []);
  // 生成ppt成功
  const handleGeneratePPTSuccess = (data: AiPPTGeneratePPTSuccessData) => {
    event$.emit({
      type: EventType.PPT_CREATE_SUCCESS,
      payload: {
        ...data,
        eventId: pptState.currInfo.eventId,
      },
    });
    // onGeneratePPTSuccess?.(data);
  };
  // 消息处理
  const handleMessage: AiPPTOnMessage = (eventType, data: any) => {
    const eventMap = {
      [AiPPTEventTypeEnum.PPT_DOWNLOAD]: handleDownload,
      [AiPPTEventTypeEnum.PPT_SAVE]: handleSave,
      [AiPPTEventTypeEnum.GENERATE_PPT_SUCCESS]: handleGeneratePPTSuccess,
    };
    eventMap[eventType]?.(data);
  };

  const loadAiPPT = async (loadProps: { appkey: string; channel: string; code: string; type: 'create' | 'edit'; container: HTMLDivElement; content?: string; pptId?: string }) => {
    const { appkey, channel, code, type, container, content, pptId } = loadProps;
    try {
      if (!container || !code) {
        return;
      }
      const config = type === 'create' ? getCreateConfig({ content: content! }) : getEditConfig({ pptId: pptId! });
      await AipptIframe?.show({
        appkey,
        channel,
        code,
        container: container!,
        editorModel: true, // 是否可编辑PPT模板功能
        ...config,
        onMessage: handleMessage,
      });
    } catch (error: any) {
      console.log('lxy loadAiPPT error', error);
      // ai ppt sdk 的报错不是Error类型，需要手动处理
      showErrorModal({
        Message: error?.msg ?? '未知错误',
        Code: error?.code ?? '-1',
      });
    }
  };

  const openCreateTab = async ({ content, sessionId, eventId }: { content: string; sessionId: string; eventId?: string }) => {
    // 因为tab会复用，aiPPT sdk不支持多个iframe，所以创建和编辑是同一个tab，新开创建或者编辑的时候，需要提示用户进行保存
    pptState.actions.setNextPptInfo({ type: 'create', content, sessionId, eventId });
    addTab(PPT_TABS.pptCreate);
  };

  const openEditTab = async ({ pptId }: { pptId: string }) => {
    // 因为tab会复用，aiPPT sdk不支持多个iframe，所以创建和编辑是同一个tab，新开创建或者编辑的时候，需要提示用户进行保存
    pptState.actions.setNextPptInfo({ type: 'edit', pptId });
    addTab(PPT_TABS.pptEdit);
  };

  const createPPT = async ({ container, content }: { container: HTMLDivElement; content: string }) => {
    const { ApiKey: appkey, Code: code, Channel: channel } = await initPPT();
    loadAiPPT({ appkey, channel, code, type: 'create', container, content });
  };

  const editPPT = async ({ container, pptId }: { container: HTMLDivElement; pptId: string }) => {
    const { ApiKey: appkey, Code: code, Channel: channel } = await initPPT();
    loadAiPPT({ appkey, channel, code, type: 'edit', container, pptId });
  };

  return {
    initStatus,
    initPPT,
    openCreateTab,
    openEditTab,
    unloadPPT,
    createPPT,
    editPPT,
  };
};

export default useAiPPT;
