import React, { useContext } from 'react';
import { useEventEmitter } from 'ahooks';

// 将所有的事件类型定义在这里
export enum EventType {
  // CREATE_KNOWLEDGE_BASE = 'CREATE_KNOWLEDGE_BASE', // 创建知识库
  PPT_CREATE_SUCCESS = 'PPT_CREATE_SUCCESS', // PPT 创建成功
  PPT_SDK_ERROR = 'PPT_SDK_ERROR', // aiPPT SDK 错误
}

export interface EventEmitterValueType {
  type: EventType;
  payload?: any;
}

export type EventEmitterType = ReturnType<typeof useEventEmitter<EventEmitterValueType>>;

const EventEmitterContext = React.createContext<EventEmitterType | null>(null);

export const EventEmitterProvider = ({ children }: { children: React.ReactNode }) => {
  const event$ = useEventEmitter<EventEmitterValueType>();
  return <EventEmitterContext.Provider value={event$}>{children}</EventEmitterContext.Provider>;
};

/**
 * 使用方式
 * 1. 在需要触发事件的组件中，使用 useGlobalEventEmitter 获取事件发射器
 * 2. 使用 event$.emit 触发事件
 * 3. 使用 event$.useSubscription 监听事件
 *
 * 触发事件
 * const event$ = useGlobalEventEmitter();
 * event$.emit({
 *   type: EventType.CREATE_KNOWLEDGE_BASE,
 *   payload: {
 *     name: 'test',
 *   },
 * });
 *
 * 监听事件
 * const event$ = useGlobalEventEmitter();
 * event$.useSubscription((data) => {
 *   console.log(data);
 * });
 */
export const useGlobalEventEmitter = () => {
  const event$ = useContext(EventEmitterContext);
  if (!event$) {
    throw new Error('useGlobalEventEmitter must be used within EventEmitterProvider');
  }
  return event$;
};

export default useGlobalEventEmitter;
