import { Iconfont } from '@/components/icon';
import { Button, Input, Modal, Radio, Divider, notification, Popover } from 'antd';
import { useRequest } from 'ahooks';
import { useCallback, useEffect } from 'react';
import { createStyles, css } from 'antd-style';
import {
  ListKnowledgeBases,
  ListKnowledgeBaseData,
} from '@/server/WuyingAI/ListKnowledgeBases';
import { CreateKnowledgeBase } from '@/server/WuyingAI/CreateKnowledgeBase';
import { UpdateKnowledgeBaseSession } from '@/server/WuyingAI/UpdateKnowledgeBaseSession';
import { ListUserSessionKbMessage } from '@/server/WuyingAI/ListUserSessionKbMessage';
import { useState } from 'react';

const useAddToKBModalStyles = createStyles(() => ({
  KBListWrapper: css`
    display: flex;
    flex-direction: column;
    max-height: 400px;
    overflow-y: auto;
      margin: 4px 0;

  `,
  KBListItem: css`
  margin: 4px 0;
display: flex;
    align-items: flex-start;
    border-radius: 12px;
    border: 1px solid transparent;
    padding: 12px;
    cursor: pointer;

    &:hover {
border: 1px solid #0075FF;
    }
  `,
  KBListItemSelected: css`
border: 1px solid #0075FF;
  `,
  KBListItemDesc: css`
  color: #8C909C;
  font-size: 12px;
  `,
}));

export const KBLists = ({ knowledgeBases, onChange }: {
  knowledgeBases: ListKnowledgeBaseData[];
  onChange: (id: string) => void;
}) => {
  const { styles, cx } = useAddToKBModalStyles();
  const [current, setCurrent] = useState<string>();

  const selectKb = useCallback((id: string) => {
    setCurrent(id);
    onChange(id);
  }, [onChange, setCurrent]);

  useEffect(() => {
    if (knowledgeBases?.length) {
      const firstKbId = knowledgeBases[0].KbId;
      selectKb(firstKbId);
    }
  }, [knowledgeBases, onChange, selectKb]);

  return (<div className={styles.KBListWrapper}>
    {
          knowledgeBases?.map?.((it) =>
            (<div
              key={it.KbId}
              className={cx(styles.KBListItem, {
                [styles.KBListItemSelected]: it.KbId === current,
              })}
              onClick={() => {
                selectKb(it.KbId);
              }}
            >
              <Radio
                checked={current === it.KbId}
              />
              <div>
                <div>{it.Name}</div>
                <div className={styles.KBListItemDesc}>
                  <span>{it.DocumentCount}个文件</span>
                  <Divider type="vertical" />
                  <span>{it.SessionCount}个会话</span>
                </div>
              </div>
            </div>))
        }
  </div>);
};

const useAddToKBModal = () => {
  const { runAsync: getKnowledgeBases } = useRequest(
    () => {
      return ListKnowledgeBases({});
    },
    {
      manual: true,
    },
  );
  const showAddKbModal = async ({
    sessionId,
    messageIds,
    fileIdList,
    fileIgnore = false,
    messageIgnore = false,
    onSuccess,
  }: {
    sessionId?: string;
    messageIds?: string[];
    fileIdList?: string[];
    fileIgnore?: boolean;
    messageIgnore?: boolean;
    onSuccess?: () => void;
  }) => {
    const { Data: knowledgeBases } = await getKnowledgeBases();
    const createKnowledgeBaseDisabled = knowledgeBases?.length >= 5;
    let selectedKbId = '';
    let newKbName = '';
    const modal = Modal.confirm({
      title: '添加到知识库',
      icon: null,
      content: <div>
        <Popover
          content={createKnowledgeBaseDisabled ? <span>最多创建5个知识库</span> : null}
          trigger="hover"
          placement="top"
        >
          <Button
            icon={<Iconfont
              type="workstation--outline"
              style={{ width: 16, height: 16 }}
            />}
            disabled={createKnowledgeBaseDisabled}
            onClick={() => {
              modal.update({
                title: '新建知识库',
                content: <Input placeholder="请输入知识库名字" onChange={(e) => { newKbName = e.target.value; }} />,
                okText: '完成并添加',
              });
            }}
          >
            新建知识库
          </Button>
        </Popover>
        <KBLists knowledgeBases={knowledgeBases} onChange={(id) => { selectedKbId = id; }} />

      </div>,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (newKbName) {
            const res = await CreateKnowledgeBase({
              Name: newKbName,
              Description: '',
            }).catch(() => {
              notification.error({
                message: '创建知识库失败',
              });
            });
            selectedKbId = res?.Data?.KbId || '';
          }
          if (!selectedKbId) throw new Error('添加失败');
          // 添加会话到知识库
          let newMessageIds: string[] = [];
          if (messageIds?.length || fileIdList?.length) {
            // 知识库现在增量更新，需要先获取已添加的会话取并集
            const kbSessionRes = await ListUserSessionKbMessage({
              SessionId: sessionId,
              KbId: selectedKbId,
            });
            const messageIdsSet = new Set(messageIds);
            const messageIdsSet2 = new Set(kbSessionRes?.Data?.MessageIds || []);
            messageIdsSet2.forEach((id) => {
              messageIdsSet.add(id);
            });
            newMessageIds = Array.from(messageIdsSet);
            // 加入知识库
          }
          const res = await UpdateKnowledgeBaseSession({
            KbId: selectedKbId,
            SessionId: sessionId!,
            MessageIdList: newMessageIds,
            FileIdList: fileIdList,
            FileIgnore: fileIgnore,
            MessageIgnore: messageIgnore,
          });
          if (res.Code === '200') {
            onSuccess?.();
            notification.success({
              message: `已存储到${newKbName || (knowledgeBases.find((it) => it.KbId === selectedKbId)?.Name ?? '')}`,
            });
          } else {
            notification.error({
              message: '添加失败',
            });
          }
        } catch (e) {
          notification.error({
            message: '存储失败',
          });
        }
      },
      onCancel: () => {
        modal.destroy();
      },
    });
  };

  return {
    showAddKbModal,
  };
};

export default useAddToKBModal;
