import { useCallback, useRef } from 'react';
import { useInfiniteScroll } from 'ahooks';
import type { Data, InfiniteScrollOptions } from 'ahooks/lib/useInfiniteScroll/types';

interface PaginationResponse<T> {
  Data: T[];
  TotalCount?: number;
  NextToken?: string;
  Code?: string | number;
}

interface PaginationRequest {
  MaxResults?: number;
  NextToken?: string;
  [key: string]: any;
}

// ahooks期望的数据格式
interface InfiniteScrollData<T> extends Data {
  list: T[];
  NextToken?: string;
  TotalCount?: number;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface UseInfiniteScrollWithTokenOptions<TParams extends PaginationRequest>
  extends Omit<InfiniteScrollOptions<InfiniteScrollData<any>>, 'target' | 'isNoMore'> {
  /**
   * 每页数量，默认20
   */
  pageSize?: number;
  /**
   * 是否立即开始请求，默认true
   */
  ready?: boolean;
  /**
   * 依赖项数组，变化时会重置并重新请求
   */
  refreshDeps?: any[];
  /**
   * 滚动容器的选择器或元素
   */
  target?: InfiniteScrollOptions<InfiniteScrollData<any>>['target'];
}

/**
 * 基于ahooks的useInfiniteScroll，适配MaxResults、NextToken分页的无限滚动hook
 *
 * @param service 请求函数，接收分页参数并返回分页数据
 * @param options 配置选项
 * @returns 无限滚动相关的状态和方法
 *
 * @example
 * ```typescript
 * // 基本使用
 * const {
 *   data,
 *   loading,
 *   loadingMore,
 *   noMore,
 *   refresh,
 *   loadMore,
 *   reset
 * } = useInfiniteScrollWithToken(
 *   async (params) => {
 *     return await listKnowledgeBases({
 *       MaxResults: params.MaxResults,
 *       NextToken: params.NextToken,
 *       Keyword: searchKeyword
 *     });
 *   },
 *   {
 *     target: () => document.getElementById('scroll-container'),
 *     pageSize: 20,
 *     refreshDeps: [searchKeyword]
 *   }
 * );
 *
 * // 在组件中使用
 * <div id="scroll-container" style={{ height: '400px', overflow: 'auto' }}>
 *   {data.map(item => (
 *     <div key={item.id}>{item.name}</div>
 *   ))}
 *   {loadingMore && <div>加载中...</div>}
 *   {noMore && <div>没有更多数据了</div>}
 * </div>
 * ```
 */
export function useInfiniteScrollWithToken<TData, TParams extends PaginationRequest>(
  service: (params: TParams) => Promise<PaginationResponse<TData>>,
  options: UseInfiniteScrollWithTokenOptions<TParams> = {},
) {
  const {
    pageSize = 20,
    ready = true,
    refreshDeps = [],
    target,
    ...restOptions
  } = options;

  // 存储额外的请求参数（除了分页参数）
  const extraParamsRef = useRef<Omit<TParams, 'MaxResults' | 'NextToken'>>({} as any);

  // 更新额外参数的方法
  const updateExtraParams = useCallback((params: Omit<TParams, 'MaxResults' | 'NextToken'>) => {
    extraParamsRef.current = params;
  }, []);

  // 请求函数
  const fetchData = useCallback(async (data?: InfiniteScrollData<TData>) => {
    if (!ready) {
      return {
        list: [],
        NextToken: undefined,
      } as InfiniteScrollData<TData>;
    }

    const nextToken = data?.NextToken;

    const requestParams = {
      MaxResults: pageSize,
      NextToken: nextToken,
      ...extraParamsRef.current,
    } as TParams;

    const response = await service(requestParams);

    return {
      list: response.Data || [],
      NextToken: response.NextToken,
      TotalCount: response.TotalCount,
    } as InfiniteScrollData<TData>;
  }, [service, pageSize, ready]);

  // 使用ahooks的useInfiniteScroll
  const {
    data,
    loading,
    loadingMore,
    noMore,
    reload,
    loadMore,
    cancel,
    mutate,
  } = useInfiniteScroll(
    fetchData,
    {
      target,
      isNoMore: (result) => !result?.NextToken,
      reloadDeps: refreshDeps,
      ...restOptions,
    },
  );

  // 获取所有数据的平铺数组
  const allData = data?.list || [];

  // 获取总数
  const totalCount = data?.TotalCount;

  // 刷新数据（重新从第一页开始）
  const refresh = useCallback(() => {
    reload();
  }, [reload]);

  // 重置数据
  const reset = useCallback(() => {
    mutate(undefined);
  }, [mutate]);

  // 手动加载更多
  const manualLoadMore = useCallback(() => {
    if (!loadingMore && !noMore) {
      loadMore();
    }
  }, [loadMore, loadingMore, noMore]);

  // 更新请求参数并刷新
  const refreshWithParams = useCallback((params: Omit<TParams, 'MaxResults' | 'NextToken'>) => {
    updateExtraParams(params);
    reload();
  }, [updateExtraParams, reload]);

  return {
    /** 所有数据的平铺数组 */
    data: allData,
    /** 首次加载状态 */
    loading,
    /** 加载更多的状态 */
    loadingMore,
    /** 是否没有更多数据 */
    noMore,
    /** 总数量 */
    totalCount,
    /** 刷新数据（从第一页开始） */
    refresh,
    /** 手动加载更多 */
    loadMore: manualLoadMore,
    /** 重置数据 */
    reset,
    /** 取消请求 */
    cancel,
    /** 更新请求参数并刷新 */
    refreshWithParams,
    /** 手动修改数据 */
    mutate,
  };
}

export default useInfiniteScrollWithToken;
