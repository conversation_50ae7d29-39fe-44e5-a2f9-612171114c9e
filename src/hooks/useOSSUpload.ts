import { useState, useRef } from 'react';
import { message, Modal } from 'antd';
import { UploadService } from '@/server/upLoad';
import { FileItem } from '@/server/upLoad';
import { AgentIdEnum } from '@/constant/enum';

export const useOSSUpload = ({
  agentId,
  sessionInfo,
  setSessionInfo,
}: {
  agentId: AgentIdEnum;
  sessionInfo: {
    SessionId?: string;
  };
  setSessionInfo: (sessionInfo: { SessionId: string }) => void;
}) => {
  const [uploadList, setUploadList] = useState<FileItem[]>([]);
  const [uploading, setUploading] = useState(false);

  // 更新文件状态的抽象函数
  const updateFileStatus = (fileId: string, updates: Partial<FileItem>) => {
    setUploadList((prev) =>
      prev.map((item) => (item.fileId === fileId ? { ...item, ...updates } : item)));
  };

  // 轮询查询文件分析状态
  const startPollingAnalyzeStatus = async (fileId: string) => {
    const pollInterval = 3000; // 3秒轮询一次
    const maxPollingTime = 3 * 60 * 1000; // 最大轮询3分钟
    const startTime = Date.now();

    const poll = async () => {
      try {
        const statusRes = await UploadService.getFileAnalyzeStatus({
          FileId: fileId,
        });

        if (statusRes?.Code !== '200') {
          updateFileStatus(fileId, { status: 'failed' as const });
          return;
        }

        const { Status } = statusRes.Data;

        switch (Status) {
          case 'analyzing':
            updateFileStatus(fileId, { status: 'parsing' as const });
            // 继续轮询
            if (Date.now() - startTime < maxPollingTime) {
              setTimeout(poll, pollInterval);
            } else {
              // 超时处理
              updateFileStatus(fileId, { status: 'failed' as const });
              message.error('文件分析超时，请重试');
            }
            break;
          case 'completed':
            updateFileStatus(fileId, { status: 'completed' as const });
            break;
          case 'failed':
            updateFileStatus(fileId, { status: 'failed' as const });
            break;
          default:
            // 继续轮询
            if (Date.now() - startTime < maxPollingTime) {
              setTimeout(poll, pollInterval);
            } else {
              updateFileStatus(fileId, { status: 'failed' as const });
              message.error('文件分析超时，请重试');
            }
        }
      } catch (error) {
        console.error('轮询文件分析状态失败:', error);
        updateFileStatus(fileId, { status: 'failed' as const });
      }
    };

    // 开始轮询
    setTimeout(poll, pollInterval);
  };
  // 使用预签名链接上传文件到 OSS
  const startUpload = async (file: File) => {
    // 文件大小限制
    if (file.size > 1024 * 1024 * 100) {
      message.error('文件大小不能超过100MB');
      return;
    }
    if (!/\.(pdf|word|ppt|pptx|xls|xlsx|xlsm|jpg|jpeg|png|bmp|gif|markdown|html|epub|mobi|rtf|txt)$/.test(file.name)) {
      message.error('文件格式不支持');
      return;
    }
    setUploading(true);
    try {
      // 1. 获取预签名上传链接
      const presignedData: any = await UploadService.createPresignedUpload({
        AgentId: agentId,
        SessionId: sessionInfo.SessionId,
        FileInfo: {
          FileType: file.type,
          FileName: file.name,
          FileSize: file.size,
        },
      });
      if (presignedData?.Code === '500') {
        message.error(presignedData?.Message);
        return;
      }
      // 添加文件到上传列表
      addToUploadList(file, presignedData.fileId);

      // 若之前没有sessionId，则更新sessionId
      if (!sessionInfo.SessionId) {
        setSessionInfo({ SessionId: presignedData?.sessionId });
      }

      // 2. 直接上传到 OSS
      try {
        await UploadService.uploadToOSS(presignedData.uploadUrl, presignedData.headers, file, (progress: number) => {
          // 更新上传进度
          updateFileStatus(presignedData.fileId, { progress, status: 'uploading' as const });
        });
      } catch (e: any) {
        Modal.error({
          title: `${file.name}上传文件失败`,
          content: e.message,
        });
        updateFileStatus(presignedData.fileId, { status: 'error' as const, progress: 0 });
        return;
      }

      // 3. 上传完成，设置状态为解析中
      updateFileStatus(presignedData.fileId, { status: 'parsing' as const, progress: 100 });

      // 4. 确认上传完成
      try {
        const confirmRes = await UploadService.confirmPresignedUpload({
          FileId: presignedData.fileId,
        });
        if (confirmRes?.Code !== '200') {
          message.error(confirmRes?.Message);
          updateFileStatus(presignedData.fileId, { status: 'failed' as const });
          return;
        } else {
          // 根据新的接口行为，如果状态是 analyzing，需要开始轮询
          if (confirmRes?.Data?.Status === 'analyzing') {
            updateFileStatus(presignedData.fileId, { status: 'parsing' as const });
            // 开始轮询查询分析状态
            startPollingAnalyzeStatus(presignedData.fileId);
          } else {
            updateFileStatus(presignedData.fileId, { status: confirmRes?.Data?.Status as any });
          }
        }
      } catch (e: any) {
        updateFileStatus(presignedData.fileId, { status: 'failed' as const });
        return;
      }

      // 5. 确认完成后更新状态
    } catch (e: any) {
      console.log(e, 'e====');
    }
    setUploading(false);
  };

  // 添加文件到上传列表
  const addToUploadList = (file: File, fileId: string) => {
    const newFile: FileItem = {
      fileId,
      name: file.name,
      size: file.size,
      status: 'ready',
      progress: 0,
      file,
    };
    setUploadList((prev) => [...prev, newFile]);
  };

  // 删除文件
  const removeFile = async (fileId: string): Promise<void> => {
    // 增加二次确认
    Modal.confirm({
      title: '确认删除该文件？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        setUploadList((prev) => prev.filter((item) => item.fileId !== fileId));
      },
    });
  };

  /**
   * 清空上传列表
   */
  const clearUploadList = () => {
    setUploadList([]);
  };

  return {
    uploadList,
    startUpload,
    removeFile,
    clearUploadList,
    uploading,
  };
};
