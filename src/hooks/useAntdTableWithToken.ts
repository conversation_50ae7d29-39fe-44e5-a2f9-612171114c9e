import { useState, useRef, useCallback, useEffect } from 'react';
import { useRequest } from 'ahooks';
import type { Options } from 'ahooks/lib/useRequest/src/types';

interface PaginationResponse<T> {
  Data: T[];
  TotalCount?: number;
  NextToken?: string;
  Code?: string | number;
}

interface PaginationRequest {
  MaxResults?: number;
  NextToken?: string;
  [key: string]: any;
}

interface TableProps {
  dataSource: any[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    onChange: (page: number, pageSize?: number) => void;
    onShowSizeChange: (current: number, size: number) => void;
  };
}

interface UseAntdTableWithTokenOptions<TParams extends PaginationRequest>
  extends Omit<Options<PaginationResponse<any>, [TParams]>, 'manual'> {
  defaultPageSize?: number;
  ready?: boolean;
  refreshDeps?: any[];
}

/**
 * 支持NextToken分页的useAntdTable hook
 * 适配Ant Design Table组件和后端NextToken分页方式
 */
export function useAntdTableWithToken<TData, TParams extends PaginationRequest>(
  service: (params: TParams) => Promise<PaginationResponse<TData>>,
  options: UseAntdTableWithTokenOptions<TParams> = {},
) {
  const {
    defaultPageSize = 20,
    ready = true,
    refreshDeps = [],
    onSuccess,
    ...restOptions
  } = options;

  // 分页数据存储
  const [allData, setAllData] = useState<TData[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);

  // 每页的NextToken映射
  const pageTokenMapRef = useRef<Map<number, string>>(new Map());
  const hasMoreRef = useRef(true);

  // 请求函数
  const { loading, run, refresh } = useRequest(
    async (params: TParams) => {
      const response = await service(params);
      return response;
    },
    {
      manual: true,
      onSuccess: (data, [params]) => {
        const isFirstPage = !params.NextToken;

        if (isFirstPage) {
          // 首页数据，重置所有状态
          setAllData(data.Data);
          setTotalCount(data.TotalCount || 0);
          setCurrentPage(1);
          pageTokenMapRef.current.clear();

          // 如果有NextToken，存储下一页的token
          if (data.NextToken) {
            pageTokenMapRef.current.set(2, data.NextToken);
            hasMoreRef.current = true;
          } else {
            hasMoreRef.current = false;
          }
        } else {
          // 非首页数据，找到对应的页码并更新数据
          let targetPage = 1;
          for (const [page, token] of pageTokenMapRef.current.entries()) {
            if (token === params.NextToken) {
              targetPage = page;
              break;
            }
          }

          // 更新全量数据
          const newAllData = [...allData];
          const startIndex = (targetPage - 1) * pageSize;
          const endIndex = startIndex + data.Data.length;

          // 填充数据到对应位置
          for (let i = 0; i < data.Data.length; i++) {
            newAllData[startIndex + i] = data.Data[i];
          }

          setAllData(newAllData);
          setTotalCount(data.TotalCount || 0);

          // 如果有NextToken，存储下一页的token
          if (data.NextToken) {
            pageTokenMapRef.current.set(targetPage + 1, data.NextToken);
          }
        }
        onSuccess?.(data, [params]);
      },
      ...restOptions,
    },
  );

  // 获取当前页数据
  const getCurrentPageData = useCallback(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return allData.slice(startIndex, endIndex);
  }, [allData, currentPage, pageSize]);

  // 检查是否需要加载数据
  const needsLoad = useCallback((page: number) => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // 检查这个页面的数据是否已经加载
    for (let i = startIndex; i < endIndex; i++) {
      if (i < totalCount && !allData[i]) {
        return true;
      }
    }
    return false;
  }, [allData, pageSize, totalCount]);

  // 页码变化处理
  const handlePageChange = useCallback((page: number, newPageSize?: number) => {
    const actualPageSize = newPageSize || pageSize;

    if (newPageSize && newPageSize !== pageSize) {
      // 页面大小变化，重新加载
      setPageSize(actualPageSize);
      setCurrentPage(1);
      setAllData([]);
      pageTokenMapRef.current.clear();

      if (ready) {
        run({
          MaxResults: actualPageSize,
        } as TParams);
      }
      return;
    }

    setCurrentPage(page);

    // 检查是否需要加载新数据
    if (needsLoad(page) && ready) {
      const token = pageTokenMapRef.current.get(page);
      if (token) {
        run({
          MaxResults: actualPageSize,
          NextToken: token,
        } as TParams);
      }
    }
  }, [pageSize, ready, run, needsLoad]);

  // 刷新数据
  const refreshData = useCallback(() => {
    setAllData([]);
    setCurrentPage(1);
    setTotalCount(0);
    pageTokenMapRef.current.clear();
    hasMoreRef.current = true;

    if (ready) {
      run({
        MaxResults: pageSize,
      } as TParams);
    }
  }, [ready, run, pageSize]);

  // 标记是否已经初始化过
  const initializedRef = useRef(false);

  // 监听依赖变化和初始加载
  useEffect(() => {
    if (!ready) return;

    // 如果是依赖变化导致的刷新
    if (initializedRef.current) {
      refreshData();
    } else {
      // 首次初始化
      initializedRef.current = true;
      if (allData.length === 0) {
        run({
          MaxResults: pageSize,
        } as TParams);
      }
    }
  }, [...refreshDeps, ready]);

  const tableProps: TableProps = {
    dataSource: getCurrentPageData(),
    loading,
    pagination: {
      current: currentPage,
      pageSize,
      total: totalCount,
      showSizeChanger: false,
      showQuickJumper: false,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    },
  };

  return {
    tableProps,
    refresh: refreshData,
    loading,
  };
}
