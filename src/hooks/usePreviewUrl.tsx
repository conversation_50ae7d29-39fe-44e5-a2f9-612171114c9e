import { useCallback } from 'react';
import { useTabContext } from '@/contexts/TabContext';
import { HtmlViewer } from '@/components/html-viewer';

export interface PreviewUrlOptions {
  key?: string;
  label?: string;
}

/**
 * 使用方式：
 * const { previewUrl } = useHTMLPreview();
 * previewUrl('https://example.com/page.html', { label: '页面预览' });
 */
export const usePreviewUrl = () => {
  const { addTab } = useTabContext();

  const previewUrl = useCallback(
    (url: string, options?: PreviewUrlOptions) => {
      if (!url) return '';

      return addTab({
        key: options?.key || url,
        label: options?.label || '文件预览',
        props: {
          source: { type: 'url', value: url },
        },
        destroyOnHidden: true,
        children: HtmlViewer,
        closable: true,
      });
    },
    [addTab],
  );

  return { previewUrl };
};

export default usePreviewUrl;

