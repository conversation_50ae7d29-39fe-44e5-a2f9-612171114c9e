declare module '@ali/aes-tracker' {
  const AES: any;
  export default AES;
}

declare module '@ali/aes-tracker-plugin-pv' {
  const AESPluginPV: any;
  export default AESPluginPV;
}

declare module '@ali/aes-tracker-plugin-event' {
  const AESPluginEvent: any;
  export default AESPluginEvent;
}

declare module '@ali/aes-tracker-plugin-jserror' {
  const AESPluginJSError: any;
  export default AESPluginJSError;
}

declare module '@ali/aes-tracker-plugin-api' {
  const AESPluginAPI: any;
  export default AESPluginAPI;
}

declare module '@ali/aes-tracker-plugin-resourceError' {
  const AESPluginResourceError: any;
  export default AESPluginResourceError;
}

declare module '@ali/aes-tracker-plugin-perf' {
  const AESPluginPerf: any;
  export default AESPluginPerf;
}

declare module '@ali/aes-tracker-plugin-eventTiming' {
  const AESPluginEventTiming: any;
  export default AESPluginEventTiming;
}

declare module '@ali/aes-tracker-plugin-longtask' {
  const AESPluginLongTask: any;
  export default AESPluginLongTask;
}

declare module '@ali/aes-tracker-plugin-blank' {
  const AESPluginBlank: any;
  export default AESPluginBlank;
}
