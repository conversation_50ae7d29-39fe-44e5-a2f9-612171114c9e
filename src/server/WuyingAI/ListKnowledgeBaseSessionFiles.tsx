import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListKnowledgeBaseSessionFilesRequest {
  LoginToken?: string; // 登录凭证
  MaxResults?: number; // 每页显示最大结果数
  NextToken?: string; // 下一个查询开始的Token
  KbId?: string; // 知识库ID
  SessionId?: string; // 会话ID
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
}

export interface ListKnowledgeBaseSessionFilesResponse {
  TotalCount?: number; // 资源总数量
  RequestId?: string; // 请求ID
  Message?: string; // 返回消息
  NextToken?: string; // 下一个查询开始的Token
  Data: FileItem[]; // 知识库会话数据列表
  Code?: number; // 响应码
}

export interface FileItem {
  Status?: string; // 文件状态
  ArtifactId: string; // 数据唯一标识
  FileName: string; // 文件名称
  GmtModified: string; // 最后修改时间
  GmtCreated: string; // 创建时间
  FileSize: number; // 文件大小
}

// 获取会话文件列表（会话详情页中使用）
export const ListKnowledgeBaseSessionFiles = (params: ListKnowledgeBaseSessionFilesRequest) => {
  return request<ListKnowledgeBaseSessionFilesRequest, ListKnowledgeBaseSessionFilesResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListKnowledgeBaseSessionFiles',
    params,
  });
};
