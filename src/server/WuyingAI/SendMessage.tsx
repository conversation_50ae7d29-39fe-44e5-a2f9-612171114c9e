import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface SendMessageRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  SessionId?: string; // 会话ID，为空则后端会创建新会话
  Prompt: string; // 用户请求内容
  AgentId: string; // AgentId
  DesktopId?: string; // 桌面id 还没完全确定
  Resources?: ResourceItem[]; // 资源信息列表
}

export interface ResourceItem {
  Type?: string;
  ResourceId?: string;
}

export interface SendMessageResponse {
  Data: {
    SessionId: string;
    RoundId: string; // 轮次ID
  };
  Code: string;
  Message?: string;
}

export const SendMessage = (params: SendMessageRequest) => {
  return request<SendMessageRequest, SendMessageResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'SendMessage',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
