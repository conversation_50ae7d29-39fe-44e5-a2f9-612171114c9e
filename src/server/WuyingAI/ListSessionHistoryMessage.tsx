import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListSessionHistoryMessageRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  SessionId: string; // 会话ID
  NextToken?: string;
  KbId?: string;
  MaxResults?: number;
}

export interface ListSessionHistoryMessageResponse {
  Message: string;
  NextToken: string;
  Data: ListSessionHistoryMessageData;
  Code: number; // 整数类型
}

export interface ListSessionHistoryMessageData {
  Events: EventItem[];
}

export interface EventItem {
  Timestamp: number; // int64类型
  IsInKb: boolean;
  Role: string;
  Type: string;
  ToolName: string;
  Delta: string;
  ExtInfo: string;
  Feedback?: 'like' | 'dislike';
  Content: any;
  ToolCallId: string;
  EventId: string;
  RunId: string;
  SessionId: string;
  MessageId: string;
  Name?: string;
  FileType?: string;
  ArtifactId?: string;
  FileName?: string;
}

export const ListSessionHistoryMessage = (params: ListSessionHistoryMessageRequest) => {
  return request<ListSessionHistoryMessageRequest, ListSessionHistoryMessageResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListSessionHistoryMessage',
    params,
  });
};
