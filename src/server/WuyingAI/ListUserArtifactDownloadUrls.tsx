import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListUserArtifactDownloadUrlsRequest {
  LoginToken?: string;
  LoginSessionId?: string;
  ArtifactIds: string[];
  SessionId?: string;
  FileFormat?: string;
}

interface DownloadLink {
  ArtifactId: string;
  FileName: string;
  FileSize: number;
  FileType: string;
  DownloadUrl: string;
}

interface FailedFile {
  ArtifactId: string;
  FileName: string;
  Error: string;
}

export interface ListUserArtifactDownloadUrlsResponse {
  Code: string;
  Message: string;
  RequestId: string;
  Data: {
    DownloadLinks: DownloadLink[];
    FailedFiles: FailedFile[];
  };
}

// 获取文件下载链接
export const ListUserArtifactDownloadUrls = (params: ListUserArtifactDownloadUrlsRequest) => {
  return request<ListUserArtifactDownloadUrlsRequest, ListUserArtifactDownloadUrlsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListUserArtifactDownloadUrls',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
