import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListUserDesktopRequest {
  LoginToken?: string; //
  LoginSessionId?: string; //
  RegionId?: string; //
}

export interface Environment {
  DesktopId: string; // 桌面id
  DesktopStatus: string; //
  Name: string; // 环境名称
}

export interface ListUserDesktopResponse {
  Message: string; //
  Data: {
    Environments: Environment[]; // 执行环境列表
    Total: number; // 环境总数
  };
  Code: number; //
}

export const ListUserDesktop = (params: ListUserDesktopRequest) => {
  return request<ListUserDesktopRequest, ListUserDesktopResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListUserDesktop',
    params,
  });
};
