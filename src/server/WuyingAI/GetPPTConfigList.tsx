import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface GetPPTConfigListRequest {
  LoginToken: string; // 登录Token
  LoginSessionId: string; // 登录会话ID
  RegionId: string; // 地域ID
}

export interface ConfigOptions {
  Title: string; // 标题
  Items: string[]; // 项目列表
}

export interface GetPPTConfigListResponse {
  RequestId: string; // 请求ID
  Message: string; // 返回消息
  Data: {
    AudienceOptions: ConfigOptions; // 受众选项
    LanguageOptions: ConfigOptions; // 语言选项
    ScenarioOptions: ConfigOptions; // 场景选项
    PageRangeOptions: ConfigOptions; // 页面范围选项
    ToneOptions: ConfigOptions; // 语气选项
  };
}

export const GetPPTConfigList = () => {
  return request<GetPPTConfigListRequest, GetPPTConfigListResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'GetPPTConfigList',
  });
};
