import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListUserSessionArtifactsRequest {
  LoginToken?: string;
  RegionId?: string;
  NextToken?: string;
  MaxResults?: number;
  SessionId?: string;
  ArtifactTypes: Array<'artifactFile' | 'sessionFile'> | string[];
  KbId?: string;
}

export interface ArtifactItem {
  ArtifactId: string;
  ArtifactType: string;
  FileName: string;
  FileType: string;
  FileSize: number;
  GmtCreated: string;
  GmtModified: string;
  IsInKb: boolean;
}

export interface ListUserSessionArtifactsResponse {
  Code: string;
  Message: string;
  RequestId: string;
  TotalCount: number;
  Data: ArtifactItem[];
  NextToken: string;
}

// 获取用户当前会话的所有制品
export const ListUserSessionArtifacts = (params: ListUserSessionArtifactsRequest) => {
  return request<ListUserSessionArtifactsRequest, ListUserSessionArtifactsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListUserSessionArtifacts',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
