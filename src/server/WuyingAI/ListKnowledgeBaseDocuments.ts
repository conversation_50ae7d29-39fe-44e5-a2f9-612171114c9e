import request from '../request';
import { POP_PRODUCT } from '../popConfig';
import { KnowledgeBaseLogStatus } from './ListKnowledgeBaseLogs';

export interface ListKnowledgeBaseDocumentsRequest {
  LoginToken?: string;
  RegionId?: string;
  MaxResults?: number;
  NextToken?: string;
  KbId?: string;
  FileNameList?: string[]; // 文件名称列表，用来查询是否有同名文件
}

export interface ListKnowledgeBaseDocumentsResponse {
  TotalCount: number; // 总数量
  RequestId: string; // 请求ID
  Message: string;
  NextToken: string;
  Data: DocumentItem[]; // 数据
  Code: string; // 响应码
}

export interface DocumentItem {
  DocumentId: string; // 文档ID
  FileName: string; // 文件名称
  FileSize: string; // 文件大小
  Status: KnowledgeBaseLogStatus; // 状态
  GmtModified: string; // 修改时间
  GmtCreated: string; // 创建时间
  IsAiGenerated?: boolean; // 是否是AI生成 TODO: 待确认
}

// 获取知识库文档列表
export const ListKnowledgeBaseDocuments = (params: ListKnowledgeBaseDocumentsRequest) => {
  return request<ListKnowledgeBaseDocumentsRequest, ListKnowledgeBaseDocumentsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListKnowledgeBaseDocuments',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
