import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DeleteKnowledgeBaseSessionsRequest {
  LoginToken?: string; // 登录凭证
  KbId: string; // 知识库ID
  SessionIdList: string[]; // 会话ID列表
  RegionId?: string; // 地域ID
  LoginSessionId?: string; // 登录会话ID
}

export interface DeleteKnowledgeBaseSessionsResponse {
  RequestId: string; // 请求ID
  Message: string; // 响应消息
  Code: string; // 响应码
}

export const DeleteKnowledgeBaseSessions = (params: DeleteKnowledgeBaseSessionsRequest) => {
  return request<DeleteKnowledgeBaseSessionsRequest, DeleteKnowledgeBaseSessionsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DeleteKnowledgeBaseSessions',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
