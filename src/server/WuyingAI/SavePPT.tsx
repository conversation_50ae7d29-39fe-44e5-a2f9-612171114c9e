import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface SavePPTRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
  FileId: string; // 文件ID
  ThumbnailUrl: string; // 缩略图URL
}

export interface SavePPTResponse {
  RequestId: string; // 请求ID
  Message: string; // 返回消息
  Code: number; // 状态码
}

export const SavePPT = (params: SavePPTRequest) => {
  return request<SavePPTRequest, SavePPTResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'SavePPT',
    params,
  });
};
