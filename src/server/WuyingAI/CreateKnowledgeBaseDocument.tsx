import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface CreateKnowledgeBaseDocumentRequest {
  KbId: string; // 知识库ID
  DocumentList: DocumentListItem[]; // 文档列表
}

export interface CreateKnowledgeBaseDocumentResponse {
  RequestId: string; // 请求ID
  Message: string; // 响应消息
  Code: string; // 响应码
}

export interface DocumentListItem {
  FileId: string; // 文件ID
  FileName: string; // 文件名
}

export const CreateKnowledgeBaseDocument = (params: CreateKnowledgeBaseDocumentRequest) => {
  return request<CreateKnowledgeBaseDocumentRequest, CreateKnowledgeBaseDocumentResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'CreateKnowledgeBaseDocument',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
