import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListKnowledgeBasesRequest {
  LoginToken?: string; // 登录凭证
  MaxResults?: string; // 最大结果数
  NextToken?: string; // 查询Token
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
}

export interface ListKnowledgeBasesResponse {
  TotalCount: number; // 资源总数量
  RequestId: string; // 请求ID
  Message: string; // 消息
  NextToken: string; // 下一个查询开始的Token
  Data: ListKnowledgeBaseData[]; // 数据
  Code: number; // 状态码
}

export interface ListKnowledgeBaseData {
  DocumentCount: number; // 文档数量
  KbId: string; // 知识库ID
  Description: string; // 描述
  GmtModified: string; // 修改时间
  GmtCreated: string; // 创建时间
  SessionCount: number; // 会话数量
  Name: string; // 名称
}

export const ListKnowledgeBases = (params: ListKnowledgeBasesRequest) => {
  return request<ListKnowledgeBasesRequest, ListKnowledgeBasesResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListKnowledgeBases',
    params,
  });
};
