import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListKnowledgeBaseLogsRequest {
  LoginToken?: string;
  RegionId?: string;
  MaxResults?: number;
  NextToken?: string;
  KbId?: string;
}

export interface ListKnowledgeBaseLogsResponse {
  TotalCount: number; // 总数量
  RequestId: string; // 请求ID
  Message: string;
  NextToken: string;
  Data: DataItem[]; // 数据
  Code: number; // 响应码
}

export enum KnowledgeBaseLogStatus {
  Processing = 'processing',
  Success = 'success',
  Failed = 'failed',
  Deleted = 'deleted',
}

export enum KnowledgeBaseTargetType {
  document = 'document',
  session = 'session',
}

export enum KnowledgeBaseLogType {
  delete = 'delete',
  create = 'create',
}

export interface DataItem {
  Status: KnowledgeBaseLogStatus; // 状态
  Type: KnowledgeBaseLogType; // 类型
  KbName: string; // 知识库名称
  GmtModified: string; // 修改时间
  Title: string; // 标题
  GmtCreated: string; // 创建时间
  TargetType: KnowledgeBaseTargetType; // 目标类型
}

export const ListKnowledgeBaseLogs = (params: ListKnowledgeBaseLogsRequest) => {
  return request<ListKnowledgeBaseLogsRequest, ListKnowledgeBaseLogsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListKnowledgeBaseLogs',
    params,
  });
};
