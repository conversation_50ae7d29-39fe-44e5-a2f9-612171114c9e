import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface BindPPTToSessionRequest {
  SessionId: string; // Session ID
  EventId: string; // Event ID
  PptId: string; // PPT ID
  LoginToken?: string; // 登录Token
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
}

export interface BindPPTToSessionResponse {
  RequestId: string; // Id of the request
  Message: string; // 响应消息
  Code: number; // 响应码
  Data: string; // 制品id
}

export const BindPPTToSession = (params: BindPPTToSessionRequest) => {
  return request<BindPPTToSessionRequest, BindPPTToSessionResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'BindPPTToSession',
    params,
  });
};
