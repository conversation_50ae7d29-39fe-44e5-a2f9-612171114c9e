import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface GetPPTThumbnailRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 区域ID
  PptId: string; // 文件ID
}

export interface GetPPTThumbnailResponse {
  RequestId: string; // 请求的ID
  Message: string; // 响应消息
  Data: string; // 数据内容
  Code: number; // 响应码
}

export const GetPPTThumbnail = (params: GetPPTThumbnailRequest) => {
  return request<GetPPTThumbnailRequest, GetPPTThumbnailResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'GetPPTThumbnail',
    params,
  });
};
