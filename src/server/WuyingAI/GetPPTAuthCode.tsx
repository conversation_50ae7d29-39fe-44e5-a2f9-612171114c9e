import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface GetPPTAuthCodeRequest {
  // LoginToken?: string; // 登录令牌
  // LoginSessionId?: string; // 登录会话ID
  // RegionId?: string; // 区域ID
}

export interface GetPPTAuthCodeResponse {
  RequestId: string; // 请求的唯一标识
  Message: string; // 响应消息
  Data: AuthCodeData; // 响应数据
  Code: number; // 响应码
  ApiKey: string; // 接口key
  Channel: string; // 渠道
}

export interface AuthCodeData {
  ApiKey: string; // 接口key
  Channel: string; // 渠道
  TimeExpire: string; // 过期时间
  Code: string; // 验证码
}

export const GetPPTAuthCode = (params: GetPPTAuthCodeRequest = {}) => {
  return request<GetPPTAuthCodeRequest, GetPPTAuthCodeResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'GetPPTAuthCode',
    params,
  });
};
