import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface CreateUserFeedbackRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 区域ID
  IssueDetail: string; // 问题详情
  ContactInfo: string; // 联系信息
}

export interface CreateUserFeedbackResponse {
  RequestId: string; // 请求的ID
  Message: string; // 响应消息
  Code: number; // 响应状态码
}

export const CreateUserFeedback = (params: CreateUserFeedbackRequest) => {
  return request<CreateUserFeedbackRequest, CreateUserFeedbackResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'CreateUserFeedback',
    params,
  });
};
