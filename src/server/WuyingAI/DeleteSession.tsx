import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DeleteSessionRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  SessionId?: string; // 会话ID
}

export interface DeleteSessionResponse {
  // 假设响应中可能包含操作结果状态
  success: boolean; // 操作是否成功
  message: string; // 响应消息
  requestId?: string; // 请求唯一标识
}

export const DeleteSession = (params: DeleteSessionRequest) => {
  return request<DeleteSessionRequest, DeleteSessionResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DeleteSession',
    params,
  });
};
