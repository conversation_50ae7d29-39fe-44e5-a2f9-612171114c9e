import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DescribeUserSettingRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
}

export interface AvailableModel {
  Name: string; // 模型名称
}

export interface CurrentSetting {
  Model: string; // 当前模型选择
  DesktopId: string; // 当前运行环境偏好
}

export interface AvailableEnvironment {
  DesktopId: string; // 桌面ID
  DesktopStatus: string;
  Name: string; // 环境名称
}

export interface DescribeUserSettingResponse {
  Msg: string;
  Data: {
    AvailableModels: string[]; // 可用模型列表
    CurrentSettings: CurrentSetting; // 当前用户设置
    AvailableEnvironments: AvailableEnvironment[]; // 可用运行环境列表
  };
  Code: number; // 200成功，其他失败
}

export const DescribeUserSetting = (params: DescribeUserSettingRequest) => {
  return request<DescribeUserSettingRequest, DescribeUserSettingResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DescribeUserSetting',
    params,
  });
};
