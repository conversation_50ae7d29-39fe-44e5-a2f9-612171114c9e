import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface IsArtifactExistedRequest {
  LoginToken?: string; // 登录凭证
  RegionId?: string; // 区域ID
  LoginSessionId?: string; // 登录会话ID
  ArtifactId: string; // 制品ID
}

export interface IsArtifactExistedData {
  ArtifactId: string; // 制品ID
  Exists: boolean; // 是否存在
}

export interface IsArtifactExistedResponse {
  RequestId: string; // 请求ID
  Message: string; // 返回信息
  Data: IsArtifactExistedData; // 返回数据
  Code: string; // 状态码
}

export const IsArtifactExisted = (params: IsArtifactExistedRequest) => {
  return request<IsArtifactExistedRequest, IsArtifactExistedResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'IsArtifactExisted',
    params,
  });
};
