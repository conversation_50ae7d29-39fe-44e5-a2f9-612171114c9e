import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface CancelSessionRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
  SessionId: string; // 会话ID
}

export interface CancelSessionResponse {

}

export const CancelSession = (params: CancelSessionRequest) => {
  return request<CancelSessionRequest, CancelSessionResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'CancelSession',
    params,
  });
};
