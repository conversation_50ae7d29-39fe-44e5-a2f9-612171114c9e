import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ConfirmPresignedUploadRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  FileId: string; // 文件ID
}

export interface ConfirmPresignedUploadResponse {
  // 这里没有定义具体返回字段，如果有具体字段可以添加
  Message: string;
  Data: {
    FileId: string;
    Message: string;
    Status: string;
    SessionId: string;
  };
  Code: any;
}

export const ConfirmPresignedUpload = (params: ConfirmPresignedUploadRequest) => {
  return request<ConfirmPresignedUploadRequest, ConfirmPresignedUploadResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ConfirmPresignedUpload',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
