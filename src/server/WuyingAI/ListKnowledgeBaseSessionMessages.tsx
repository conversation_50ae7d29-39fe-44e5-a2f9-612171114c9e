import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListKnowledgeBaseSessionMessagesRequest {
  LoginToken?: string; // Id of the request
  MaxResults?: number;
  NextToken?: string;
  KbId: string;
  SessionId: string;
  IsAll?: boolean;
  LoginSessionId?: string;
  RegionId?: string;
}

export interface ListKnowledgeBaseSessionMessagesResponse {
  TotalCount: number; // Id of the request
  RequestId: string; // Id of the request
  Message: string;
  NextToken: string;
  Data: MessageData[]; // Data
  Code: number;
}

export interface MessageData {
  MessageContent: string; // MessageContent
  IsAdded: boolean; // IsAdded
  MessageRole: string; // MessageRole
  GmtModified: string; // GmtModified
  GmtCreated: string; // GmtCreated
  MessageId: string; // MessageId
}

// 获取会话内容列表（会话详情页中使用）
export const ListKnowledgeBaseSessionMessages = (
  params: ListKnowledgeBaseSessionMessagesRequest,
) => {
  return request<ListKnowledgeBaseSessionMessagesRequest, ListKnowledgeBaseSessionMessagesResponse>(
    {
      product: POP_PRODUCT.WuyingAI,
      action: 'ListKnowledgeBaseSessionMessages',
      params,
    },
  );
};
