import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DeleteUserArtifactsRequest {
  LoginToken?: string;
  RegionId?: string;
  ArtifactIds: string[];
  SessionId?: string;
}

interface DeletedFile {
  ArtifactId: string;
  FileName: string;
}

interface FailedFile {
  ArtifactId: string;
  FileName: string;
  Error: string;
}

export interface DeleteUserArtifactsResponse {
  Code: string;
  Message: string;
  RequestId: string;
  Data: {
    DeleteFiles: DeletedFile[];
    FailedFiles: FailedFile[];
  };
}

// 删除制品
export const DeleteUserArtifacts = (params: DeleteUserArtifactsRequest) => {
  return request<DeleteUserArtifactsRequest, DeleteUserArtifactsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DeleteUserArtifacts',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
