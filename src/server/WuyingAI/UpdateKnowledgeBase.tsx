import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface UpdateKnowledgeBaseRequest {
  LoginToken?: string; // 登录Token
  KbId: string; // 知识库ID
  Name: string; // 名称
  Description: string; // 描述
  RegionId?: string; // 地域ID
  LoginSessionId?: string; // 登录会话ID
}

export interface UpdateKnowledgeBaseResponse {
  RequestId: string; // 请求ID
  Message: string; // 返回消息
  Code: string; // 状态码
}

export const UpdateKnowledgeBase = (params: UpdateKnowledgeBaseRequest) => {
  return request<UpdateKnowledgeBaseRequest, UpdateKnowledgeBaseResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'UpdateKnowledgeBase',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};

export const ERROR_CODE_UPDATE_KNOWLEDGE_BASE = {
  OBJECT_DUPLICATE: '已存在同名知识库，请更换名称',
};
