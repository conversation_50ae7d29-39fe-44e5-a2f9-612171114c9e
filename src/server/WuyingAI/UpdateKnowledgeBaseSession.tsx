import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface UpdateKnowledgeBaseSessionRequest {
  LoginToken?: string;
  LoginSessionId?: string;
  RegionId?: string;
  KbId: string;
  SessionId: string;
  FileIdList?: string[];
  MessageIdList?: string[];
  FileIgnore?: boolean; // 为true，表示忽略不做更新（无论是否有数据传入）
  MessageIgnore?: boolean; // 为true，表示忽略不做更新（无论是否有数据传入）
}

export interface UpdateKnowledgeBaseSessionResponse {
  RequestId: string;
  Message: string;
  Code: string;
}

export const UpdateKnowledgeBaseSession = (params: UpdateKnowledgeBaseSessionRequest) => {
  return request<UpdateKnowledgeBaseSessionRequest, UpdateKnowledgeBaseSessionResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'UpdateKnowledgeBaseSession',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
