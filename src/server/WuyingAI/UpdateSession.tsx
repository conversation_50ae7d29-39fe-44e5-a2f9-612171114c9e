import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface UpdateSessionRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  SessionId: string; // 会话ID
  NewTitle: string; // 新的会话标题
}

export interface UpdateSessionResponse {
  // 无特殊返回参数，保留空对象接口
}

export const UpdateSession = (params: UpdateSessionRequest) => {
  return request<UpdateSessionRequest, UpdateSessionResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'UpdateSession',
    params,
  });
};
