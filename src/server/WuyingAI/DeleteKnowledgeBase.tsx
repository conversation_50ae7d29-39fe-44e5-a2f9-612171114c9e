import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DeleteKnowledgeBaseRequest {
  KbId: string; // 知识库ID
}

export interface DeleteKnowledgeBaseResponse {
  RequestId: string; // 请求ID
  Message: string; // 响应消息
  Code: number; // 响应码
}

export const DeleteKnowledgeBase = (params: DeleteKnowledgeBaseRequest) => {
  return request<DeleteKnowledgeBaseRequest, DeleteKnowledgeBaseResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DeleteKnowledgeBase',
    params,
  });
};
