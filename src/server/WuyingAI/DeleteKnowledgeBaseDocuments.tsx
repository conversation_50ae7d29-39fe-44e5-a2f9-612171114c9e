import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DeleteKnowledgeBaseDocumentRequest {
  KbId: string; // 知识库ID
  FileNameList: string[]; // 文件名称列表
}

export interface DeleteKnowledgeBaseDocumentResponse {
  RequestId: string; // 请求ID
  Message: string; // 响应消息
  Code: number; // 响应码
}

export const DeleteKnowledgeBaseDocuments = (params: DeleteKnowledgeBaseDocumentRequest) => {
  return request<DeleteKnowledgeBaseDocumentRequest, DeleteKnowledgeBaseDocumentResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DeleteKnowledgeBaseDocuments',
    params,
  });
};
