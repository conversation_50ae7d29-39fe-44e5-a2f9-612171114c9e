import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface UpdateUserSettingRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  DesktopId?: string; // 运行环境偏好，传递desktop_id
  Model?: string; // 模型选择，传递model名称
}

export interface UpdateUserSettingResponseData {
  Model: string; // 模型选择
  DesktopId: string; // 桌面ID
}

export interface UpdateUserSettingResponse {
  Msg: string; // 响应消息
  Data: UpdateUserSettingResponseData; // 响应数据
  Code: number; // 状态码
}

export const UpdateUserSetting = (params: UpdateUserSettingRequest) => {
  return request<UpdateUserSettingRequest, UpdateUserSettingResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'UpdateUserSetting',
    params,
  });
};
