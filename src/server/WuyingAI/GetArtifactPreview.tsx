import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface GetArtifactPreviewRequest {
  LoginToken?: string; // 登录凭证
  RegionId?: string; // 地域ID
  ArtifactId: string; // 产物ID
  LoginSessionId?: string; // 登录会话ID
  SessionId?: string;
}

export interface GetArtifactPreviewResponse {
  // 无具体返回字段定义，根据实际情况补充
  Message: string;
  Data: {
    ArtifactId: string;
    FileName?: string;
    Url?: string;
  };
}

export const GetArtifactPreview = (params: GetArtifactPreviewRequest) => {
  return request<GetArtifactPreviewRequest, GetArtifactPreviewResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'GetArtifactPreview',
    params,
  });
};
