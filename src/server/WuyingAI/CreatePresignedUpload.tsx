import request from '../request';
import { POP_PRODUCT } from '../popConfig';
import { AgentIdEnum } from '@/constant/enum';

export interface CreatePresignedUploadRequest {
  AgentId: AgentIdEnum;
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  SessionId?: string; // 会话ID，为空则创建新会话
  FileInfo: {
    FileType: string;
    FileName: string;
    FileSize: number; // 文件大小
  }; // 文件信息
}

export interface CreatePresignedUploadResponse {
  Data: {
    FileId: string; // 文件ID
    SessionId: string;
    UploadUrl: string; // 上传时间
    Headers: Record<string, any>;
  };
  Code: any;
  Message: string;
}

export const CreatePresignedUpload = (params: CreatePresignedUploadRequest) => {
  return request<CreatePresignedUploadRequest, CreatePresignedUploadResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'CreatePresignedUpload',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
