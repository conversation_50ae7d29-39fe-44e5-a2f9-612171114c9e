import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface CreateKnowledgeBaseRequest {
  Name: string; // 名称
  Description?: string; // 描述
}

export interface CreateKnowledgeBaseResponse {
  RequestId: string; // 请求ID
  Message: string; // 响应消息
  Code: string; // 响应码
  Data: {
    KbId: string; // 知识库ID
  };
}

export const CreateKnowledgeBase = (params: CreateKnowledgeBaseRequest) => {
  return request<CreateKnowledgeBaseRequest, CreateKnowledgeBaseResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'CreateKnowledgeBase',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};

export const ERROR_CODE_CREATE_KNOWLEDGE_BASE = {
  OBJECT_ALREADY_EXISTS: '已存在同名知识库，请更换名称',
};
