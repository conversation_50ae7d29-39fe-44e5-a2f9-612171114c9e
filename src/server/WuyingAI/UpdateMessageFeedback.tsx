import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface UpdateMessageFeedbackRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  SessionId: string; // 会话ID
  MessageId: string; // 消息id
  Feedback: 'like' | 'dislike' | null; // 反馈信息: like, dislike, 或 null 表示取消反馈
}

export interface UpdateMessageFeedbackResponse {
  Message: string; // 响应消息
  RequestId: string; // 请求ID
  Data: ResponseData; // 响应数据对象
  Code: number; // 状态码
}

export interface ResponseData {
  // 可以根据实际返回结构调整此类型
  [key: string]: any;
}

export const UpdateMessageFeedback = (params: UpdateMessageFeedbackRequest) => {
  return request<UpdateMessageFeedbackRequest, UpdateMessageFeedbackResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'UpdateMessageFeedback',
    params,
  });
};
