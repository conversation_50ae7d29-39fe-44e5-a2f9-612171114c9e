import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DescribeKnowledgeBaseDocumentRequest {
  LoginToken?: string; // 登录令牌
  KbId: string; // 知识库ID
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
}

export interface DescribeKnowledgeBaseDocumentResponse {
  TotalCount: number; // 资源总数量
  RequestId: string; // Id of the request
  Message: string; // 返回消息
  NextToken: string; // 下一个查询开始的Token
  Data: DescribeKnowledgeBaseDocumentData; // 返回数据
  Code: number; // 响应码
}

export interface DescribeKnowledgeBaseDocumentData {
  DocumentCount: number; // 文档数量
  KbId: string; // 知识库ID
  Description: string; // 描述
  GmtModified: string; // 最后修改时间
  GmtCreated: string; // 创建时间
  SessionCount: number; // 会话数量
  Name: string; // 名称
}

export const DescribeKnowledgeBaseDocument = (params: DescribeKnowledgeBaseDocumentRequest) => {
  return request<DescribeKnowledgeBaseDocumentRequest, DescribeKnowledgeBaseDocumentResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DescribeKnowledgeBaseDocument',
    params,
  });
};
