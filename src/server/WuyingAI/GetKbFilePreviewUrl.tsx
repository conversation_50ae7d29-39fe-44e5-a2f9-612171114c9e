import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface GetKbFilePreviewUrlRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 区域ID
  FileId: string; // 文件ID
}

export interface GetKbFilePreviewUrlResponseData {
  PreviewUrl: string; // 预览URL
  ExpiresIn: string; // 过期时间
}

export interface GetKbFilePreviewUrlResponse {
  Message: string; // 返回消息
  RequestId: string; // 请求ID
  Data: GetKbFilePreviewUrlResponseData; // 响应数据
  Code: number; // 状态码
}

export const GetKbFilePreviewUrl = (params: GetKbFilePreviewUrlRequest) => {
  return request<GetKbFilePreviewUrlRequest, GetKbFilePreviewUrlResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'GetKbFilePreviewUrl',
    params,
  });
};
