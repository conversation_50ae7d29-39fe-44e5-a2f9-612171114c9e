import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListUserSessionKbMessageRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
  SessionId?: string; // 会话ID
  KbId?: string; // 知识库ID
}

export interface ListUserSessionKbMessageResponse {
  Message: string; // 返回消息
  RequestId: string; // 请求ID
  Data: ListUserSessionKbMessageData; // 响应数据
  Code: number; // 响应码
}

export interface ListUserSessionKbMessageData {
  MessageIds: string[]; // 消息ID列表
}

export const ListUserSessionKbMessage = (params: ListUserSessionKbMessageRequest) => {
  return request<ListUserSessionKbMessageRequest, ListUserSessionKbMessageResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListUserSessionKbMessage',
    params,
  });
};
