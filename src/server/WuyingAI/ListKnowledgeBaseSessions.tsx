import request from '../request';
import { POP_PRODUCT } from '../popConfig';
import { KnowledgeBaseLogStatus } from './ListKnowledgeBaseLogs';

export interface ListKnowledgeBaseSessionsRequest {
  LoginToken?: string; // 登录令牌
  RegionId?: string; // 地域ID
  LoginSessionId?: string; // 登录会话ID
  MaxResults?: number; // 最大结果数
  NextToken?: string; // 下一个查询的令牌
  KbId?: string; // 知识库ID
}

export interface ListKnowledgeBaseSessionsResponse {
  TotalCount: number; // 总数量
  RequestId: string; // 请求ID
  Message: string; // 响应消息
  NextToken: string; // 下一个查询的令牌
  Data: SessionItem[]; // 会话数据列表
  Code: number; // 响应状态码
}

export interface SessionItem {
  Status: KnowledgeBaseLogStatus; // 会话状态
  GmtModified: string; // 最后修改时间
  GmtCreated: string; // 创建时间
  SessionId: string; // 会话ID
  SessionName: string; // 会话名称
  FileSize: number; // 文件大小
}

export const ListKnowledgeBaseSessions = (params: ListKnowledgeBaseSessionsRequest) => {
  return request<ListKnowledgeBaseSessionsRequest, ListKnowledgeBaseSessionsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListKnowledgeBaseSessions',
    params,
  });
};
