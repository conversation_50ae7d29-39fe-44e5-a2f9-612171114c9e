import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DescribeKnowledgeBaseRequest {
  LoginToken?: string; // 登录凭证
  KbId?: string; // 知识库ID
  RegionId?: string; // 地域ID
  LoginSessionId?: string; // 登录会话ID
}

export interface DescribeKnowledgeBaseData {
  DocumentCount: number; // 文档数量
  KbId: string; // 知识库ID
  Description: string; // 描述
  GmtModified: string; // 最后修改时间
  GmtCreated: string; // 创建时间
  SessionCount: number; // 会话数量
  Name: string; // 名称
}

export interface DescribeKnowledgeBaseResponse {
  TotalCount: number; // 总数量
  RequestId: string; // 请求ID
  Message: string; // 消息
  NextToken: string; // 下一个查询开始的Token
  Data: DescribeKnowledgeBaseData; // 返回数据
  Code: number; // 状态码
}

export const DescribeKnowledgeBase = (params: DescribeKnowledgeBaseRequest) => {
  return request<DescribeKnowledgeBaseRequest, DescribeKnowledgeBaseResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DescribeKnowledgeBase',
    params,
  });
};
