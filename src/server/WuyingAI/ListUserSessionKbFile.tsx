import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface ListUserSessionKbFileRequest {
  LoginToken?: string;
  LoginSessionId?: string;
  RegionId?: string;
  SessionId: string;
  KbId: string;
}

export interface ListUserSessionKbFileResponse {
  Message: string;
  RequestId: string;
  Data: {
    FileIds: string[];
  };
  Code: number;
}

export const ListUserSessionKbFile = (params: ListUserSessionKbFileRequest) => {
  return request<ListUserSessionKbFileRequest, ListUserSessionKbFileResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListUserSessionKbFile',
    params,
  });
};
