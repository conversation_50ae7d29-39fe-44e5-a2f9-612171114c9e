import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface DownloadPPTRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
  PptId: string; // 作品ID
  DownloadFormat: string; // 下载格式
}

export interface DownloadPPTResponse {
  RequestId: string; // Id of the request
  Message: string;
  Data: string; // 作品下载url
  Code: number; // 状态码
}

export const DownloadPPT = (params: DownloadPPTRequest) => {
  return request<DownloadPPTRequest, DownloadPPTResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'DownloadPPT',
    params,
  });
};
