import request from '../request';
import { POP_PRODUCT } from '../popConfig';
import { AgentIdEnum } from '@/constant/enum';

export interface ListUserSessionsRequest {
  LoginToken?: string; // 登录令牌
  LoginSessionId?: string; // 会话ID
  RegionId?: string; // 区域ID
  MaxResults?: number; // 每页数量，默认20，最大100
  NextToken?: string; // 分页标记
  AgentId?: AgentIdEnum;
  KbId?: string;
  Keyword?: string;
}

export interface Session {
  GmtCreate: string;
  GmtModified: string;
  Title: string;
  AgentId: AgentIdEnum;
  SessionId: string;
  IsInKb: boolean;
}

export interface ListUserSessionsResponse {
  RequestId: string;
  Msg: string;
  NextToken?: string;
  TotalCount: number;
  Data: {
    Sessions: Session[];
  };
  Code: number;
}

// 查询用户会话列表（在知识库添加会话时可供选择的会话）
export const ListUserSessions = (params: ListUserSessionsRequest) => {
  return request<ListUserSessionsRequest, ListUserSessionsResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'ListUserSessions',
    params,
  });
};
