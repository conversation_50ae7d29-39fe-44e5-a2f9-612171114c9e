import request from '../request';
import { POP_PRODUCT } from '../popConfig';

export interface CreateKnowledgeBaseSessionRequest {
  LoginToken?: string; // Id of the request
  KbId?: string;
  SessionId?: string;
  MessageIdList?: string[]; // Id of the request
  FileIdList?: string[]; // Id of the request
  LoginSessionId?: string; // Id of the request
  RegionId?: string; // Id of the request
}

export interface CreateKnowledgeBaseSessionResponse {
  RequestId?: string; // Id of the request
  Code?: string;
  Message?: string;
}

export const CreateKnowledgeBaseSession = (params: CreateKnowledgeBaseSessionRequest) => {
  return request<CreateKnowledgeBaseSessionRequest, CreateKnowledgeBaseSessionResponse>({
    product: POP_PRODUCT.WuyingAI,
    action: 'CreateKnowledgeBaseSession',
    params,
    errorOptions: {
      showErrorModal: false,
      throwError: false,
    },
  });
};
