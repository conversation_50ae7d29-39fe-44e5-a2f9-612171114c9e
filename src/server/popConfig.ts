/**
 * 预发其余环境host参考，可以不配置
 * https://aliyuque.antfin.com/wuying/guifan/xnytxc#e2pTv
 */

import { env } from '@/utils/env';

export enum POP_PRODUCT {
  WuyingAI = 'WuyingAI',
  AppstreamCenter = 'appstream-center',
  ecd = 'ecd',
  ecd20250730 = 'ecd20250730',
}

export const POP_CONFIG = {
  [POP_PRODUCT.WuyingAI]: {
    version: '2025-07-17',
    product: 'WuyingAI',
    getEndpoint(action?: string) {
      const endPointMap = {
        online: `https://wuyingai.cn-hangzhou.aliyuncs.com/${action}`,
        pre: `https://wuyingai-pre.cn-hangzhou.aliyuncs.com/${action}`,
      };
      // 在开发环境下使用本地代理
      if (process.env.NODE_ENV === 'development' && process.env.MOCK === 'true') {
        return `/api/WuyingAI/${action}`;
      }
      return endPointMap[env] ?? endPointMap.online;
    },
  },
  [POP_PRODUCT.AppstreamCenter]: {
    version: '2025-07-17',
    product: POP_PRODUCT.AppstreamCenter,
    getEndpoint() {
      const endPointMap = {
        online: 'https://appstream-center.cn-shanghai.aliyuncs.com',
        pre: 'https://appstream-center-pre.aliyuncs.com',
      };
      return endPointMap[env] ?? endPointMap.online;
    },
  },
  [POP_PRODUCT.ecd]: {
    version: '2020-10-02',
    product: POP_PRODUCT.ecd,
    getEndpoint(action?: string) {
      const endPointMap = {
        online: `https://ecd.cn-shanghai.aliyuncs.com/${action}`,
        pre: `https://ecd-pre.cn-hangzhou.aliyuncs.com/${action}`,
      };
      // 在开发环境下使用本地代理
      if (process.env.NODE_ENV === 'development' && process.env.MOCK === 'true') {
        return `/api/ecd/${action}`;
      }
      return endPointMap[env] ?? endPointMap.online;
    },
  },
  [POP_PRODUCT.ecd20250730]: {
    version: '2025-07-30',
    product: POP_PRODUCT.ecd20250730,
    getEndpoint(action?: string) {
      const endPointMap = {
        online: `https://ecd.cn-shanghai.aliyuncs.com/${action}`,
        pre: `https://ecd-pre.cn-hangzhou.aliyuncs.com/${action}`,
      };
      // 在开发环境下使用本地代理
      if (process.env.NODE_ENV === 'development' && process.env.MOCK === 'true') {
        return `/api/ecd/${action}`;
      }
      return endPointMap[env] ?? endPointMap.online;
    },
  },
};
