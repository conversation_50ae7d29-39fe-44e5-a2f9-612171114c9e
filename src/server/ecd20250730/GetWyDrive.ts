import request from '../request';
import { POP_PRODUCT } from '../popConfig';
import { getOrCreateClientId } from '../../utils/common';

export interface GetWyDriveRequest {
  LoginToken?: string; // 登录凭证
  SessionId?: string; // 会话ID
  ClientId?: string; // 客户端ID
  RegionId?: string; // 地域ID
  ProductType?: string; // 产品类型
  WithToken?: boolean; // 是否携带token
}

export interface GetWyDriveResponse {
  WyDrive: {
    WyDriveId: string; // 无影同步盘Id
    SupportWyAi: boolean; // 是否开通无影AI服务
  };
}

export const GetWyDrive = (params: GetWyDriveRequest) => {
  return request<GetWyDriveRequest, GetWyDriveResponse>({
    product: POP_PRODUCT.ecd20250730,
    action: 'GetWyDrive',
    params: {
      ...params,
      ClientId: getOrCreateClientId(),
      WithToken: false,
      ProductType: 'CloudDesktop',
    },
    errorOptions: {
      showErrorModal: false,
    },
  });
};
