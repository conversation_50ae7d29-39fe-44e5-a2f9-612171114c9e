import { message } from 'antd';
export interface FileItem {
  name: string;
  size: number;
  status:
    | 'ready'
    | 'uploading'
    | 'done'
    | 'error'
    | 'cancelled'
    | 'parsing'
    | 'completed'
    | 'failed';
  progress: number;
  file: File;
  fileId: string;
}

export interface UploadResult {
  url: string;
  fileId: string;
  ossKey: string;
  uploadTime: string;
}

import {
  CreatePresignedUploadRequest,
  CreatePresignedUpload,
} from '@/server/WuyingAI/CreatePresignedUpload';
import {
  ConfirmPresignedUploadRequest,
  ConfirmPresignedUpload,
} from '@/server/WuyingAI/ConfirmPresignedUpload';
import axios from 'axios';

export class UploadService {
  /**
   * 创建预签名上传链接
   */
  static async createPresignedUpload(params: CreatePresignedUploadRequest) {
    const res = await CreatePresignedUpload({
      ...params,
    });
    if (res?.Code !== '200') {
      return res;
    } else {
      const { Data } = res;
      return {
        uploadUrl: Data.UploadUrl,
        fileId: Data.FileId,
        sessionId: Data.SessionId,
        headers: Data.Headers,
      };
    }
  }

  static async uploadToOSS(uploadUrl: string, headers: Record<string, any>, file: File, onProgress: (progress: number) => void) {
    if (!uploadUrl) return Promise.reject('uploadUrl is empty');
    const response = await axios.put(uploadUrl.replace(/^http:/, 'https:'), file, {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((100.0 * progressEvent.loaded) / progressEvent.total);
        onProgress && onProgress(progress);
      },
      headers,
    });
    return response;
  }

  /**
   * 确认预签名上传
   */
  static async confirmPresignedUpload(params: ConfirmPresignedUploadRequest) {
    if (!params.FileId) return Promise.reject('FileId is empty');
    try {
      const response = await ConfirmPresignedUpload({
        ...params,
      });
      return response;
    } catch (error) {
      console.error('Confirm presigned upload error:', error);
      throw error;
    }
  }
}
