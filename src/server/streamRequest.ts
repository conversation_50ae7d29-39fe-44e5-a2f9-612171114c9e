/**
 * @see https://github.com/ant-design/x/blob/main/components/x-request/index.ts
 */

import { XStream } from '@ant-design/x';
import type { XStreamOptions } from '@ant-design/x';

import xFetch from './xFetch';
import type { XFetchOptions } from './xFetch';

import { paramStringify, getCurrentTimer, generateSignatureNonce } from '@/utils';

export type AnyObject = Record<PropertyKey, any>;

export type SSEFields = 'data' | 'event' | 'id' | 'retry';

export type SSEOutput = Partial<Record<SSEFields, any>>;

export interface StreamRequestBaseOptions {
  /**
   * @description Base URL, e.g., 'https://api.example.com/v1/chat'
   */
  baseURL: string;
}

interface StreamRequestCustomOptions {
  /**
   * @description Custom fetch
   */
  fetch?: XFetchOptions['fetch'];
}

export type StreamRequestOptions = StreamRequestBaseOptions & StreamRequestCustomOptions;

type StreamRequestMessageContent = string | AnyObject;

interface StreamRequestMessage extends AnyObject {
  role?: string;
  content?: StreamRequestMessageContent;
}

/**
 * Compatible with the parameters of OpenAI's chat.completions.create,
 * with plans to support more parameters and adapters in the future
 */
export interface StreamRequestParams {
  /**
   * @description Indicates whether to use streaming for the response
   */
  stream?: boolean;

  /**
   * @description The messages to be sent to the model
   */
  messages?: StreamRequestMessage[];
}

export interface StreamRequestCallbacks<Output> {
  /**
   * @description Callback when the request is successful
   */
  onSuccess: (chunks: Output[]) => void;

  /**
   * @description Callback when the request fails
   */
  onError: (error: Error) => void;

  /**
   * @description Callback when the request is updated
   */
  onUpdate: (chunk: Output) => void;

  /**
   * @description Callback monitoring and control the stream
   */

  onStream?: (abortController: AbortController) => void;
}

export type StreamRequestFunction<Input = AnyObject, Output = SSEOutput> = (
  params: StreamRequestParams & Input,
  callbacks: StreamRequestCallbacks<Output>,
  transformStream?: XStreamOptions<Output>['transformStream'],
) => Promise<void>;

class StreamRequestClass {
  static init(options: StreamRequestOptions): StreamRequestClass {
    if (!options.baseURL || typeof options.baseURL !== 'string')
      throw new Error('The baseURL is not valid!');

    return new StreamRequestClass(options);
  }

  readonly baseURL;

  private defaultHeaders;
  private customOptions;

  private constructor(options: StreamRequestOptions) {
    const { baseURL, ...customOptions } = options;

    this.baseURL = options.baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    this.customOptions = customOptions;
  }

  create = async <Input = AnyObject, Output = SSEOutput>(
    params: StreamRequestParams & Input,
    callbacks?: StreamRequestCallbacks<Output>,
    transformStream?: XStreamOptions<Output>['transformStream'],
  ) => {
    const abortController = new AbortController();
    const timeStamp = await getCurrentTimer();
    const urlParams = paramStringify({
      Timestamp: timeStamp,
      SignatureNonce: generateSignatureNonce(),
      ...params,
    });
    const requestInit = {
      method: 'GET',
      // body: ,
      headers: this.defaultHeaders,
      signal: abortController.signal,
    };

    callbacks?.onStream?.(abortController);

    try {
      const response = await xFetch(`${this.baseURL}?${urlParams}`, {
        fetch: this.customOptions.fetch,
        ...requestInit,
      });

      if (transformStream) {
        await this.customResponseHandler<Output>(response, callbacks, transformStream);
        return;
      }

      const contentType = response.headers.get('content-type') || '';

      const mimeType = contentType.split(';')[0].trim();

      switch (mimeType) {
        /** SSE */
        case 'text/event-stream':
          await this.sseResponseHandler<Output>(response, callbacks);
          break;

        /** JSON */
        case 'application/json':
          await this.jsonResponseHandler<Output>(response, callbacks);
          break;

        default:
          throw new Error(`The response content-type: ${contentType} is not support!`);
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown error!');

      callbacks?.onError?.(err);
      throw err;
    }
  };

  private customResponseHandler = async <Output = SSEOutput>(
    response: Response,
    callbacks?: StreamRequestCallbacks<Output>,
    transformStream?: XStreamOptions<Output>['transformStream'],
  ) => {
    const chunks: Output[] = [];

    for await (const chunk of XStream({
      readableStream: response.body!,
      transformStream,
    })) {
      chunks.push(chunk);
      callbacks?.onUpdate?.(chunk);
    }

    callbacks?.onSuccess?.(chunks);
  };

  private sseResponseHandler = async <Output = SSEOutput>(
    response: Response,
    callbacks?: StreamRequestCallbacks<Output>,
  ) => {
    const chunks: Output[] = [];
    const stream = XStream<Output>({
      readableStream: response.body!,
    });
    for await (const chunk of stream) {
      chunks.push(chunk);
      callbacks?.onUpdate?.(chunk);
    }
    callbacks?.onSuccess?.(chunks);
  };

  private jsonResponseHandler = async <Output = SSEOutput>(
    response: Response,
    callbacks?: StreamRequestCallbacks<Output>,
  ) => {
    const chunk: Output = await response.json();

    callbacks?.onUpdate?.(chunk);
    callbacks?.onSuccess?.([chunk]);
  };
}

const StreamRequest = StreamRequestClass.init;

export default StreamRequest;
