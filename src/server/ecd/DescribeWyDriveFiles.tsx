import request from '../request';
import { POP_PRODUCT } from '../popConfig';
import { getOrCreateClientId } from '../../utils/common';

export interface DescribeWyDriveFilesRequest {
  LoginToken?: string; // 登录凭证
  SessionId?: string; // 会话ID
  ParentFolderId?: string; // 父文件夹ID
  PageSize?: number; // 每页数量
  PageNumber?: number; // 页码
  ClientId?: string; // 客户端ID
  LoginSessionId?: string; // 登录会话ID
  RegionId?: string; // 地域ID
  ProductType?: string; // 产品类型
}

export interface DescribeWyDriveFilesResponse {
  TotalCount: number; // 资源总数量
  RequestId: string; // 请求ID
  Message: string; // 消息
  Files: FileData[]; // 数据
  Code: number; // 状态码
}

export interface FileData {
  FileId: string; // 文件/文件夹ID
  FileName: string; // 名称
  FilePath: string; // 文件路径
  Path: string; // oss全路径
  FileType: 'FILE' | 'FOLDER'; // 文件or目录 FILE/FOLDER
  Size: number; // 大小
  Status: string; // 文件/文件夹状态，理论上文件的状态都是正常（NORMAL），只有文件夹会出现正常（NORMAL）和重命名中（RENAMING）。
  FileFormatIconUrl: string; // 文件格式icon地址
  GmtCreate: string; // 创建时间
  GmtModified: string; // 修改时间
  Tags: Tag[]; // 标签列表
}

export interface Tag {
  TagCode: string; // 标签值
  TagDesc: string; // 标签描述，譬如AI生成
  TagStyle: string; // 标签展示前端样式
}

export const DescribeWyDriveFiles = (params: DescribeWyDriveFilesRequest) => {
  return request<DescribeWyDriveFilesRequest, DescribeWyDriveFilesResponse>({
    product: POP_PRODUCT.ecd,
    action: 'DescribeWyDriveFiles',
    params: {
      ...params,
      ClientId: getOrCreateClientId(),
    },
  });
};
