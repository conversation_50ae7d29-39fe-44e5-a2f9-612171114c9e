import axios, { AxiosRequestConfig } from 'axios';
import { Modal } from 'antd';
import { getCurrentTimer, paramStringify, generateSignatureNonce, getLoginInfo } from '@/utils';
import { POP_CONFIG, POP_PRODUCT } from './popConfig';

const createRequest = () => {
  const instance = axios.create({
    timeout: 180000, // 超时时间这么长？
  });

  instance.interceptors.request.use((config) => {
    // 发送请求前处理
    // headers 可能为 undefined，这里做兜底并重新赋值，避免直接改参数属性的类型告警
    const headers = (config.headers ?? {}) as Record<string, any>;
    headers['Content-Type'] = 'application/x-www-form-urlencoded';
    config.headers = headers as any;
    return config;
  });

  instance.interceptors.response.use(
    (response) => {
      // 成功状态（HTTP 状态码 2xx）
      const data = response?.data ?? {};
      const config: any = response?.config || {};
      const errorOptions = config.errorOptions || {};
      const {
        showErrorModal: shouldShowErrorModal = true,
        throwError: shouldThrowError = true,
      } = errorOptions;

      // 统一 POP 返回码处理：当 Code 存在且不为 '200' 时按错误处理
      if (data && typeof data === 'object' && 'Code' in data && data.Code !== '200') {
        if (shouldShowErrorModal) {
          showErrorModal(data);
        }
        if (shouldThrowError) {
          return Promise.reject(data);
        }
        // 不抛错则直接返回数据给调用方自行处理
        return data;
      }

      return data;
    },
    (error) => {
      // 非 2xx 或网络错误
      const errorData = error?.response?.data ?? { Message: error?.message };

      showErrorModal(errorData);
      return Promise.reject(errorData);
    },
  );

  return instance;
};

const requestInstance = createRequest();

// 扩展的请求配置接口
interface RequestConfig<T> {
  product: POP_PRODUCT;
  params?: T;
  action: string;
  // 错误处理选项
  errorOptions?: {
    showErrorModal?: boolean; // 是否显示错误弹窗
    throwError?: boolean; // 是否抛出错误
  };
}

// 定义错误响应数据结构
export interface ErrorResponse {
  RequestId?: string;
  Code?: string;
  Message?: string;
  [key: string]: any;
}

/**
 * 显示错误弹窗
 * @param errorData 错误数据
 */
export const showErrorModal = (errorData: ErrorResponse) => {
  const { RequestId, Code, Message } = errorData;

  Modal.error({
    title: '请求失败',
    content: (
      <div>
        {Message && (
          <div>
            <strong>错误信息：</strong>
            {Message}
          </div>
        )}
        {Code && (
          <div>
            <strong>错误代码：</strong>
            {Code}
          </div>
        )}
        {RequestId && (
          <div>
            <strong>请求ID：</strong>
            {RequestId}
          </div>
        )}
      </div>
    ),
    width: 500,
    okText: '确定',
  });
};

/**
 * 发送 POP API 请求
 * @param config 请求配置
 * @returns Promise<AxiosResponse<R>>
 */
const request = async <T, R>({
  product,
  action,
  params,
  errorOptions = {},
}: RequestConfig<T>): Promise<R> => {
  // 这里只是转发给拦截器，不在此处使用，避免未使用告警

  const popConfig = POP_CONFIG[product];
  const loginInfo = await getLoginInfo();
  const timeStamp = await getCurrentTimer();

  // 构建请求参数
  const requestParams: any = {
    Action: action,
    Timestamp: timeStamp,
    SignatureNonce: generateSignatureNonce(),
    ClientType: 'HTML5',
    Format: 'JSON',
    Version: popConfig.version,
    LoginToken: loginInfo.loginToken,
    LoginSessionId: loginInfo.loginSessionId,
    RegionId: loginInfo.loginRegionId,
    ...params,
  };

  if (!requestParams.SessionId && (product === POP_PRODUCT.ecd || product === POP_PRODUCT.ecd20250730)) {
    requestParams.SessionId = loginInfo.loginSessionId;
  }

  // 通过自定义字段把错误处理选项注入到 axios config，让拦截器可感知
  const axiosConfig: AxiosRequestConfig & { errorOptions?: RequestConfig<T>['errorOptions'] } = {
    // 自定义扩展字段，不会影响 axios，本项目拦截器会读取
    errorOptions,
  };

  return requestInstance.post(
    popConfig.getEndpoint(action),
    paramStringify(requestParams),
    axiosConfig,
  );
};

export default request;
