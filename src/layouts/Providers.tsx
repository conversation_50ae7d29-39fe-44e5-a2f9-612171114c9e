import { XProvider } from '@ant-design/x';
import { ConfigProvider, App } from 'antd';
import { EventEmitterProvider } from '@/hooks/useGlobalEventEmitter';
import { TabProvider } from '@/contexts/TabContext';
import { theme } from './theme';
import zhCN from 'antd/es/locale/zh_CN';
import { DEFAULT_ROUTE_TABS } from '@/constant/tabs';

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ConfigProvider
      theme={theme}
      locale={zhCN}
    >
      <XProvider>
        <EventEmitterProvider>
          <TabProvider defaultRouteTabs={DEFAULT_ROUTE_TABS} >
            <App>{children}</App>
          </TabProvider>
        </EventEmitterProvider>
      </XProvider>
    </ConfigProvider>
  );
}
