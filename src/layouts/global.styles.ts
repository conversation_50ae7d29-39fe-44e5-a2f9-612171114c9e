import { createStyles } from 'antd-style';

export const useGlobalStyles = createStyles(({ css, token }) => {
  return {
    global: css`
      .ant-checkbox-wrapper {
        .ant-checkbox {
          .ant-checkbox-inner {
            border-radius: 50%;
          }
        }
      }

      .ant-checkbox-indeterminate .ant-checkbox-inner {
        background: ${token.colorPrimary};
        border: none;
      }

      .ant-checkbox-indeterminate .ant-checkbox-inner::after {
        height: 2px;
        background-color: #fff;
      }

      .ant-table-wrapper .ant-table-tbody >tr >td {
        border-bottom: none;
      }
    `,
  };
});
