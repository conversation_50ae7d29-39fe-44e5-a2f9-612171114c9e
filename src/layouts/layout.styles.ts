import { createStyles } from 'antd-style';

export const useLayoutStyles = createStyles(({ css, token }) => {
  return {
    wrapper: css`
      width: 100vw;
      height: 100vh;
      display: flex;
      background: ${token.colorBorderSecondary};
    `,

    conversationAreaWithTab: css`
      width: 450px; // 防止flex item溢出
      background: ${token.colorBgContainer};
    `,

    conversationAreaWithNoTab: css`
      flex: 1;
      min-width: 375px; // 防止flex item溢出
      background: ${token.colorBgContainer};
    `,

    noConversationArea: css`
      display: none;
    `,

    tabAreaWithConversation: css`
      flex: 1;
      min-width: 375px;
      background: #f2f5fa;
      padding: 20px;
      max-width: 100%;
    `,

    tabAreaWithNoConversation: css`
      flex: 1;
      background: #f2f5fa;
      padding: 20px;
      max-width: 100%;
    `,

    noTabArea: css`
      flex: 0 0 auto;
      max-width: 0;
      padding: 0;
      opacity: 0;
      overflow: hidden;
      pointer-events: none;
    `,
  };
});
