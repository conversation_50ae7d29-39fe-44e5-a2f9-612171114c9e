import React, { useCallback, useMemo } from 'react';
import { Outlet, useAppData, useLocation, matchPath } from 'umi';
import Menu from '@/components/menu';
import TabContainer from '@/components/tab-container';
import { useTabContext } from '@/contexts/TabContext';
import { useLayoutStyles } from './layout.styles';
import { useGlobalStyles } from './global.styles';

export default function Layout() {
  const { styles: layoutStyles, cx } = useLayoutStyles();
  const { styles: globalStyles } = useGlobalStyles();
  const { tabs, activeKey, switchToTab, closeTab, tabVisible } = useTabContext();
  const { routes } = useAppData();
  const location = useLocation();

  const hideConversation = useMemo(() => {
    const routesArray = Object.entries(routes).map(([, value]) => value);
    const matchedRoute = routesArray.find((route) =>
      matchPath({ path: route.path || '' }, location.pathname));
    // @ts-ignore: hideConversation is a custom property added to route config
    return matchedRoute?.hideConversation;
  }, [location.pathname, routes]);

  const onTabEdit = useCallback(
    (key: React.MouseEvent | React.KeyboardEvent | string, action: 'remove' | 'add') => {
      if (action === 'remove') {
        closeTab(key as string);
      }
    },
    [closeTab],
  );

  const conversationClassName = hideConversation
    ? layoutStyles.noConversationArea
    : !tabVisible
      ? layoutStyles.conversationAreaWithNoTab
      : layoutStyles.conversationAreaWithTab;

  const tabAreaClassName = !tabVisible
    ? layoutStyles.noTabArea
    : hideConversation
      ? layoutStyles.tabAreaWithNoConversation
      : layoutStyles.tabAreaWithConversation;

  return (
    <div className={cx(layoutStyles.wrapper, globalStyles.global)}>
      {/* 菜单区域 */}
      <Menu />

      {/* 主要对话区域 - 如果路由配置了hideConversation，则不显示 */}
      <div className={conversationClassName}>
        <Outlet />
      </div>

      {/* 功能面板区域 */}
      <div className={tabAreaClassName}>
        <TabContainer
          tabs={tabs}
          activeKey={activeKey}
          onTabChange={switchToTab}
          onTabEdit={onTabEdit}
          hideConversation={hideConversation}
        />
      </div>
    </div>
  );
}
