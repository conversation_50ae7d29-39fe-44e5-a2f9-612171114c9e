import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

export const getCurrentTimer = async () => {
  const timeoutPromise = (timeout: number) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({ status: 500 });
      }, timeout);
    });
  };
  const requestPromise = (url: string) => {
    return fetch(url, {
      method: 'GET',
    });
  };
  return Promise.race([timeoutPromise(3000), requestPromise('https://eds.aliyun.com/timestamp')])
    .then(async (res: any) => {
      if (res.status === 200) {
        const dataInfo = await res.json();
        const timer = new Date(parseInt(dataInfo)).toISOString();
        return timer;
      } else {
        throw new Error();
      }
    })
    .catch((e) => {
      return dayjs().utc().format();
    });
};

/**
 * 生成签名随机数
 * @returns 基于时间戳和随机数的唯一标识
 */
export const generateSignatureNonce = (): string => {
  const timestamp = dayjs.utc().format('x');
  const random = Math.ceil(Math.random() * 10000);
  return `${timestamp}${random}`;
};

/**
 * 根据UTC时间显示相对时间（多久前）
 * @param utcTime UTC时间字符串或Date对象
 * @returns 相对时间字符串，如"3分钟前"、"2小时前"等
 */
export const getRelativeTime = (utcTime: string | Date): string => {
  const now = dayjs.utc();
  const target = dayjs.utc(utcTime);

  if (!target.isValid()) {
    return '时间无效';
  }

  const diffInSeconds = now.diff(target, 'second');

  // 如果时间在未来，显示"刚刚"
  if (diffInSeconds < 0) {
    return '刚刚';
  }

  // 1分钟内
  if (diffInSeconds < 60) {
    return '刚刚';
  }

  // 1小时内
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  }

  // 1天内
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  }

  // 1周内
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}天前`;
  }

  // 1个月内
  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks}周前`;
  }

  // 1年内
  const diffInMonths = now.diff(target, 'month');
  if (diffInMonths < 12) {
    return `${diffInMonths}个月前`;
  }

  // 超过1年
  const diffInYears = now.diff(target, 'year');
  return `${diffInYears}年前`;
};

/**
 * 将GMT/UTC时间转换为当地时间
 * @param gmtTime GMT/UTC时间字符串或Date对象
 * @param format 输出格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 转换为当地时间的字符串
 */
export const convertGMTToLocal = (
  gmtTime: string | Date,
  format = 'YYYY-MM-DD HH:mm:ss',
): string => {
  const utcTime = dayjs.utc(gmtTime);

  if (!utcTime.isValid()) {
    return '时间无效';
  }

  // 转换为当地时间
  return utcTime.local().format(format);
};

/**
 * 将GMT/UTC时间转换为当地时间的dayjs对象
 * @param gmtTime GMT/UTC时间字符串或Date对象
 * @returns 当地时间的dayjs对象
 */
export const convertGMTToLocalDayjs = (gmtTime: string | Date) => {
  const utcTime = dayjs.utc(gmtTime);

  if (!utcTime.isValid()) {
    return null;
  }

  return utcTime.local();
};

/**
 * 获取当前当地时间
 * @param format 输出格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 当前当地时间字符串
 */
export const getCurrentLocalTime = (format = 'YYYY-MM-DD HH:mm:ss'): string => {
  return dayjs().format(format);
};

export default dayjs;
