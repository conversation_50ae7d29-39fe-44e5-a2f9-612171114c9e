export const formatFileSize = (bytes: number) => {
  if (bytes === 0 || bytes === undefined) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

// 文件类型映射
const FILE_TYPE_MAP: Record<string, string> = {
  // 图片类型
  jpg: 'image',
  jpeg: 'image',
  png: 'image',
  gif: 'image',
  bmp: 'image',
  webp: 'image',
  svg: 'image',
  ico: 'image',
  tiff: 'image',
  tif: 'image',

  // Office 文档类型
  xls: 'excel',
  xlsx: 'excel',
  xlsm: 'excel',
  doc: 'word',
  docx: 'word',
  docm: 'word',
  ppt: 'ppt',
  pptx: 'ppt',
  pptm: 'ppt',

  // 其他文档类型
  pdf: 'pdf',
  txt: 'txt',
  md: 'md',
  html: 'html',
  epub: 'epub',
  mobi: 'mobi',
  web: 'web',
};

export const getFileIconByFileName = (fileName: string, type?: 'FOLDER' | 'FILE'): string => {
  if (type === 'FOLDER') {
    return 'ILL-FOLDER';
  }

  if (!fileName || typeof fileName !== 'string') {
    return 'ILL-TXT';
  }

  // 使用正则表达式获取文件扩展名
  const match = fileName.match(/\.([^.]+)$/);
  if (!match) {
    return 'ILL-TXT';
  }

  const fileExtension = match[1].toLowerCase();
  const mappedType = FILE_TYPE_MAP[fileExtension];

  return `ILL-${(mappedType || 'txt').toUpperCase()}`;
};
