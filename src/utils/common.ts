export const safeParseJSON = (jsonStr?: string | null) => {
  let parsed;
  try {
    if (!jsonStr) return;
    parsed = JSON.parse(jsonStr);
  } catch (e) {
    // Oh well, but whatever...
    console.log(e);
  }
  return parsed; // Could be undefined!
};

export const safeStringifyJSON = (json: any) => {
  let str;
  try {
    str = JSON.stringify(json);
  } catch (e) {
    // Oh well, but whatever...
    console.log(e);
    return '';
  }
  return str; // Could be undefined!
};

export function paramStringify(obj: { [key: string]: any }): string {
  return Object.keys(obj)
    .filter((k) => obj[k] || +obj[k] === 0)
    .map((k) => {
      let value = obj[k];
      typeof value === 'object'
        ? (value = encodeURIComponent(JSON.stringify(value)))
        : (value = encodeURIComponent(value));
      return `${encodeURIComponent(k)}=${value}`;
    })
    .join('&');
}

/**
 * 生成符合 RFC 4122 标准的 UUID v4
 */
export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

/**
 * 获取或生成客户端唯一标识符
 * 优先从 localStorage 获取，不存在则生成新的并保存
 */
export const getOrCreateClientId = (): string => {
  try {
    const storedId = localStorage.getItem('clientId');
    if (storedId) {
      return storedId;
    }

    const newId = generateUUID();
    localStorage.setItem('clientId', newId);
    return newId;
  } catch (error) {
    // 如果 localStorage 不可用，返回生成的 UUID
    console.warn('localStorage not available, using generated UUID:', error);
    return generateUUID();
  }
};

/**
 * @description 获取url查询字段
 */
export const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search || window.location.hash.split('?')[1]);
  const obj: Record<string, string> = {};
  params.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
};
