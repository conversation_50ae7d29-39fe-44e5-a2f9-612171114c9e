type ScriptStatus = 'loading' | 'loaded' | 'error';

// 全局状态管理器
class ScriptStateManager {
  private static instance: ScriptStateManager;

  static getInstance() {
    if (!ScriptStateManager.instance) {
      ScriptStateManager.instance = new ScriptStateManager();
    }
    return ScriptStateManager.instance;
  }

  private scriptCache = new Map<string, Promise<void>>();
  private statusCache = new Map<string, ScriptStatus>();
  private listeners = new Map<string, Set<(status: ScriptStatus) => void>>();

  // 预加载脚本
  preloadScript(url: string): Promise<void> {
    if (this.scriptCache.has(url)) {
      return this.scriptCache.get(url)!;
    }

    // 设置初始状态为 loading
    this.setStatus(url, 'loading');

    const promise = new Promise<void>((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url;
      script.async = true;
      script.onload = () => {
        this.setStatus(url, 'loaded');
        resolve();
      };

      script.onerror = (error) => {
        this.setStatus(url, 'error');
        this.scriptCache.delete(url);
        reject(error);
      };

      document.head.appendChild(script);
    });

    this.scriptCache.set(url, promise);
    return promise;
  }

  // 获取脚本状态
  getStatus(url: string): ScriptStatus {
    return this.statusCache.get(url) || 'loading';
  }

  // 添加状态变化监听器
  addListener(url: string, callback: (status: ScriptStatus) => void) {
    if (!this.listeners.has(url)) {
      this.listeners.set(url, new Set());
    }
    this.listeners.get(url)!.add(callback);

    // 立即返回当前状态
    callback(this.getStatus(url));
  }

  // 移除监听器
  removeListener(url: string, callback: (status: ScriptStatus) => void) {
    this.listeners.get(url)?.delete(callback);
  }

  // 检查脚本是否已加载
  isLoaded(url: string): boolean {
    return this.getStatus(url) === 'loaded';
  }

  // 清理缓存
  clearCache(url?: string) {
    if (url) {
      this.scriptCache.delete(url);
      this.statusCache.delete(url);
      this.listeners.delete(url);
    } else {
      this.scriptCache.clear();
      this.statusCache.clear();
      this.listeners.clear();
    }
  }

  // 设置脚本状态并通知监听器
  private setStatus(url: string, status: ScriptStatus) {
    this.statusCache.set(url, status);
    this.notifyListeners(url, status);
  }

  // 通知所有监听器
  private notifyListeners(url: string, status: ScriptStatus) {
    this.listeners.get(url)?.forEach(callback => callback(status));
  }
}

// 全局实例
export const scriptManager = ScriptStateManager.getInstance();
