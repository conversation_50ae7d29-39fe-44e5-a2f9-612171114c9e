// 获取软终端信息

import { safeParseJSON, safeStringifyJSON } from './common';
// 通用插件调用函数
export const callWyPlugin = ({ pluginName, methodName, params = {}, timeoutMs = 5000, onSuccess, onError }: {
  pluginName: string;
  methodName: string;
  params: any;
  timeoutMs?: number;
  onSuccess: (res: any) => void;
  onError: (code: string, msg: string) => void;
}) => {
  if (!(window as any)?.cefViewQuery) {
    // 非软终端环境直接返回
    onError('-1', 'not cefViewQuery');
    return;
  }
  const timer = setTimeout(() => {
    onError('-1', 'timeout');
  }, timeoutMs + 500);
  const request = {
    plugin: pluginName,
    method: methodName,
    params,
    timeout: timeoutMs, // 添加超时设置，由C++层处理
  };

  const query = {
    request: safeStringifyJSON(request),
    onSuccess(response: string) {
      console.log('Plugin call success:', response);
      clearTimeout(timer);
      try {
        const result = safeParseJSON(response);
        if (result.success) {
          onSuccess(result.data || result.message);
        } else {
          throw new Error(result.message);
        }
      } catch (e: any) {
        onError('-1', e);
      }
    },
    onFailure(error_code: string, error_message: string) {
      clearTimeout(timer);
      onError(error_code, error_message);
    },
  };
  (window as any).cefViewQuery?.(query);
};
