/**
 * 文件下载工具
 * 利用浏览器原生下载能力，支持单个和批量下载
 */

import { message } from 'antd';

// 下载链接接口
export interface DownloadLink {
  DownloadUrl: string; // 必传：下载链接
  FileName?: string; // 可选：文件名，如果不传则从URL中提取
}

// 下载选项接口
export interface DownloadOptions {
  showProgress?: boolean; // 是否显示下载进度提示
  delayBetweenDownloads?: number; // 批量下载时文件间的延迟（毫秒）
}

// 默认下载选项
const DEFAULT_DOWNLOAD_OPTIONS: DownloadOptions = {
  showProgress: false,
  delayBetweenDownloads: 300,
};

/**
 * 从URL中提取文件名
 * @param url 下载链接
 * @returns 文件名
 */
const extractFileNameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const fileName = pathname.split('/').pop() || 'download';
    return fileName.split('?')[0] || 'download';
  } catch {
    return 'download';
  }
};

/**
 * 单个文件下载
 * @param downloadLink 下载链接信息
 * @param options 下载选项
 */
export const downloadSingleFile = async (
  downloadLink: DownloadLink,
  options: DownloadOptions = {},
): Promise<boolean> => {
  const opts = { ...DEFAULT_DOWNLOAD_OPTIONS, ...options };

  try {
    // 验证下载链接
    if (!downloadLink?.DownloadUrl) {
      message.error('下载链接无效');
      return false;
    }

    // 获取文件名
    const fileName = downloadLink.FileName || extractFileNameFromUrl(downloadLink.DownloadUrl);

    if (opts.showProgress) {
      message.loading(`正在下载: ${fileName}`, 0);
    }

    // 创建下载链接并触发下载
    const link = document.createElement('a');
    link.href = downloadLink.DownloadUrl;
    link.download = fileName;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('请在浏览器下载中查看结果');

    return true;
  } catch (error) {
    const fileName = downloadLink.FileName || extractFileNameFromUrl(downloadLink.DownloadUrl);
    if (opts.showProgress) {
      message.destroy();
      message.error(`下载失败: ${fileName}`);
    }
    console.error('下载文件失败:', error);
    return false;
  }
};

/**
 * 批量文件下载
 * @param downloadLinks 下载链接数组
 * @param options 下载选项
 */
export const downloadMultipleFiles = async (
  downloadLinks: DownloadLink[],
  options: DownloadOptions = {},
): Promise<{ success: number; failed: number; total: number }> => {
  const opts = { ...DEFAULT_DOWNLOAD_OPTIONS, ...options };
  const total = downloadLinks.length;
  let success = 0;
  let failed = 0;

  if (total === 0) {
    return { success: 0, failed: 0, total: 0 };
  }

  try {
    // 逐个下载文件，避免浏览器阻塞
    for (let i = 0; i < downloadLinks.length; i++) {
      const link = downloadLinks[i];

      const result = await downloadSingleFile(link, { showProgress: false });

      if (result) {
        success++;
      } else {
        failed++;
      }

      // 文件间延迟，避免浏览器阻塞
      if (i < downloadLinks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, opts.delayBetweenDownloads));
      }
    }

    // 显示最终结果
    if (failed === 0) {
      message.success(`成功下载 ${total} 个文件`);
    } else if (success === 0) {
      message.error(` ${total} 个文件下载失败`);
    } else {
      message.warning(`下载完成: ${success} 个成功, ${failed} 个失败`);
    }

    return { success, failed, total };
  } catch (error) {
    if (opts.showProgress) {
      message.destroy();
      message.error('批量下载过程中发生错误');
    }

    return { success, failed, total };
  }
};

/**
 * 智能下载 - 根据文件数量自动选择下载方式
 * @param downloadLinks 下载链接数组
 * @param options 下载选项
 */
export const downloadFiles = async (
  downloadLinks: DownloadLink | DownloadLink[],
  options: DownloadOptions = {},
): Promise<{ success: number; failed: number; total: number }> => {
  const downloadLinksArray = Array.isArray(downloadLinks) ? downloadLinks : [downloadLinks];
  if (!downloadLinks || downloadLinksArray.length === 0) {
    // message.warning('没有可下载的文件');
    return { success: 0, failed: 0, total: 0 };
  }

  // 单个文件使用单个下载方法
  if (downloadLinksArray.length === 1) {
    const result = await downloadSingleFile(downloadLinksArray[0], options);
    return {
      success: result ? 1 : 0,
      failed: result ? 0 : 1,
      total: 1,
    };
  }

  // 多个文件使用批量下载方法
  return downloadMultipleFiles(downloadLinksArray, options);
};
