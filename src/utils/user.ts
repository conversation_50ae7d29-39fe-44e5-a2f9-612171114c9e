import { getUrlParams } from './common';
import { callWyPlugin } from './bridge';


export const getLoginInfo = async () => {
  // const { loginToken, loginSessionId, username, loginRegionId } = getUrlParams();
  const getNewPromise = (key: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      callWyPlugin({
        pluginName: 'config',
        methodName: 'getSecure',
        params: {
          key,
        },
        onSuccess: (val) => resolve(val),
        onError: (code) => reject(code),
      });
    });
  };
  try {
    const [loginToken = '', loginSessionId = '', username = '', loginRegionId = ''] = await Promise.all([
      getNewPromise('ro.sd.loginToken'),
      getNewPromise('ro.sd.sessionId'),
      getNewPromise('ro.sd.username'),
      getNewPromise('ro.env.regionId'),
    ]);
    return {
      loginToken,
      loginSessionId,
      username,
      loginRegionId: loginRegionId || 'cn-hangzhou',
    };
  } catch (e) {
    return {
      loginToken: process.env.UMI_APP_LOGIN_TOKEN || '123',
      loginSessionId: process.env.UMI_APP_LOGIN_SESSION_ID || '123',
      username: '本地测试用户',
      loginRegionId: 'cn-hangzhou',
    };
  }
};
