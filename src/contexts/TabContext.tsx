import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';
import { useLocation } from 'umi';
import { v4 as uuidv4 } from 'uuid';

import type { TabsProps } from 'antd';

export interface TabItem extends Omit<NonNullable<TabsProps['items']>[number], 'children'> {
  key: string;
  label: string;
  children: React.ComponentType<any>;
  closable?: boolean;
  onClose?: () => void;
  disabled?: boolean;
  icon?: string;
  props?: Record<string, any>;
}

interface TabContextType {
  tabs: TabItem[];
  activeKey: string;
  addTab: (tab: Omit<TabItem, 'key'> & { key?: string }) => string;
  closeTab: (key: string) => void;
  switchToTab: (key: string) => void;
  updateTab: (key: string, updates: Partial<TabItem>) => void;
  closeAllTabs: () => void;
  closeOtherTabs: (exceptKey: string) => void;
  closeCurrentTab: () => void;
  tabCount: number;
  hasActiveTabs: boolean;
  // 显示/隐藏tab功能
  tabVisible: boolean;
  showTabs: () => void;
  hideTabs: () => void;
  toggleTabs: () => void;
}

const TabContext = createContext<TabContextType | null>(null);

export const useTabContext = () => {
  const context = useContext(TabContext);
  if (!context) {
    throw new Error('useTabContext must be used within TabProvider');
  }
  return context;
};

interface TabProviderProps {
  children: React.ReactNode;
  defaultRouteTabs: Record<string, TabItem[]>;
}

// 路由tab状态类型
interface RouteTabState {
  tabs: TabItem[];
  activeKey: string;
  tabVisible: boolean; // 添加tab可见性状态
}

export const TabProvider: React.FC<TabProviderProps> = ({ children, defaultRouteTabs }) => {
  const location = useLocation();
  // 存储每个路由的tab状态
  const [routeTabStates, setRouteTabStates] = useState<Map<string, RouteTabState>>(new Map());

  // 获取当前路由的标识符
  const currentRouteKey = useMemo(() => {
    // 使用路由路径作为key，也可以根据需要调整
    return location.pathname;
  }, [location.pathname]);

  // 获取当前路由的tab状态
  const currentTabState = useMemo(() => {
    return routeTabStates.get(currentRouteKey) || { tabs: [], activeKey: '', tabVisible: false };
  }, [routeTabStates, currentRouteKey]);

  // 更新当前路由的tab状态
  const updateCurrentTabState = useCallback(
    (updater: (state: RouteTabState) => RouteTabState) => {
      setRouteTabStates((prev) => {
        const newMap = new Map(prev);
        const currentState = prev.get(currentRouteKey) || {
          tabs: [],
          activeKey: '',
          tabVisible: false,
        };
        newMap.set(currentRouteKey, updater(currentState));
        return newMap;
      });
    },
    [currentRouteKey],
  );

  const mergeDefaultRouteTabs = useCallback(({ currTabs, prevTabVisible }: { currTabs: TabItem[]; prevTabVisible: boolean }) => {
    // 如果之前prevTabVisible为true，这种情况下用户主动关闭的默认tab，在addTab的时候，不要追加
    if (prevTabVisible) {
      return currTabs;
    }
    const defaultTabs = defaultRouteTabs[currentRouteKey] || [];
    const mergedMap = new Map();
    defaultTabs.forEach((tab) => {
      mergedMap.set(tab.key, tab);
    });
    currTabs.forEach((tab) => {
      mergedMap.set(tab.key, tab);
    });
    return Array.from(mergedMap.values());
  }, [defaultRouteTabs, currentRouteKey]);

  // 显示tab
  const showTabs = useCallback(() => {
    updateCurrentTabState((state) => {
      const newTabs = mergeDefaultRouteTabs({ currTabs: state.tabs, prevTabVisible: state.tabVisible });
      return ({
        ...state,
        tabs: newTabs,
        tabVisible: true,
        activeKey: state.activeKey || newTabs[0]?.key,
      });
    });
  }, [updateCurrentTabState, mergeDefaultRouteTabs]);

  // 隐藏tab
  const hideTabs = useCallback(() => {
    updateCurrentTabState((state) => ({
      ...state,
      tabVisible: false,
    }));
  }, [updateCurrentTabState]);

  // 切换tab显示/隐藏
  const toggleTabs = useCallback(() => {
    updateCurrentTabState((state) => ({
      ...state,
      tabVisible: !state.tabVisible,
    }));
  }, [updateCurrentTabState]);

  // 添加tab
  const addTab = useCallback(
    (tab: Omit<TabItem, 'key'> & { key?: string }) => {
      const newTab: TabItem = {
        key: tab.key || uuidv4(),
        closable: true,
        ...tab,
      };

      updateCurrentTabState((state) => {
        // 检查是否已存在相同key的tab
        const existingIndex = state.tabs.findIndex((t) => t.key === newTab.key);
        if (existingIndex !== -1) {
          return { ...state, activeKey: newTab.key };
        }

        // 检查tab数量限制
        const newTabs = [...state.tabs];
        const maxTabs = 10;
        if (newTabs.length >= maxTabs) {
          // 如果超过限制，移除最老的可关闭tab
          const removableIndex = newTabs.findIndex((t) => t.closable !== false);
          if (removableIndex !== -1) {
            newTabs.splice(removableIndex, 1);
          }
        }

        return {
          ...state,
          tabs: [...newTabs, newTab],
          activeKey: newTab.key,
        };
      });
      showTabs();
      return newTab.key;
    },
    [updateCurrentTabState, showTabs],
  );

  // 关闭tab
  const closeTab = useCallback(
    (targetKey: string) => {
      updateCurrentTabState((state) => {
        const targetIndex = state.tabs.findIndex((tabItem) => tabItem.key === targetKey);
        if (targetIndex === -1) return state;

        const tabItem = state.tabs[targetIndex];

        // 如果是不可关闭的tab，直接返回
        if (tabItem.closable === false) return state;

        const newTabs = state.tabs.filter((item) => item.key !== targetKey);

        // 如果关闭的是当前激活的tab，需要切换到相邻的tab
        let newActiveKey = state.activeKey;
        if (state.activeKey === targetKey && newTabs.length > 0) {
          const newActiveIndex = targetIndex >= newTabs.length ? targetIndex - 1 : targetIndex;
          newActiveKey = newTabs[newActiveIndex].key;
        }

        let { tabVisible } = state;
        if (newTabs.length === 0) {
          tabVisible = false;
          newActiveKey = '';
        }

        tabItem?.onClose?.();

        return {
          ...state,
          tabs: newTabs,
          activeKey: newActiveKey,
          tabVisible,
        };
      });
    },
    [updateCurrentTabState],
  );

  // 切换tab
  const switchToTab = useCallback(
    (key: string) => {
      updateCurrentTabState((state) => ({
        ...state,
        activeKey: key,
      }));
    },
    [updateCurrentTabState],
  );

  // 更新tab
  const updateTab = useCallback(
    (key: string, updates: Partial<TabItem>) => {
      updateCurrentTabState((state) => ({
        ...state,
        tabs: state.tabs.map((tab) => (tab.key === key ? { ...tab, ...updates } : tab)),
      }));
    },
    [updateCurrentTabState],
  );

  // 关闭所有可关闭的tab
  const closeAllTabs = useCallback(() => {
    updateCurrentTabState((state) => {
      const keepTabs = state.tabs.filter((tabItem) => tabItem.closable === false);
      return {
        ...state,
        tabs: keepTabs,
        activeKey: keepTabs.length > 0 ? keepTabs[0].key : '',
        tabVisible: keepTabs.length > 0,
      };
    });
  }, [updateCurrentTabState]);

  // 关闭其他tab
  const closeOtherTabs = useCallback(
    (exceptKey: string) => {
      updateCurrentTabState((state) => ({
        ...state,
        tabs: state.tabs.filter(
          (tabItem) => tabItem.key === exceptKey || tabItem.closable === false,
        ),
      }));
    },
    [updateCurrentTabState],
  );

  const closeCurrentTab = useCallback(() => {
    closeTab(currentTabState.activeKey);
  }, [closeTab, currentTabState.activeKey]);

  const contextValue: TabContextType = {
    tabs: currentTabState.tabs,
    activeKey: currentTabState.activeKey,
    addTab,
    closeTab,
    switchToTab,
    updateTab,
    closeAllTabs,
    closeOtherTabs,
    closeCurrentTab,
    tabCount: currentTabState.tabs.length,
    hasActiveTabs: currentTabState.tabs.length > 0,
    // 显示/隐藏tab功能
    tabVisible: currentTabState.tabVisible,
    showTabs,
    hideTabs,
    toggleTabs,
  };

  return <TabContext.Provider value={contextValue}>{children}</TabContext.Provider>;
};
