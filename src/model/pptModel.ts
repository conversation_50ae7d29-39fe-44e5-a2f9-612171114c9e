import { proxy } from 'umi';

interface PPTInfo {
  type: string;
  pptId?: string;
  content?: string;
  sessionId?: string;
  eventId?: string;
}

const defaultPPTInfo = { type: '', pptId: '', content: '', sessionId: '', eventId: '' };

interface PPTState {
  currInfo: PPTInfo;
  nextInfo: PPTInfo;
  // appkey: string;
  // channel: string;
  // code: string;
  actions: {
    setNextPptInfo: (nextInfo: PPTState['nextInfo']) => void;
    setCurrPptInfo: (currInfo: PPTState['currInfo']) => void;
    resetNextPptInfo: () => void;
    resetPptInfo: () => void;
  };
}

const pptState = proxy<PPTState>({
  currInfo: { ...defaultPPTInfo },
  nextInfo: { ...defaultPPTInfo },
  // appkey: '',
  // channel: '',
  // code: '',
  actions: {
    setNextPptInfo(nextInfo) {
      pptState.nextInfo = {
        ...defaultPPTInfo,
        ...nextInfo,
      };
    },
    resetNextPptInfo() {
      pptState.nextInfo = { ...defaultPPTInfo };
    },
    setCurrPptInfo(currInfo) {
      pptState.currInfo = {
        ...defaultPPTInfo,
        ...currInfo,
      };
    },
    resetPptInfo() {
      pptState.currInfo = { ...defaultPPTInfo };
      pptState.nextInfo = { ...defaultPPTInfo };
    },
  },
});

export default pptState;
