import { proxy } from 'umi';
import { Session } from '@/server/WuyingAI/ListUserSessions';
import { ListKnowledgeBaseData } from '@/server/WuyingAI/ListKnowledgeBases';

interface KnowledgeBaseState {
  knowledgeBaseParams: Record<string, any>;
  knowledgeBaseRefresh: number;
  sessionListRefresh: number;
  fileListRefresh: number;
  recentUpdatesRefresh: number;
  selectedSessions: Session[];
  fileIds: string[];
  messageIds: string[];
  knowledgeBaseInfo: ListKnowledgeBaseData;
  sessionSelectionModalOpen: boolean;
  actions: {
    setKnowledgeBaseRefresh: () => void;
    setSessionListRefresh: () => void;
    setFileListRefresh: () => void;
    setRecentUpdatesRefresh: () => void;
    setSelectedSessions: (sessions: Session[]) => void;
    setFileIds: (fileIds: string[]) => void;
    setMessageIds: (messageIds: string[]) => void;
    setKnowledgeBaseInfo: (knowledgeBaseInfo: ListKnowledgeBaseData) => void;
    setKnowledgeBaseParams: (knowledgeBaseParams: Record<string, any>) => void;
    setSessionSelectionModalOpen: (open: boolean) => void;
  };
}

const knowledgeBaseState = proxy<KnowledgeBaseState>({
  knowledgeBaseParams: {},
  knowledgeBaseRefresh: 0,
  sessionListRefresh: 0,
  fileListRefresh: 0,
  recentUpdatesRefresh: 0,
  selectedSessions: [],
  fileIds: [],
  messageIds: [],
  sessionSelectionModalOpen: false,
  knowledgeBaseInfo: {
    KbId: '',
    Name: '',
    Description: '',
    GmtModified: '',
    GmtCreated: '',
    SessionCount: 0,
    DocumentCount: 0,
  },
  actions: {
    setKnowledgeBaseRefresh() {
      knowledgeBaseState.knowledgeBaseRefresh += 1;
    },
    setSessionListRefresh() {
      knowledgeBaseState.sessionListRefresh += 1;
    },
    setFileListRefresh() {
      knowledgeBaseState.fileListRefresh += 1;
    },
    setRecentUpdatesRefresh() {
      knowledgeBaseState.recentUpdatesRefresh += 1;
    },
    setSelectedSessions(sessions: Session[]) {
      knowledgeBaseState.selectedSessions = sessions;
    },
    setFileIds(fileIds: string[]) {
      knowledgeBaseState.fileIds = fileIds;
    },
    setMessageIds(messageIds: string[]) {
      knowledgeBaseState.messageIds = messageIds;
    },
    setKnowledgeBaseInfo(knowledgeBaseInfo: ListKnowledgeBaseData) {
      knowledgeBaseState.knowledgeBaseInfo = knowledgeBaseInfo;
    },
    setKnowledgeBaseParams(knowledgeBaseParams: Record<string, any>) {
      knowledgeBaseState.knowledgeBaseParams = {
        ...knowledgeBaseState.knowledgeBaseParams,
        ...knowledgeBaseParams,
      };
    },
    setSessionSelectionModalOpen(open: boolean) {
      knowledgeBaseState.sessionSelectionModalOpen = open;
    },
  },
});

export default knowledgeBaseState;
