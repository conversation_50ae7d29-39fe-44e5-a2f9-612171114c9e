import { proxy } from 'umi';

interface ResearchState {
  researchInfo: ResearchInfo;
  actions: {
    setResearchInfo: (researchInfo: ResearchInfo) => void;
  };
}

interface ResearchInfo {
  research_plan?: string;
  report_outline?: Array<{ title: string; key_question: string }>;
  report_title?: string;
  loading?: boolean;
  run_error?: boolean;
  stopChat?: () => void;
  sessionId?: string;
  run_success?: boolean;
}

const researchState = proxy<ResearchState>({
  researchInfo: {
    research_plan: '',
    report_outline: [],
    report_title: '',
    loading: false,
    run_error: false,
    sessionId: '',
    stopChat: () => {},
    run_success: false,
  },
  actions: {
    setResearchInfo(researchInfo: ResearchInfo) {
      researchState.researchInfo = { ...researchState.researchInfo, ...researchInfo };
    },
  },
});

export default researchState;
