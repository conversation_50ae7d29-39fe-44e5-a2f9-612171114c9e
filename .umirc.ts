import { defineConfig } from 'umi';

const isDev = process.env.NODE_ENV === 'development';
let isProd = false;
let publicPath = './';
const {
  BUILD_ENV,
  BUILD_GIT_BRANCH = '',
  BUILD_GIT_GROUP,
  BUILD_GIT_PROJECT,
  BUILD_ARGV_STR = '',
  MOCK,
} = process.env;
if (BUILD_ENV === 'cloud') {
  // webpack 采用def发布 资源会被发布到cdn上去
  isProd = BUILD_ARGV_STR.indexOf('def_publish_env=prod') > -1;
  const host = isProd ? 'g.alicdn.com' : 'dev.g.alicdn.com';
  const appName = [BUILD_GIT_GROUP, BUILD_GIT_PROJECT].join('/');
  const version = (/\/(.*)/i.exec(BUILD_GIT_BRANCH) || [])[0] || '';
  publicPath = `https://${host}/${appName}${version}/`;
}

export default defineConfig({
  publicPath: isDev ? '/' : publicPath,
  history: {
    type: 'hash',
  },
  metas: [
    {
      'http-equiv': 'Content-Security-Policy',
      content: 'upgrade-insecure-requests',
    },
    {
      name: 'aplus-core',
      content: 'aplus.js',
    },
  ],
  headScripts: [
    `https://${isProd ? '' : 'dev.'}o.alicdn.com/aliyun-ecs/eds-cdn/icon/aipc-iconfont.js`,
    `https://${isProd ? '' : 'dev.'}o.alicdn.com/aliyun-ecs/eds-cdn/icon/aipc-iconfont-colored.js`,
    'https://dev.g.alicdn.com/aliyun-ecs/WuyingWebSdk-multi/2.9.0-asp3.17.10/WuyingWebSDK/WuyingWebSDK.js',
    `
      (function(w, d, s, q) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0],j = d.createElement(s);
        j.async = true;
        j.id = 'beacon-aplus';
        j.setAttribute('exparams','userid=&aplus&sidx=aplusSidex&ckx=aplusCkx');
        j.src = "//g.alicdn.com/alilog/mlog/aplus_v2.js";
        j.crossorigin = 'anonymous';
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'aplus_queue');
    `,
  ],
  routes: [
    { path: '/chat', component: 'chat' },
    {
      path: '/knowledge-base',
      component: 'knowledge-base',
      hideConversation: true,
    },
    {
      path: '/history',
      component: 'history',
    },
    {
      path: '/setting',
      component: 'setting',
    },
    {
      path: '/deep',
      component: 'deep',
    },
    {
      path: '/design',
      component: 'design',
    },
    {
      path: '/folder',
      component: 'folder',
    },
    {
      path: '/ppt',
      component: 'ppt',
    },
    {
      path: '/',
      redirect: '/chat',
    },
    {
      path: '/*',
      redirect: '/chat',
    },
  ],
  npmClient: 'yarn',
  valtio: {},
  plugins: ['@umijs/plugins/dist/locale', '@umijs/plugins/dist/valtio'],
  forkTSChecker: {},
  mfsu: {
    strategy: 'normal',
  },
  define: {
    'process.env.MOCK': MOCK,
  },
});
