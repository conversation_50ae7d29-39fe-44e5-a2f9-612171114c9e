// @name: OpenAPIService
// @displayName: OpenAPI请求
// @description: OpenAPI请求
// @category: services
// @addMode: replace
// @fileNameVariable: action
// @variable: {"name": "product", "type": "select", "label": "product", "required": true, "dynamicOptions": { "service": "getProducts"}}
// @variable: {"name": "action", "type": "select", "label": "API", "required": true, "dynamicOptions": { "service": "getActionsWithProduct", "dependsOn": ["product"] }}

import request from '../request';
import { POP_PRODUCT } from '../popConfig';


export interface {{action}}Request {

}

export interface {{action}}Response {

}

export const {{action}} = (params: {{action}}Request) => {
  return request<{{action}}Request, {{action}}Response>({
    product: POP_PRODUCT.{{product}},
    action: '{{action}}',
    params,
  });
};



