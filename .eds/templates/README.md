# 模板写法

模板使用 Mustache 语法，支持变量、循环、条件等语法，并且支持设置元数据，通过元数据的定义实现模板的动态渲染

## 模板类型

模板分为两种方式：

1. **普通模板**（不包含分隔线），一般用于 pages、hooks、snippets 等场景

普通模板示例（ProTablePage）

```tpl
// @name: ProTablePage
// @displayName: ProTable页面
// @description: 基于ProTable的标准列表页面模板
// @category: pages
// @variable: {"name": "pageName", "type": "string", "label": "页面名称", "required": true, "default": "ProTablePage", "validation": {"pattern": "^[A-Z][a-zA-Z0-9]*$", "message": "页面名称必须以大写字母开头"}}
// @variable: {"name": "useToken", "type": "boolean", "label": "是否使用token分页", "default": false, "description": "启用token分页模式，适用于大数据量场景"}
// @variable: {"name": "tableSize", "type": "select", "label": "表格尺寸", "options": ["small", "middle", "large"], "default": "middle"}
import React, { useCallback, useMemo, useState } from 'react';
import { ProTable } from '@ali/eds-design/pro';
import { useColumns } from './columns';

interface {{pageName}}Props {
  // Props定义
}

const {{pageName}}: React.FC<{{pageName}}Props> = (props) => {
  const request = useCallback((params, filters, sorter, extra) => {
    // TODO: 实现数据请求逻辑
  }, []);

  const columns = useColumns();

  {{#useToken}}
  const pagination = useMemo(() => {
    return {
      mode: 'token' as const,
    };
  }, []);
  {{/useToken}}

  return (
    <ProTable
      rowKey="id"
      request={request}
      columns={columns}
      size="{{tableSize}}"
      {{#useToken}}
      pagination={pagination}
      {{/useToken}}
    />
  );
};

export default {{pageName}};
```

2. **增强模板**（包含分隔线），一般用于 components 等场景
   增强模板示例（ProTable）

```tpl
// @name: ProTable
// @displayName: ProTable
// @description: ProTable
// @category: components
// @addMode: insert
// @variable: {"name": "useToken", "type": "boolean", "label": "是否使用token分页", "default": false}

import React, { useCallback, useMemo, useState } from 'react';
import { ProTable } from '@ali/eds-design/pro';
import { useColumns } from './columns';
import { ProTableRequestDataSource } from '@ali/eds-design/pro';

---IMPORTS_END---

interface Props {
  // Props定义
}

---EXTERNAL_END---

const request: ProTableRequestDataSource<any> = useCallback(async (params, filters, sorter, extra) => {
  return {
    total: 100,
    data: [],
  };
}, []);

const requestConfig = useMemo(() => {
  return {};
}, []);

const initialValues = useMemo(() => {
  return {};
}, []);

const columns = useColumns();

const searchOperationToolBar = useMemo(() => {
  return {};
}, []);

{{#useToken}}
const pagination = useMemo(() => {
  return {
    mode: 'token' as const,
  };
}, []);
{{/useToken}}

---LOGIC_END---

<ProTable
  rowKey=""
  request={request}
  requestConfig={requestConfig}
  columns={columns}
  initialValues={initialValues}
  searchOperationToolBar={searchOperationToolBar}
  {{#useToken}}
  pagination={pagination}
  {{/useToken}}
/>
```

## 模板元数据

模板元数据通过注释的形式定义在模板文件的顶部，用于配置模板的基本信息和变量定义。所有元数据都以 `// @` 开头。

### 基础元数据

```tpl
// @name: 模板名称           // 模板的唯一标识符，用于程序内部识别
// @displayName: 显示名称    // 在用户界面中显示的友好名称
// @description: 模板描述    // 模板的用途和功能说明
// @category: 模板分类       // 模板所属分类：components | pages | hooks | snippets | services
// @addMode: 添加模式     // 添加模式：insert（插入） | replace（覆盖） 默认为insert
// @fileNameVariable: 文件名变量来源 // 默认文件名变量来源 默认值为空，表示使用哪个变量名作为文件名
```

### 添加模式（addMode）

添加模式用于控制模板如何插入到目标位置。目前支持以下两种模式：

1. **insert**：插入模式，将模板内容插入到当前光标位置
2. **replace**：替换模式，将模板内容替换当前文件的所有内容

### 变量定义

变量定义使用 @variable: 标记，后跟 JSON 格式的变量配置：

```tpl
// @variable: {"name": "变量名", "type": "类型", "label": "显示标签", "required": true|false, "default": "默认值", ...其他选项}
```

#### 变量类型和配置选项

1. 字符串类型 (string)

```tpl
// @variable: {"name": "componentName", "type": "string", "label": "组件名称", "required": true, "default": "MyComponent"}

// 带验证规则的字符串
// @variable: {"name": "componentName", "type": "string", "label": "组件名称", "required": true, "default": "MyComponent", "validation": {"pattern": "^[A-Z][a-zA-Z0-9]*$", "message": "组件名必须以大写字母开头"}}

// 带占位符的字符串
// @variable: {"name": "description", "type": "string", "label": "描述", "placeholder": "请输入组件描述"}
```

2. 布尔类型 (boolean)

```t'p'l
// @variable: {"name": "hasProps", "type": "boolean", "label": "是否需要Props接口", "default": false}

// 带描述的布尔值
// @variable: {"name": "useToken", "type": "boolean", "label": "是否使用token分页", "default": false, "description": "启用token分页模式"}
```

3. 选择类型 (select)

```tpl
// @variable: {"name": "buttonType", "type": "select", "label": "按钮类型", "options": [{"value": "primary", "label": "主要按钮"}, {"value": "default", "label": "默认按钮"}, {"value": "danger", "label": "危险按钮"}], "default": "primary"}
```

4. 数字类型 (number)

```tpl
// @variable: {"name": "maxLength", "type": "number", "label": "最大长度", "default": 100, "min": 1, "max": 1000}

// 带步长的数字
// @variable: {"name": "opacity", "type": "number", "label": "透明度", "default": 1, "min": 0, "max": 1, "step": 0.1}
```

5. 多选类型 (multiselect)

```tpl
// @variable: {"name": "features", "type": "multiselect", "label": "功能特性", "options": [{"value": "pagination", "label": "分页功能"}, {"value": "search", "label": "搜索功能"}, {"value": "filter", "label": "筛选功能"}], "default": ["pagination"]}

// 简化的多选类型（直接使用字符串数组）
// @variable: {"name": "hooks", "type": "multiselect", "label": "使用的Hooks", "options": ["useState", "useEffect", "useMemo", "useCallback"], "default": ["useState"]}

// 带最小/最大选择数量限制的多选
// @variable: {"name": "columns", "type": "multiselect", "label": "表格列", "options": ["name", "age", "email", "phone", "address"], "minSelection": 1, "maxSelection": 3, "default": ["name", "age"]}
```

## 增强模板分隔线

增强模板使用特定的分隔线来区分不同的代码段落：

**---IMPORTS_END---**：导入代码段结束，之后是外部代码段
**---EXTERNAL_END---**：外部代码段结束，之后是组件内逻辑段
**---LOGIC_END---**：组件内逻辑段结束，之后是 JSX 代码段

### 代码段说明

**导入段（imports）**：放置所有 import 语句
**外部代码段（external）**：放置接口定义、类型声明、常量等
**组件内逻辑段（logic）**：放置 useState、useEffect、事件处理函数等
**JSX 段（jsx）**：放置组件的渲染内容

_注意：当使用分隔符时，需要将 ---IMPORTS_END---、---EXTERNAL_END---、---LOGIC_END--- 按顺序写全，即使有些段落为空，不然会判断错代码类型，如_

```
// @name: Form
// @displayName: Form
// @description: Form
// @category: components
// @addMode: insert

import React, { useCallback, useMemo, useState } from 'react';
import { Button, Checkbox, Form, Input } from 'antd';
import { intl } from 'umi';

---IMPORTS_END---
---EXTERNAL_END---

const [form] = Form.useForm();
const initialValues = {};

const handleOK = useCallback(async () => {
    const values = await form.validateFields();
}, []);

---LOGIC_END---

<Form
  form={form}
  initialValues={initialValues}
  layout="vertical"
>
  <Form.Item
    name=""
    label={intl('label名称')}
    extra={intl('提示信息')}
    required
   >
    <Input />
  </Form.Item>
</Form>
<div>
  <Button type="primary" onClick={() => { handleOK(); }}>
    {intl('btn.confirm')}
  </Button>
</div>
```
