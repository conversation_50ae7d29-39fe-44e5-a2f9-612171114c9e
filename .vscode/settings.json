{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "editor.formatOnSave": true, "editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["deskgroup"], "mds.i18n.rule.fileExclusions": ["/node_modules/**/*", "/build/**/*", "/src/locales/**/*"], "edsDevtools.popConfig": {"WuyingAI": {"popCode": "WuyingAI", "version": "2025-07-17"}}}