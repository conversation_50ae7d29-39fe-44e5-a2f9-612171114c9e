import Mock from 'mockjs';

// 生成UTC格式的时间字符串
const generateUTCDateTime = () => {
  const randomTime = Mock.mock('@datetime');
  return new Date(randomTime).toISOString();
};

// 解析请求体中的Action参数
const parseAction = (body: any): string => {
  if (typeof body === 'string') {
    const params = new URLSearchParams(body);
    return params.get('Action') || '';
  }
  return body?.Action || '';
};

// 生成分页响应
const generatePaginatedResponse = (data: any[], maxResults = 10, nextToken = '') => {
  const startIndex = nextToken ? parseInt(nextToken) : 0;
  const endIndex = startIndex + maxResults;
  const items = data.slice(startIndex, endIndex);
  const hasMore = endIndex < data.length;

  return {
    Data: items,
    NextToken: hasMore ? endIndex.toString() : null,
    TotalCount: data.length,
  };
};

// 使用Mock.js生成知识库数据
const generateKnowledgeBases = () => Mock.mock({
  'list|5-10': [{
    KbId: '@guid',
    Name: '@ctitle(5, 20)',
    Description: '@ctitle(10, 50)',
    CreateTime: () => generateUTCDateTime(),
    UpdateTime: () => generateUTCDateTime(),
    'Status|1': ['Available', 'Processing', 'Failed'],
    'DocumentCount|5-100': 1,
    'SessionCount|5-100': 1,
  }],
}).list;

// 使用Mock.js生成文档数据
const generateDocuments = (kbId: string) => Mock.mock({
  'list|3-15': [{
    DocumentId: '@guid',
    KbId: kbId,
    FileName: '@title(1, 3).@pick(["pdf", "docx", "txt", "md"])',
    OssPath: '/docs/@title(1, 2).@pick(["pdf", "docx", "txt", "md"])',
    UploadTime: () => generateUTCDateTime(),
    'Status|1': ['Available', 'Processing', 'Failed'],
    'Size|500000-5000000': 1,
  }],
}).list;

// 使用Mock.js生成文档列表数据（符合ListKnowledgeBaseDocuments接口）
const generateDocumentList = (kbId: string) => Mock.mock({
  'list|3-15': [{
    DocumentId: '@guid',
    FileName: '@title(1, 3).@pick(["pdf", "docx", "txt", "md"])',
    FileSize: '@integer(500000, 5000000)', // 文件大小（字节）
    'Status|1': ['Available', 'Processing', 'Failed'],
    GmtModified: () => generateUTCDateTime(), // 修改时间
    GmtCreated: () => generateUTCDateTime(), // 创建时间
    IsAiGenerated: () => Mock.mock('@boolean'), // 是否是AI生成
    Type: () => Mock.mock('@ctitle(5, 15)'), // 类型
  }],
}).list;

// 使用Mock.js生成会话数据
const generateSessions = (kbId: string) => Mock.mock({
  'list|2-8': [{
    SessionId: '@guid',
    KbId: kbId,
    CreateTime: () => generateUTCDateTime(),
    UpdateTime: () => generateUTCDateTime(),
    'MessageCount|3-20': 1,
    'ArtifactCount|1-5': 1,
    SessionName: '@ctitle(5, 15)',
  }],
}).list;

// 使用Mock.js生成消息数据
const generateMessages = (sessionId: string) => Mock.mock({
  'list|5-20': [{
    MessageId: '@guid',
    'MessageRole|1': ['User', 'Assistant'],
    GmtModified: () => generateUTCDateTime(),
    GmtCreated: () => generateUTCDateTime(),
    MessageContent: '@cparagraph(1, 3)',
    IsAdded: () => Mock.mock('@boolean'),
  }],
}).list;

// 使用Mock.js生成产物数据
const generateArtifacts = (sessionId: string) => Mock.mock({
  'list|1-3': [{
    ArtifactId: '@guid',
    SessionId: sessionId,
    FileName: '@ctitle(5, 15)',
    'Type|1': ['document', 'table', 'chart', 'code'],
    GmtModified: () => generateUTCDateTime(),
    GmtCreated: () => generateUTCDateTime(),
    FileSize: '@integer(500000, 5000000)',
    Status: '@ctitle(5, 15)',
  }],
}).list;

// 使用Mock.js生成日志数据
const generateLogs = (kbId: string) => Mock.mock({
  'list|5-15': [{
    LogId: '@guid',
    KbId: kbId,
    'Action|1': ['CreateDocument', 'UpdateKnowledgeBase', 'DeleteDocument', 'CreateSession'],
    'Status|1': ['Success', 'Failed', 'Processing'],
    Message: '@csentence(5, 20)',
    CreateTime: () => generateUTCDateTime(),
    KbName: '@ctitle(5, 15)',
    Title: '@ctitle(5, 15)',
    Status: '@ctitle(5, 15)',
    Type: '@ctitle(5, 15)',
  }],
}).list;

// 使用Mock.js生成会话历史消息数据
const generateSessionHistoryMessages = (sessionId: string) => Mock.mock({
  'list|8-15': [{
    Timestamp: () => Date.now() - Mock.mock('@integer(0, 86400000)'), // 随机过去24小时内的时间戳
    'IsInKb|1': ['true', 'false'],
    'Role|1': ['user', 'assistant', 'system'],
    'Type|1': ['message', 'tool_call', 'tool_response', 'action'],
    'ToolName|1': ['search', 'calculator', 'file_reader', 'web_browser', ''],
    Delta: '@cparagraph(1, 2)',
    ExtInfo: {
      source: '@url',
      confidence: '@float(0.5, 1.0, 2, 2)',
      references: '@ctitle(3, 8)',
      metadata: {
        fileType: '@pick(["pdf", "docx", "txt", "md"])',
        fileSize: '@integer(1000, 5000000)',
      },
    },
    Content: '@cparagraph(2, 4)',
    ToolCallId: '@guid',
    EventId: '@guid',
    RunId: '@guid',
    SessionId: sessionId,
    MessageId: '@guid',
  }],
}).list;

// 使用Mock.js生成用户会话文件数据
const generateUserSessionArtifacts = (sessionId: string) => Mock.mock({
  'list|2-8': [{
    'Type|1': ['document', 'image', 'table', 'chart', 'code', 'text'],
    RoundId: '@integer(1, 10)',
    'Size|500000-5000000': 1, // 文件大小（字节）
    GmtModified: () => generateUTCDateTime(),
    Id: '@guid',
    GmtCreated: () => generateUTCDateTime(),
    Name: '@ctitle(3, 10)',
    MessageId: '@guid',
  }],
}).list;

// 使用Mock.js生成用户会话数据
const generateUserSessions = () => Mock.mock({
  'list|10-50': [{
    SessionId: '@guid',
    AgentId: '@guid',
    Title: '@ctitle(3, 8)',
    GmtCreate: () => generateUTCDateTime(),
    GmtModified: () => generateUTCDateTime(),
    IsInKb: () => Mock.mock('@boolean'),
  }],
}).list;

export default {
  // 拦截所有POST请求到/api路径
  'POST /api/WuyingAI/*': (req: any, res: any) => {
    const action = parseAction(req.body);

    // 模拟网络延迟
    setTimeout(() => {
      const baseResponse = {
        RequestId: Mock.mock('@guid'),
      };

      switch (action) {
        case 'CreateKnowledgeBase': {
          res.json({
            ...baseResponse,
            KbId: Mock.mock('@guid'),
            Message: '知识库创建成功',
          });
          break;
        }

        case 'ListKnowledgeBases': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const mockData = generateKnowledgeBases();

          res.json({
            ...baseResponse,
            ...generatePaginatedResponse(mockData, maxResults, nextToken),
          });
          break;
        }

        case 'DescribeKnowledgeBase': {
          const mockData = {
            DocumentCount: Mock.mock('@integer(5, 100)'), // 文档数量
            KbId: req.body.KbId || Mock.mock('@guid'), // 知识库ID
            Description: Mock.mock('@ctitle(10, 50)'), // 描述
            GmtModified: generateUTCDateTime(), // 最后修改时间
            GmtCreated: generateUTCDateTime(), // 创建时间
            SessionCount: Mock.mock('@integer(2, 50)'), // 会话数量
            Name: Mock.mock('@ctitle(5, 20)'), // 名称
          };

          res.json({
            ...baseResponse,
            TotalCount: 1, // 总数量
            Message: '获取知识库详情成功', // 消息
            NextToken: '', // 下一个查询开始的Token
            Data: mockData, // 返回数据
            Code: 200, // 状态码
          });
          break;
        }

        case 'UpdateKnowledgeBase': {
          res.json({
            ...baseResponse,
            Message: '知识库更新成功',
          });
          break;
        }

        case 'DeleteKnowledgeBase': {
          res.json({
            ...baseResponse,
            Message: '知识库删除成功',
          });
          break;
        }

        case 'CreateKnowledgeBaseDocument': {
          const documentIds = Mock.mock({
            'list|1-5': ['@guid'],
          }).list;

          res.json({
            ...baseResponse,
            DocumentIds: documentIds,
            Message: '添加文件成功',
          });
          break;
        }

        case 'DescribeKnowledgeBaseDocument': {
          const kbId = req.body.KbId;
          const documents = generateDocuments(kbId);

          res.json({
            ...baseResponse,
            Documents: documents,
          });
          break;
        }

        case 'DeleteKnowledgeBaseDocuments': {
          res.json({
            ...baseResponse,
            Message: '文档删除成功',
          });
          break;
        }

        case 'ListKnowledgeBaseDocuments': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const kbId = req.body.KbId || Mock.mock('@guid');
          const documents = generateDocumentList(kbId);

          res.json({
            ...baseResponse,
            ...generatePaginatedResponse(documents, maxResults, nextToken),
            Message: '获取知识库文档列表成功',
            Code: 200,
          });
          break;
        }

        case 'CreateKnowledgeBaseSession': {
          res.json({
            ...baseResponse,
            Code: 200,
            Message: '会话创建成功',
          });
          break;
        }

        case 'ListKnowledgeBaseSessions': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const kbId = req.body.KbId || Mock.mock('@guid');
          const sessions = generateSessions(kbId);

          res.json({
            ...baseResponse,
            ...generatePaginatedResponse(sessions, maxResults, nextToken),
          });
          break;
        }

        case 'DeleteKnowledgeBaseSessions': {
          res.json({
            ...baseResponse,
            Message: '会话删除成功',
          });
          break;
        }

        case 'ListKnowledgeBaseSessionMessages': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const sessionId = req.body.SessionId || Mock.mock('@guid');
          const messages = generateMessages(sessionId);

          res.json({
            ...baseResponse,
            ...generatePaginatedResponse(messages, maxResults, nextToken),
          });
          break;
        }

        case 'ListKnowledgeBaseSessionFiles': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const sessionId = req.body.SessionId || Mock.mock('@guid');
          const artifacts = generateArtifacts(sessionId);

          res.json({
            ...baseResponse,
            ...generatePaginatedResponse(artifacts, maxResults, nextToken),
          });
          break;
        }

        case 'ListKnowledgeBaseLogs': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const kbId = req.body.KbId || Mock.mock('@guid');
          const logs = generateLogs(kbId);

          res.json({
            ...baseResponse,
            ...generatePaginatedResponse(logs, maxResults, nextToken),
          });
          break;
        }

        case 'ListSessionHistoryMessage': {
          const sessionId = req.body.SessionId || Mock.mock('@guid');
          const nextToken = req.body.NextToken || '';
          const events = generateSessionHistoryMessages(sessionId);

          res.json({
            ...baseResponse,
            Message: '获取会话历史消息成功',
            NextToken: Mock.mock('@boolean') ? Mock.mock('@integer(10, 50)').toString() : '',
            Data: {
              Events: events,
            },
            Code: 200,
          });
          break;
        }

        case 'ListUserSessionArtifacts': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';
          const sessionId = req.body.SessionId || Mock.mock('@guid');
          const artifacts = generateUserSessionArtifacts(sessionId);
          const paginatedData = generatePaginatedResponse(artifacts, maxResults, nextToken);

          res.json({
            ...baseResponse,
            TotalCount: paginatedData.TotalCount,
            Message: '获取用户会话文件列表成功',
            NextToken: paginatedData.NextToken || '',
            MaxResults: maxResults,
            Data: paginatedData.Data,
            Code: '200',
          });
          break;
        }

        case 'ListUserSessions': {
          const maxResults = parseInt(req.body.MaxResults || '10');
          const nextToken = req.body.NextToken || '';

          res.json({
            ...baseResponse,
            Msg: '获取用户会话列表成功',
            Data: {
              Sessions: generateUserSessions(),
            },
            NextToken: nextToken,
            TotalCount: maxResults,
            Code: 200,
          });
          break;
        }

        default: {
          res.status(400).json({
            ...baseResponse,
            Code: 'InvalidAction',
            Message: `不支持的操作: ${action}`,
          });
        }
      }
    }, Mock.mock('@integer(200, 800)')); // 200-800ms 随机延迟
  },
};
