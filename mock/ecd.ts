import Mock from 'mockjs';

// 生成UTC格式的时间字符串
const generateUTCDateTime = () => {
  const randomTime = Mock.mock('@datetime');
  return new Date(randomTime).toISOString();
};

// 解析请求体中的Action参数
const parseAction = (body: any): string => {
  if (typeof body === 'string') {
    const params = new URLSearchParams(body);
    return params.get('Action') || '';
  }
  return body?.Action || '';
};

// 文件扩展名映射
const fileExtensions = {
  ppt: ['ppt', 'pptx'],
  doc: ['doc', 'docx'],
  pdf: ['pdf'],
  excel: ['xls', 'xlsx'],
  txt: ['txt'],
  html: ['html', 'htm'],
  image: ['jpg', 'jpeg', 'png', 'gif', 'bmp'],
};

// 随机获取文件扩展名
const getRandomExtension = () => {
  const types = Object.keys(fileExtensions);
  const randomType = types[Math.floor(Math.random() * types.length)];
  const extensions = fileExtensions[randomType as keyof typeof fileExtensions];
  return extensions[Math.floor(Math.random() * extensions.length)];
};

// 生成单个文件数据
const generateSingleFile = () => {
  const titles = [
    '生态环境保护是一个长期任务，要久久为功',
    '十班，十班，锐不可当，超越自我，再创辉煌',
    '时与势在我们一边',
    '所有的快跑都是短跑',
    '人人关心体育，体育造福人人',
    '感受荆风楚韵，享受省运激情',
    '鼓干劲，同富裕；小山沟，喜变样',
    '2024年度工作总结报告',
    '项目开发规范文档',
    '产品需求说明书',
    '技术架构设计方案',
    '用户体验优化建议',
    '市场调研分析报告',
    '团队建设与管理',
    '创新思维培训材料',
  ];

  const randomTitle = titles[Math.floor(Math.random() * titles.length)];
  const extension = getRandomExtension();
  const fileName = `${randomTitle}.${extension}`;

  // 30% 概率是 AI 生成的文件
  const isAiGenerated = Math.random() < 0.3;
  const tags = isAiGenerated ? [{
    TagCode: 'AI_GENERATED',
    TagDesc: 'AI生成',
    TagStyle: 'blue',
  }] : [];

  return {
    FileId: Mock.mock('@guid'),
    FileName: fileName,
    FilePath: `/oss/workspace/documents/${fileName}`,
    FileType: 'FILE',
    Size: Mock.mock('@integer(1024000, 10485760)'), // 1MB-10MB
    Status: 'NORMAL',
    FileFormatIconUrl: `/icons/file-${extension}.png`,
    GmtCreate: generateUTCDateTime(),
    GmtModified: generateUTCDateTime(),
    Tags: tags,
  };
};

// 生成文件数据列表
const generateFiles = (pageSize: number) => {
  return Array.from({ length: pageSize }, () => generateSingleFile());
};

export default {
  'POST /api/ecd/*': (req: any, res: any) => {
    const action = parseAction(req.body);
    const { LoginToken, PageSize = 20, PageNumber = 1 } = req.body;

    // 校验登录凭证
    if (!LoginToken) {
      return res.json({
        Code: 401,
        Message: '未授权访问',
        RequestId: Mock.mock('@guid'),
        Files: [],
        TotalCount: 0,
      });
    }

    switch (action) {
      case 'DescribeWyDriveFiles': {
        // 生成总数据量（模拟有100个文件）
        const totalCount = 100;
        const pageSize = Math.min(PageSize, 50); // 最大50条
        const pageNumber = PageNumber || 1;

        // 计算当前页的数据
        const startIndex = (pageNumber - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, totalCount);
        const currentPageSize = endIndex - startIndex;

        // 只生成当前页需要的数据量
        const files = currentPageSize > 0 ? generateFiles(currentPageSize) : [];

        return res.json({
          Code: 0,
          Message: 'Success',
          RequestId: Mock.mock('@guid'),
          Files: files,
          TotalCount: totalCount,
        });
      }

      default:
        return res.json({
          Code: 400,
          Message: `不支持的操作: ${action}`,
          RequestId: Mock.mock('@guid'),
        });
    }
  },
};
